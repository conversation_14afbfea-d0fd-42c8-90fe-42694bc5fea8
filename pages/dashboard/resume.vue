<template>
  <div class="min-h-screen bg-gradient-to-b from-indigo-50 to-purple-50 p-6">
    <!-- 页面标题 -->
    <h1 class="mb-6 bg-gradient-to-r from-purple-700 to-pink-600 bg-clip-text text-center text-2xl font-bold tracking-tight text-transparent">
      简历助手 Hi-Offer
    </h1>

    <!-- 标签页导航 -->
    <TabNavigation />

    <!-- 主体内容区域 -->
    <div class="mx-auto grid max-w-6xl grid-cols-1 gap-6 md:grid-cols-2">
      <!-- 左侧内容 -->
      <div class="rounded-lg border border-indigo-100/70 bg-white p-6 shadow-sm transition duration-300 hover:shadow-md">
        <!-- 简历优化 -->
        <template v-if="resumeStore.activeTab === 'optimize'">
          <OptimizeTabContent />
        </template>

        <!-- 面试题预测 -->
        <template v-if="resumeStore.activeTab === 'interviewer'">
          <IAMInterviewer />
        </template>

        <!-- 简历帮写 -->
        <template v-if="resumeStore.activeTab === 'write'">
          <ResumeHelpEdit />
        </template>
      </div>

      <!-- 右侧内容 -->
      <div class="rounded-lg border border-indigo-100/70 bg-white p-6 shadow-sm transition duration-300 hover:shadow-md">
        <template v-if="resumeStore.activeTab === 'optimize'">
          <!-- 根据当前状态显示不同占位区域内容 -->
          <ResultPlaceholder v-if="optimizationStore.status == 'pending'" :title="resultPlaceholder.title" :description="resultPlaceholder.description" />
          <ResumeLoadingIndicator v-else-if="optimizationStore.status == 'processing'" message="正在优化简历..." :showProgress="true" />
          <ResumeResult v-else :markdownText="markdownText" :originalContent="originalContent" />
        </template>

        <template v-if="resumeStore.activeTab === 'interviewer'">
          <!-- 根据当前状态显示不同占位区域内容 -->
          <ResultPlaceholder v-if="IAMInterviewerStore.status == 'pending'" :title="resultPlaceholder.title" :description="resultPlaceholder.description" />
          <ResumeLoadingIndicator v-else-if="IAMInterviewerStore.status == 'processing'" message="正在生成面试问题集..." :showProgress="true" />
          <ResumeResult v-else :markdownText="markdownText" :originalContent="originalContent" />
        </template>

        <!-- 简历帮写 -->
        <template v-if="resumeStore.activeTab === 'write'">
          <ResultPlaceholder v-if="resumeEditStore.status == 'pending'" :title="resultPlaceholder.title" :description="resultPlaceholder.description" />
          <ResumeLoadingIndicator v-else-if="resumeEditStore.status == 'processing'" message="正在编写简历内容..." :showProgress="true" />
          <ResumeResult v-else :markdownText="markdownText" :originalContent="originalContent" />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import OptimizeTabContent from '@/components/resume/OptimizeTabContent.vue'
import ResultPlaceholder from '@/components/resume/ResultPlaceholder.vue'
import ResumeResult from '@/components/resume/ResumeResult.vue'
import TabNavigation from '@/components/resume/TabNavigation.vue'
import IAMInterviewer from '@/components/resume/IAMInterviewer.vue'
import ResumeHelpEdit from '@/components/resume/ResumeHelpEdit.vue'
import ResumeLoadingIndicator from '@/components/resume/LoadingIndicator.vue'
import { useResumeStore, useOptimizationStore, useIAMInterviewerStore, useResumeEditStore } from '@/stores/resume'

definePageMeta({
  layout: 'dashboard',
  middleware: 'sidebase-auth',
})

const { useAutoCanonicalUrl } = useCanonicalUrl()
const { useAutoHreflang } = useHreflang()
useAutoCanonicalUrl()
useAutoHreflang()

useHead({
  title: '简历服务 - Hi-Offer',
  titleTemplate: '%s',
})

useSeoMeta({
  description: '使用Hi-offer AI简历服务，智能优化您的简历，提高求职成功率。包含简历优化、翻译、帮写等功能。',
  ogTitle: '简历服务 - Hi-Offer',
  ogDescription: '使用Hi-offer AI简历服务，智能优化您的简历，提高求职成功率。包含简历优化、翻译、帮写等功能。',
  robots: 'noindex, nofollow',
})

const resumeStore = useResumeStore()
const optimizationStore = useOptimizationStore()
const IAMInterviewerStore = useIAMInterviewerStore()
const resumeEditStore = useResumeEditStore()

const resultPlaceholder = computed(() => {
  if (resumeStore.activeTab == 'interviewer') {
    return { title: '面试官想要问的问题将显示在这里', description: '完成左侧表单并点击"开始提问"按钮' }
  }
  if (resumeStore.activeTab == 'write') {
    return { title: '编写结果将显示在这里', description: '完成左侧表单并点击"开始编写"按钮' }
  }
  return { title: '优化结果将显示在这里', description: '完成左侧表单并点击"开始优化"按钮' }
})

const originalContent = computed(() => {
  const activeTab = resumeStore.activeTab
  const optimizeInputType = optimizationStore.uiState.inputType
  if (activeTab == 'optimize' && optimizeInputType == InputType.FILE) {
    return optimizationStore.resumeInput.fileToMarkdownContent
  }
  if (activeTab == 'optimize' && optimizeInputType == InputType.TEXT) {
    return optimizationStore.resumeInput.content
  }
  if (activeTab == 'write') return resumeEditStore.resumeInput.content
  if (activeTab == 'interviewer') return IAMInterviewerStore.resumeInput.content
  return ''
})

const markdownText = computed(() => {
  const activeTab = resumeStore.activeTab
  if (activeTab == 'optimize') return optimizationStore.output.content
  if (activeTab == 'interviewer') return IAMInterviewerStore.output.content
  if (activeTab == 'write') return resumeEditStore.output.content
  return ''
})
</script>
