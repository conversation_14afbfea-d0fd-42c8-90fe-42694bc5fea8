<template>
  <div class="mx-auto max-w-6xl py-10">
    <h1 class="mb-4 text-center text-3xl font-bold text-purple-600">Offer 助手</h1>
    <div class="rounded-2xl border border-purple-100 bg-white shadow-lg">
      <!-- Tab区域 -->
      <div class="flex justify-center overflow-hidden rounded-t-2xl bg-white shadow-sm">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          class="tab-button relative flex-1 px-6 py-3 text-center text-base font-medium transition-all duration-500 ease-in-out focus:outline-none"
          :class="activeTab === tab.key ? 'bg-purple-50 text-purple-700' : 'bg-white text-gray-600 hover:bg-gray-50'"
          @click="changeTab(tab)"
        >
          <span>{{ tab.label }}</span>
          <div
            class="absolute bottom-0 left-1/2 h-0.5 -translate-x-1/2 transform transition-all duration-500 ease-in-out"
            :class="activeTab === tab.key ? 'w-20 bg-purple-600' : 'w-0 bg-transparent'"
          ></div>
        </button>
      </div>
      <!-- 内容区 -->
      <div class="p-0 md:p-0">
        <DeclineOfferSection v-if="activeTab === 'decline'" />
        <DelayOfferSection v-else-if="activeTab === 'delay'" />
        <SalaryNegotiationSection v-else-if="activeTab === 'salary'" />
        <AllSpeechTemplatesPanel :groups="allOptions" :title="currentTitle" class="mb-8" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DeclineOfferSection from '@/components/offer-management/DeclineOfferSection.vue'
import DelayOfferSection from '@/components/offer-management/DelayOfferSection.vue'
import SalaryNegotiationSection from '@/components/offer-management/SalaryNegotiationSection.vue'
import AllSpeechTemplatesPanel from '@/components/offer-management/AllSpeechTemplatesPanel.vue'
import { DECLINE_REASONS, DELAY_REASONS, SALARY_REASONS } from '@/stores/offer'

definePageMeta({
  layout: 'dashboard',
  middleware: 'sidebase-auth',
})

const { useAutoCanonicalUrl } = useCanonicalUrl()
const { useAutoHreflang } = useHreflang()
useAutoCanonicalUrl()
useAutoHreflang()

useHead({
  title: 'Offer助手 - Hi-Offer',
  titleTemplate: '%s',
})

useSeoMeta({
  description: 'Hi-offer Offer助手，帮助您处理工作邀请，包括拒绝Offer、延期回复和薪资谈判等专业建议。',
  ogTitle: 'Offer助手 - Hi-Offer',
  ogDescription: 'Hi-offer Offer助手，帮助您处理工作邀请，包括拒绝Offer、延期回复和薪资谈判等专业建议。',
  robots: 'noindex, nofollow',
})

const tabs: { key: string; label: string }[] = reactive([
  { key: 'decline', label: '拒绝 Offer' },
  { key: 'delay', label: '拖延 Offer' },
  { key: 'salary', label: '薪资谈判' },
])
const activeTab = ref('decline')

const changeTab = (tab: { key: string; label: string }) => {
  activeTab.value = tab.key
  console.log(activeTab.value, 'activeTab')
}

const allOptions = computed(() => {
  if (activeTab.value === 'decline') {
    return [{ group: '系统模板', options: DECLINE_REASONS }]
  } else if (activeTab.value === 'delay') {
    return [{ group: '系统话术', options: DELAY_REASONS }]
  } else if (activeTab.value === 'salary') {
    return [{ group: '系统话术', options: SALARY_REASONS }]
  }
  return []
})

const currentTitle = computed(() => {
  if (activeTab.value === 'decline') {
    return '查看所有拒绝offer模板'
  } else if (activeTab.value === 'delay') {
    return '查看所有拖延offer模板'
  } else if (activeTab.value === 'salary') {
    return '查看所有薪资谈判模板'
  }
  return '查看所有模板'
})
</script>
