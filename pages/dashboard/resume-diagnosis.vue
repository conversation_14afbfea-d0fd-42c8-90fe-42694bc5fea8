<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -right-40 -top-40 h-80 w-80 rounded-full bg-gradient-to-br from-purple-400/20 to-pink-400/20 blur-3xl"></div>
      <div class="absolute -bottom-40 -left-40 h-80 w-80 rounded-full bg-gradient-to-br from-blue-400/20 to-cyan-400/20 blur-3xl"></div>
    </div>

    <div class="relative z-10 p-6">
      <!-- 页面头部 -->
      <div class="mb-8 text-center">
        <div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-600 to-blue-600 shadow-lg">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <h1 class="bg-gradient-to-r from-gray-900 via-purple-800 to-blue-800 bg-clip-text text-3xl font-bold tracking-tight text-transparent">
          AI 简历诊断助手
        </h1>
        <p class="mt-3 text-lg text-gray-600">智能分析简历匹配度，生成专业优化建议</p>

        <!-- 特性标签 -->
        <div class="mt-6 flex flex-wrap justify-center gap-2">
          <span class="inline-flex items-center rounded-full bg-purple-100 px-3 py-1 text-xs font-medium text-purple-800">
            <svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              />
            </svg>
            智能匹配分析
          </span>
          <span class="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800">
            <svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              />
            </svg>
            专业优化建议
          </span>
          <span class="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800">
            <svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              />
            </svg>
            一键生成优化
          </span>
        </div>
      </div>

      <!-- 阶段进度指示器 -->
      <div class="mb-8">
        <DiagnosisStageProgress :current-stage="currentStage" :stage-one-completed="stageOneCompleted" :stage-two-completed="stageTwoCompleted" />
      </div>

      <!-- 主体内容区域 -->
      <div class="mx-auto max-w-7xl">
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-12">
          <!-- 左侧输入区域 -->
          <div class="lg:col-span-5">
            <div class="sticky top-6">
              <div class="overflow-hidden rounded-2xl border border-white/20 bg-white/80 shadow-xl backdrop-blur-sm">
                <div class="bg-gradient-to-r from-purple-600 to-blue-600 px-6 py-4">
                  <h2 class="flex items-center text-lg font-semibold text-white">
                    <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                    简历信息输入
                  </h2>
                </div>
                <div class="p-6">
                  <ResumeDiagnosisForm
                    :is-processing="isProcessing"
                    @submit="handleDiagnosisSubmit"
                    @generate-resume="handleGenerateResume"
                    :stage-one-completed="stageOneCompleted"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧结果区域 -->
          <div class="lg:col-span-7">
            <div class="overflow-hidden rounded-2xl border border-white/20 bg-white/80 shadow-xl backdrop-blur-sm">
              <div class="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
                <h2 class="flex items-center text-lg font-semibold text-white">
                  <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                  分析结果
                </h2>
              </div>
              <div class="p-6">
                <ResumeDiagnosisResult
                  :current-stage="currentStage"
                  :is-processing="isProcessing || isGeneratingResume || isStreamingResume"
                  :diagnosis-result="diagnosisResult"
                  :optimized-resume="optimizedResumeContent || optimizedResume"
                  :error-message="errorMessage"
                  :is-streaming="isStreamingResume"
                  :current-status-message="currentMessage"
                  @open-diagnosis-dialog="showDiagnosisDialog = true"
                  @retry="handleRetry"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 诊断报告Dialog -->
      <DiagnosisReportDialog
        :is-open="showDiagnosisDialog"
        :diagnosis-result="diagnosisResult"
        @close="showDiagnosisDialog = false"
        @generate-resume="handleGenerateResumeFromDialog"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
// import { useToast } from 'vue-toast-notification' // 暂时不使用
import { useResumeDiagnosis } from '@/composables/useResumeDiagnosis'
import { useDiagnosisStatusMessages } from '@/composables/useDiagnosisStatusMessages'
import ResumeDiagnosisForm from '@/components/resume/ResumeDiagnosisForm.vue'
import ResumeDiagnosisResult from '@/components/resume/ResumeDiagnosisResult.vue'
import DiagnosisStageProgress from '@/components/resume/DiagnosisStageProgress.vue'
import DiagnosisReportDialog from '@/components/resume/DiagnosisReportDialog.vue'

// const toast = useToast() // 暂时不使用
const {
  isLoading: isGeneratingResume,
  processedContent: optimizedResumeContent,
  isStreamProcessing: isStreamingResume,
  getDiagnosisResult,
  generateOptimizedResume,
} = useResumeDiagnosis()

const { currentMessage, startMessagesForStage, stopMessages } = useDiagnosisStatusMessages()

definePageMeta({
  layout: 'dashboard',
})

// SEO和页面设置
useHead({
  title: '简历诊断 - Hi-Offer',
  meta: [{ name: 'description', content: '使用AI智能分析您的简历，提供专业的优化建议' }],
})

// 状态管理
const currentStage = ref<'idle' | 'stage-one' | 'stage-two'>('idle')
const isProcessing = ref(false)
const stageOneCompleted = ref(false)
const stageTwoCompleted = ref(false)
const errorMessage = ref('')

// Dialog状态
const showDiagnosisDialog = ref(false)

// 诊断结果数据
const diagnosisResult = reactive({
  analyzer_sop: null as any,
  diagnosis_sop: null as any,
  suggestions_sop: null as any,
})

// 优化后的简历
const optimizedResume = ref('')

// 保存表单数据用于Dialog中的简历生成
const savedFormData = ref<{
  resumeText: string
  jobDescription: string
  model: 'standard' | 'professional'
} | null>(null)

// 处理诊断提交
const handleDiagnosisSubmit = async (formData: { resumeText: string; jobDescription: string; model: 'standard' | 'professional' }) => {
  try {
    isProcessing.value = true
    currentStage.value = 'stage-one'
    errorMessage.value = ''

    // 开始阶段一的动态消息
    startMessagesForStage('stage-one')

    // 保存表单数据
    savedFormData.value = { ...formData }

    // 重置之前的结果
    diagnosisResult.analyzer_sop = null
    diagnosisResult.diagnosis_sop = null
    diagnosisResult.suggestions_sop = null
    optimizedResume.value = ''
    stageOneCompleted.value = false
    stageTwoCompleted.value = false

    // 调用阶段一API - 使用新的composable方法
    const result = await getDiagnosisResult(formData)

    // 更新诊断结果
    Object.assign(diagnosisResult, result)
    stageOneCompleted.value = true
    // 自动弹出诊断报告Dialog
    showDiagnosisDialog.value = true
  } catch (error: any) {
    errorMessage.value = error.message || '诊断过程中发生错误'
    console.error('诊断失败:', error)
  } finally {
    isProcessing.value = false
    // 停止动态消息
    stopMessages()
  }
}

// 从Dialog中生成简历
const handleGenerateResumeFromDialog = async () => {
  if (savedFormData.value) {
    await handleGenerateResume(savedFormData.value)
  }
}

// 处理生成优化简历
const handleGenerateResume = async (formData: { resumeText: string; jobDescription: string; model: 'standard' | 'professional' }) => {
  try {
    currentStage.value = 'stage-two'
    errorMessage.value = ''

    // 开始阶段二的动态消息
    startMessagesForStage('stage-two')

    // 调用阶段二API - 使用流式输出
    const result = await generateOptimizedResume(formData, diagnosisResult)

    if (result.content) {
      optimizedResume.value = result.content
      stageTwoCompleted.value = true
    }
  } catch (error: any) {
    errorMessage.value = error.message || '简历生成过程中发生错误'
    console.error('简历生成失败:', error)
  } finally {
    // 停止动态消息
    stopMessages()
  }
}

// 重试功能
const handleRetry = () => {
  if (savedFormData.value) {
    if (currentStage.value === 'stage-one' || !stageOneCompleted.value) {
      handleDiagnosisSubmit(savedFormData.value)
    } else if (currentStage.value === 'stage-two') {
      handleGenerateResume(savedFormData.value)
    }
  }
}
</script>
