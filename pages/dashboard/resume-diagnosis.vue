<template>
  <div class="min-h-screen bg-gradient-to-b from-indigo-50 to-purple-50 p-6">
    <!-- 页面标题 -->
    <div class="mb-6 text-center">
      <h1 class="bg-gradient-to-r from-purple-700 to-pink-600 bg-clip-text text-2xl font-bold tracking-tight text-transparent">
        简历诊断助手
      </h1>
      <p class="mt-2 text-sm text-gray-600">AI 智能分析您的简历，提供专业的优化建议</p>
    </div>

    <!-- 阶段进度指示器 -->
    <DiagnosisStageProgress 
      :current-stage="currentStage" 
      :stage-one-completed="stageOneCompleted"
      :stage-two-completed="stageTwoCompleted"
      class="mb-6"
    />

    <!-- 主体内容区域 -->
    <div class="mx-auto grid max-w-6xl grid-cols-1 gap-6 md:grid-cols-2">
      <!-- 左侧输入区域 -->
      <div class="rounded-lg border border-indigo-100/70 bg-white p-6 shadow-sm transition duration-300 hover:shadow-md">
        <ResumeDiagnosisForm 
          :is-processing="isProcessing"
          @submit="handleDiagnosisSubmit"
          @generate-resume="handleGenerateResume"
          :stage-one-completed="stageOneCompleted"
        />
      </div>

      <!-- 右侧结果区域 -->
      <div class="rounded-lg border border-indigo-100/70 bg-white p-6 shadow-sm transition duration-300 hover:shadow-md">
        <ResumeDiagnosisResult 
          :current-stage="currentStage"
          :is-processing="isProcessing || isGeneratingResume || isStreamingResume"
          :diagnosis-result="diagnosisResult"
          :optimized-resume="optimizedResumeContent || optimizedResume"
          :error-message="errorMessage"
          :is-streaming="isStreamingResume"
          :current-status-message="currentMessage"
          @open-diagnosis-dialog="showDiagnosisDialog = true"
        />
      </div>
    </div>

    <!-- 诊断报告Dialog -->
    <DiagnosisReportDialog 
      :is-open="showDiagnosisDialog"
      :diagnosis-result="diagnosisResult"
      @close="showDiagnosisDialog = false"
      @generate-resume="handleGenerateResumeFromDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useToast } from 'vue-toast-notification'
import { useResumeDiagnosis } from '@/composables/useResumeDiagnosis'
import { useDiagnosisStatusMessages } from '@/composables/useDiagnosisStatusMessages'
import ResumeDiagnosisForm from '@/components/resume/ResumeDiagnosisForm.vue'
import ResumeDiagnosisResult from '@/components/resume/ResumeDiagnosisResult.vue'
import DiagnosisStageProgress from '@/components/resume/DiagnosisStageProgress.vue'
import DiagnosisReportDialog from '@/components/resume/DiagnosisReportDialog.vue'

const toast = useToast()
const {
  isLoading: isGeneratingResume,
  processedContent: optimizedResumeContent,
  isStreamProcessing: isStreamingResume,
  getDiagnosisResult,
  generateOptimizedResume
} = useResumeDiagnosis()

const {
  currentMessage,
  startMessagesForStage,
  stopMessages
} = useDiagnosisStatusMessages()

definePageMeta({
  layout: 'dashboard',
})

// SEO和页面设置
useHead({
  title: '简历诊断 - Hi-Offer',
  meta: [{ name: 'description', content: '使用AI智能分析您的简历，提供专业的优化建议' }],
})

// 状态管理
const currentStage = ref<'idle' | 'stage-one' | 'stage-two'>('idle')
const isProcessing = ref(false)
const stageOneCompleted = ref(false)
const stageTwoCompleted = ref(false)
const errorMessage = ref('')

// Dialog状态
const showDiagnosisDialog = ref(false)

// 诊断结果数据
const diagnosisResult = reactive({
  analyzer_sop: null as any,
  diagnosis_sop: null as any,
  suggestions_sop: null as any,
})

// 优化后的简历
const optimizedResume = ref('')

// 保存表单数据用于Dialog中的简历生成
const savedFormData = ref<{
  resumeText: string
  jobDescription: string
  model: 'standard' | 'professional'
} | null>(null)

// 处理诊断提交
const handleDiagnosisSubmit = async (formData: {
  resumeText: string
  jobDescription: string
  model: 'standard' | 'professional'
}) => {
  try {
    isProcessing.value = true
    currentStage.value = 'stage-one'
    errorMessage.value = ''
    
    // 开始阶段一的动态消息
    startMessagesForStage('stage-one')
    
    // 保存表单数据
    savedFormData.value = { ...formData }
    
    // 重置之前的结果
    diagnosisResult.analyzer_sop = null
    diagnosisResult.diagnosis_sop = null
    diagnosisResult.suggestions_sop = null
    optimizedResume.value = ''
    stageOneCompleted.value = false
    stageTwoCompleted.value = false

    // 调用阶段一API - 使用新的composable方法
    const result = await getDiagnosisResult(formData)
    
    // 更新诊断结果
    Object.assign(diagnosisResult, result)
    stageOneCompleted.value = true
    // 自动弹出诊断报告Dialog
    showDiagnosisDialog.value = true
  } catch (error: any) {
    errorMessage.value = error.message || '诊断过程中发生错误'
    console.error('诊断失败:', error)
  } finally {
    isProcessing.value = false
    // 停止动态消息
    stopMessages()
  }
}

// 从Dialog中生成简历
const handleGenerateResumeFromDialog = async () => {
  if (savedFormData.value) {
    await handleGenerateResume(savedFormData.value)
  }
}

// 处理生成优化简历
const handleGenerateResume = async (formData: {
  resumeText: string
  jobDescription: string
  model: 'standard' | 'professional'
}) => {
  try {
    currentStage.value = 'stage-two'
    errorMessage.value = ''

    // 开始阶段二的动态消息
    startMessagesForStage('stage-two')

    // 调用阶段二API - 使用流式输出
    const result = await generateOptimizedResume(formData, diagnosisResult)
    
    if (result.content) {
      optimizedResume.value = result.content
      stageTwoCompleted.value = true
    }
  } catch (error: any) {
    errorMessage.value = error.message || '简历生成过程中发生错误'
    console.error('简历生成失败:', error)
  } finally {
    // 停止动态消息
    stopMessages()
  }
}
</script>