<template>
  <div class="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <NuxtLink to="/">
        <Logo class="mx-auto h-10 w-auto" />
      </NuxtLink>
      <h2 class="mt-6 text-center text-2xl/9 font-bold tracking-tight text-gray-900">创建新账号</h2>
    </div>

    <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
      <div class="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
        <div class="space-y-6">
          <div>
            <label for="email" class="block text-sm/6 font-medium text-gray-900">邮箱</label>
            <div class="mt-2">
              <input
                v-model="form.email"
                type="email"
                id="email"
                autocomplete="email"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
              />
            </div>
          </div>

          <div>
            <label for="password" class="block text-sm/6 font-medium text-gray-900">密码</label>
            <div class="mt-2">
              <input
                v-model="form.password"
                type="password"
                id="password"
                autocomplete="new-password"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
              />
            </div>
          </div>

          <div>
            <label for="confirm-password" class="block text-sm/6 font-medium text-gray-900">确认密码</label>
            <div class="mt-2">
              <input
                v-model="form.confirmPassword"
                type="password"
                id="confirm-password"
                autocomplete="new-password"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
              />
            </div>
          </div>

          <div>
            <UiButton
              @click="handleSubmit"
              :disabled="isLoading"
              :loading="isLoading"
              variant="primary"
              size="md"
              full-width
            >
              {{ isLoading ? '注册中...' : '注册' }}
            </UiButton>
          </div>
        </div>

        <div class="mt-6 text-center text-sm/6 text-gray-500">
          已有账号？
          <NuxtLink to="/login" class="font-semibold text-indigo-600 hover:text-indigo-500">立即登录</NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Logo from '@/assets/icons/logo.svg'
import { useEventBus } from '@/composables/useEventBus'
import type { ApiResponse } from '~/types/index'
import { handleApiError } from '~/utils/error'
import { z } from 'zod'
import { apiFetch } from '~/utils/api'

const form = ref({
  email: '',
  password: '',
  confirmPassword: '',
})

const isLoading = ref(false)
const router = useRouter()
const eventBus = useEventBus()

const registerSchema = z
  .object({
    email: z.string().min(1, '请输入邮箱').email('请输入有效的邮箱地址'),
    password: z.string().min(1, '请输入密码').min(6, '密码长度至少为6位'),
    confirmPassword: z.string().min(1, '请确认密码'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: '两次输入的密码不一致',
    path: ['confirmPassword'],
  })

const validateForm = () => {
  try {
    registerSchema.parse(form.value)
    return true
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0]
      eventBus.emit('showToast', { message: firstError.message, type: 'warning' })
    }
    return false
  }
}

const handleSubmit = async () => {
  if (!validateForm()) return
  try {
    isLoading.value = true
    await apiFetch('/api/auth/register', {
      method: 'POST',
      body: {
        email: form.value.email,
        password: form.value.password,
      },
    })
    eventBus.emit('showToast', { message: '注册成功！', type: 'success' })
    router.push('/login')
  } catch (error) {
    console.error(error)
  } finally {
    isLoading.value = false
  }
}
</script>
