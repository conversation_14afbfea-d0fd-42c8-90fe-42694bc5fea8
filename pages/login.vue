<template>
  <div class="flex min-h-full flex-1 flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <NuxtLink to="/">
        <Logo class="mx-auto h-10 w-auto" />
      </NuxtLink>
      <h2 class="mt-6 text-center text-2xl/9 font-bold tracking-tight text-gray-900">登录您的账号</h2>
    </div>

    <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
      <div class="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
        <div class="space-y-5">
          <div>
            <label for="email" class="block text-sm/6 font-medium text-gray-900">邮箱</label>
            <div class="mt-2">
              <input
                v-model="form.email"
                type="email"
                id="email"
                autocomplete="email"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
              />
            </div>
          </div>

          <div>
            <label for="password" class="block text-sm/6 font-medium text-gray-900">密码</label>
            <div class="mt-2">
              <input
                v-model="form.password"
                type="password"
                id="password"
                autocomplete="current-password"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
              />
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex gap-3">
              <div class="flex h-6 shrink-0 items-center">
                <div class="group grid size-4 grid-cols-1">
                  <input
                    v-model="rememberPassword"
                    id="remember-me"
                    type="checkbox"
                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"
                  />
                  <svg
                    class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25"
                    viewBox="0 0 14 14"
                    fill="none"
                  >
                    <path
                      class="opacity-0 group-has-[:checked]:opacity-100"
                      d="M3 8L6 11L11 3.5"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      class="opacity-0 group-has-[:indeterminate]:opacity-100"
                      d="M3 7H11"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </div>
              <label for="remember-me" class="block text-sm/6 text-gray-900">记住密码</label>
            </div>

            <div class="flex gap-4 text-sm/6">
              <NuxtLink to="/register" class="font-semibold text-indigo-600 hover:text-indigo-500">没有账号？</NuxtLink>
            </div>
          </div>

          <div class="flex">
            <NuxtLink to="/forgot-password" class="text-xs text-gray-500 hover:text-gray-600">忘记密码</NuxtLink>
          </div>

          <div>
            <UiButton @click="handleSubmit" :disabled="isLoading" :loading="isLoading" variant="primary" size="md" full-width>
              {{ isLoading ? '登录中...' : '登录' }}
            </UiButton>
          </div>
        </div>

        <!-- <div>
          <div class="relative mt-10">
            <div class="absolute inset-0 flex items-center" aria-hidden="true">
              <div class="w-full border-t border-gray-200" />
            </div>
            <div class="relative flex justify-center text-sm/6 font-medium">
              <span class="bg-white px-6 text-gray-900">或继续使用</span>
            </div>
          </div>

          <div class="mt-6 grid grid-cols-1 gap-4">
            <button
              class="flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-transparent"
            >
              <Google class="size-5" />
              <span class="text-sm/6 font-semibold">Google</span>
            </button>

            <button
              @click="signInWithGithub"
              class="flex w-full items-center justify-center gap-3 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:ring-transparent"
            >
              <Github class="size-5 fill-[#24292F]" />
              <span class="text-sm/6 font-semibold">GitHub</span>
            </button>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Logo from '@/assets/icons/logo.svg'
import Github from '@/assets/icons/github.svg'
import { useEventBus } from '@/composables/useEventBus'
import { useCredentials } from '@/composables/useCredentials'
import type { ApiResponse } from '~/types/index'
import { ErrorCode } from '~/utils/error-code'
import { z } from 'zod'
import { apiFetch } from '~/utils/api'

const { saveCredentials, getCredentials, removeCredentials } = useCredentials()
const { signIn } = useAuth()
const router = useRouter()
const eventBus = useEventBus()

interface LoginForm {
  email: string
  password: string
}

const form = ref<LoginForm>({
  email: '',
  password: '',
})

const isLoading = ref(false)
const rememberPassword = ref(false)

onMounted(() => {
  const credentials = getCredentials()
  if (credentials) {
    form.value.email = credentials.email
    form.value.password = credentials.password
    rememberPassword.value = true
  }
})

const loginSchema = z.object({
  email: z.string().min(1, '请输入邮箱').email('请输入有效的邮箱地址'),
  password: z.string().min(1, '请输入密码'),
})

const validateForm = () => {
  try {
    loginSchema.parse(form.value)
    return true
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0]
      eventBus.emit('showToast', { message: firstError.message, type: 'warning' })
    }
    return false
  }
}

const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    isLoading.value = true
    await apiFetch('/api/auth/login', {
      method: 'POST',
      body: form.value,
    })
    rememberPassword.value ? saveCredentials(form.value.email, form.value.password) : removeCredentials()
    await signIn('credentials', {
      ...form.value,
      redirect: false,
      callbackUrl: '/dashboard',
    })
    router.push('/dashboard')
  } catch (error) {
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

const signInWithGithub = async () => {
  try {
    await signIn('github', { callbackUrl: '/dashboard' })
  } catch (error) {
    console.error(error)
    eventBus.emit('showToast', { message: 'GitHub 登录失败', type: 'error' })
  }
}

const { useAutoCanonicalUrl } = useCanonicalUrl()
const { useAutoHreflang } = useHreflang()
useAutoCanonicalUrl()
useAutoHreflang()

useHead({
  title: '登录 - Hi-Offer',
  titleTemplate: '%s',
})

useSeoMeta({
  description: '登录Hi-offer账号，开始您的职业发展之旅。享受简历优化、模拟面试等全方位职业服务。',
  ogTitle: '登录 - Hi-Offer',
  ogDescription: '登录Hi-offer账号，开始您的职业发展之旅。享受简历优化、模拟面试等全方位职业服务。',
  robots: 'noindex, nofollow',
})
</script>
