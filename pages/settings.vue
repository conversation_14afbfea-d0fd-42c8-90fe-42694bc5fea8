<template>
  <div class="relative min-h-screen overflow-hidden bg-gradient-to-br from-indigo-50 via-white to-purple-50">
    <!-- Animated background elements -->
    <div class="pointer-events-none absolute inset-0 overflow-hidden">
      <div class="animate-float absolute -right-40 -top-40 h-80 w-80 rounded-full bg-gradient-to-br from-indigo-400/20 to-purple-400/20 blur-3xl"></div>
      <div
        class="animate-float absolute -bottom-40 -left-40 h-80 w-80 rounded-full bg-gradient-to-br from-purple-400/20 to-pink-400/20 blur-3xl"
        style="animation-delay: 2s"
      ></div>
      <div
        class="animate-float absolute left-1/2 top-1/2 h-96 w-96 -translate-x-1/2 -translate-y-1/2 transform rounded-full bg-gradient-to-br from-indigo-300/10 to-purple-300/10 blur-3xl"
        style="animation-delay: 4s"
      ></div>
    </div>

    <div class="relative z-10 mx-auto max-w-6xl space-y-10 px-4 py-8 sm:px-6 lg:px-8">
      <!-- 页面标题 -->
      <div class="text-center transition-all duration-1000 ease-out" :class="isPageLoaded ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'">
        <h1
          class="animate-scaleIn bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-500 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl"
        >
          个人设置
        </h1>
        <p class="animate-fadeIn mx-auto mt-4 max-w-2xl text-lg text-gray-600" style="animation-delay: 0.2s">管理您的账号设置和偏好，打造专属的个人档案</p>
      </div>

      <div
        class="grid gap-6 transition-all duration-1000 ease-out lg:grid-cols-12 lg:gap-10"
        :class="isPageLoaded ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'"
        style="animation-delay: 0.4s"
      >
        <!-- 侧边导航 -->
        <nav class="lg:col-span-3">
          <div class="lg:sticky lg:top-8">
            <!-- Mobile horizontal scroll navigation -->
            <div class="mb-6 lg:hidden">
              <div class="scrollbar-hide flex gap-2 overflow-x-auto pb-2">
                <button
                  v-for="tab in tabs"
                  :key="tab.id"
                  @click="activeTab = tab.id"
                  :class="[
                    'group relative flex items-center gap-3 whitespace-nowrap rounded-2xl px-6 py-3 text-sm font-semibold transition-all duration-300',
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/25'
                      : 'bg-white/70 text-gray-700 backdrop-blur-sm hover:bg-white hover:text-indigo-600 hover:shadow-lg',
                  ]"
                >
                  <component :is="tab.icon" class="size-5" :class="activeTab === tab.id ? 'text-white' : 'text-gray-500 group-hover:text-indigo-600'" />
                  <span>{{ tab.name }}</span>
                </button>
              </div>
            </div>

            <!-- Desktop vertical navigation -->
            <ul role="list" class="hidden flex-col gap-2 lg:flex">
              <li v-for="tab in tabs" :key="tab.id">
                <button
                  @click="activeTab = tab.id"
                  :class="[
                    'group relative flex w-full items-center gap-4 overflow-hidden rounded-2xl px-6 py-4 text-sm font-semibold transition-all duration-300',
                    activeTab === tab.id
                      ? 'scale-[1.02] transform bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/25'
                      : 'bg-white/70 text-gray-700 backdrop-blur-sm hover:scale-[1.01] hover:bg-white hover:text-indigo-600 hover:shadow-lg hover:shadow-gray-200/50',
                  ]"
                >
                  <!-- Background gradient effect for active state -->
                  <div
                    v-if="activeTab === tab.id"
                    class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                  ></div>

                  <component
                    :is="tab.icon"
                    class="relative z-10 size-6"
                    :class="activeTab === tab.id ? 'text-white' : 'text-gray-500 group-hover:text-indigo-600'"
                  />
                  <span class="relative z-10">{{ tab.name }}</span>

                  <!-- Active indicator -->
                  <div v-if="activeTab === tab.id" class="absolute right-4 top-1/2 h-2 w-2 -translate-y-1/2 transform rounded-full bg-white"></div>
                </button>
              </li>
            </ul>
          </div>
        </nav>

        <!-- 主要内容区 -->
        <main class="space-y-10 lg:col-span-9">
          <!-- 个人信息卡片 -->
          <div
            class="group relative overflow-hidden rounded-3xl bg-white shadow-xl shadow-gray-200/20 ring-1 ring-gray-200/50 transition-all duration-500 hover:shadow-2xl hover:shadow-indigo-500/10 hover:ring-indigo-200/50"
          >
            <!-- Background gradient overlay -->
            <div
              class="absolute inset-0 bg-gradient-to-br from-indigo-50/50 via-transparent to-purple-50/30 opacity-0 transition-opacity duration-500 group-hover:opacity-100"
            ></div>

            <!-- Header with enhanced styling -->
            <div class="relative border-b border-gray-200/60 bg-gradient-to-r from-gray-50/50 to-indigo-50/30 px-8 py-8">
              <div class="flex items-center gap-4">
                <div class="flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-r from-indigo-600 to-purple-600 shadow-lg">
                  <UserIcon class="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 class="text-2xl font-bold tracking-tight text-gray-900">个人信息</h3>
                  <p class="mt-1 text-base text-gray-600">这些信息将显示在您的个人资料中，让其他人更好地了解您</p>
                </div>
              </div>
            </div>

            <form @submit.prevent="debouncedSubmit" class="relative p-10">
              <div class="grid gap-10">
                <!-- 头像上传 -->
                <div
                  class="group flex flex-col items-start gap-6 rounded-2xl bg-gradient-to-r from-gray-50/50 to-indigo-50/30 p-6 transition-all duration-300 hover:from-indigo-50/50 hover:to-purple-50/40 sm:flex-row sm:gap-8"
                >
                  <div class="mx-auto shrink-0 sm:mx-0">
                    <div class="relative">
                      <img
                        :src="form.image"
                        class="size-20 rounded-3xl bg-gray-50 object-cover shadow-xl ring-4 ring-white transition-all duration-300 group-hover:scale-105 group-hover:shadow-2xl group-hover:ring-indigo-200/50 sm:size-24"
                      />
                      <!-- Avatar overlay effect -->
                      <div
                        class="absolute inset-0 rounded-3xl bg-gradient-to-br from-indigo-500/20 to-purple-500/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      ></div>
                    </div>
                  </div>
                  <div class="flex-1 text-center sm:text-left">
                    <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                      <div>
                        <h4 class="mb-2 text-lg font-bold text-gray-900">个人头像</h4>
                        <p class="text-sm leading-relaxed text-gray-600">
                          推荐使用正方形图片，文件大小不超过 2MB
                          <span class="hidden sm:inline"><br />高质量头像有助于建立专业形象</span>
                        </p>
                      </div>
                      <UiButton type="button" @click="regenerateAvatar" variant="outline" size="sm" class="w-full shrink-0 sm:w-auto"> 随机生成 </UiButton>
                    </div>
                  </div>
                </div>

                <!-- 基本信息表单 -->
                <div class="grid gap-8">
                  <UiInput
                    v-model="form.name"
                    id="name"
                    label="昵称"
                    placeholder="请输入您的昵称"
                    variant="primary"
                    :maxlength="50"
                    :disabled="isLoading"
                    :show-count="true"
                    required
                  />

                  <UiTextarea
                    v-model="form.bio"
                    id="bio"
                    label="个人简介"
                    placeholder="介绍一下您自己，让其他人更好地了解您..."
                    variant="secondary"
                    :maxlength="200"
                    :disabled="isLoading"
                    :show-count="true"
                    :show-progress="true"
                    :rows="4"
                    description="高质量的个人简介有助于建立专业形象"
                  />
                </div>
              </div>

              <!-- 提交按钮 -->
              <div class="mt-12 flex justify-end">
                <div class="relative">
                  <UiButton
                    type="submit"
                    :disabled="isLoading"
                    :loading="isLoading"
                    variant="primary"
                    size="lg"
                    class="min-w-[140px] transition-all duration-300 hover:scale-105 active:scale-95"
                  >
                    {{ isLoading ? '保存中...' : '保存更改' }}
                  </UiButton>

                  <!-- Success animation overlay -->
                  <div v-if="showSuccessAnimation" class="animate-scaleIn absolute inset-0 flex items-center justify-center rounded-lg bg-green-500 text-white">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </main>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { UserIcon } from '@heroicons/vue/24/outline'
import { useEventBus } from '@/composables/useEventBus'
import { useAvatar } from '@/composables/useAvatar'
import type { ApiResponse } from '~/types/index'
import { useDebounceFn } from '@vueuse/core'

definePageMeta({
  layout: 'dashboard',
  middleware: 'sidebase-auth',
})

const tabs = [{ id: 'profile', name: '个人信息', icon: UserIcon }]

const activeTab = ref('profile')
const isLoading = ref(false)
const { generateNewAvatar } = useAvatar()
const userStore = useUserStore()
const { data: session } = useAuth()
const eventBus = useEventBus()

const form = ref({
  name: userStore.name || '',
  bio: userStore.bio || '',
  image: userStore.image || '',
})

const fetchUserData = async () => {
  if (session.value?.user) {
    try {
      await userStore.fetchProfile()
      form.value.name = userStore.name
      form.value.bio = userStore.bio
      form.value.image = userStore.image
      if (session.value.user) {
        session.value.user.name = userStore.name
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      eventBus.emit('showToast', { message: '获取用户信息失败', type: 'error' })
      form.value.name = session.value.user.name || ''
    }
  }
}

const regenerateAvatar = () => {
  const newAvatarUrl = generateNewAvatar()
  form.value.image = newAvatarUrl
}

const formRules = {
  name: { required: true, min: 2, max: 50 },
  bio: { max: 200 },
}

const validateForm = () => {
  if (!form.value.name) {
    eventBus.emit('showToast', { message: '请输入姓名', type: 'error' })
    return false
  }
  if (form.value.name.length < formRules.name.min) {
    eventBus.emit('showToast', { message: '姓名至少需要2个字符', type: 'error' })
    return false
  }
  return true
}

const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    isLoading.value = true
    const response = await $fetch<ApiResponse>('/api/user/settings', {
      method: 'POST',
      body: {
        name: form.value.name.trim(),
        bio: form.value.bio.trim(),
        image: form.value.image,
      },
    })

    if (response.success) {
      // Show success animation
      showSuccessAnimation.value = true
      setTimeout(() => {
        showSuccessAnimation.value = false
      }, 2000)

      eventBus.emit('showToast', { message: '保存设置成功', type: 'success' })
      if (session.value?.user) {
        session.value.user.name = response.data?.name || ''
      }
      await fetchUserData()
    }
  } catch (error: any) {
    const errorMessage = error.data?.message || '保存设置失败'
    console.error('保存设置失败:', errorMessage)
    eventBus.emit('showToast', {
      message: errorMessage,
      type: 'error',
    })
  } finally {
    isLoading.value = false
  }
}

const debouncedSubmit = useDebounceFn(handleSubmit, 300)

// Animation state for page entrance
const isPageLoaded = ref(false)
const showSuccessAnimation = ref(false)

onMounted(() => {
  setTimeout(() => {
    isPageLoaded.value = true
  }, 100)
})
</script>

<style scoped>
/* Enhanced animations for settings page */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer-effect {
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Floating animation for background elements */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(1deg);
  }
  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

/* Enhanced hover effects */
.group:hover .group-hover\:shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.group:hover .group-hover\:shimmer::before {
  left: 100%;
}

/* Pulse animation for interactive elements */
@keyframes pulse-subtle {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
</style>
