<script setup lang="ts">
import type { IInterview } from '~/server/models/interview.model'

type InterviewWithStringId = Omit<IInterview, '_id'> & { _id: string }

definePageMeta({
  middleware: 'sidebase-auth',
  layout: 'dashboard',
})

const { useAutoCanonicalUrl } = useCanonicalUrl()
const { useAutoHreflang } = useHreflang()
useAutoCanonicalUrl()
useAutoHreflang()

useHead({
  title: '面试记录 - Hi-Offer',
  titleTemplate: '%s',
})

useSeoMeta({
  description: '查看您的面试历史记录，回顾面试表现，分析评估结果，持续提升面试技能。',
  ogTitle: '面试记录 - Hi-Offer',
  ogDescription: '查看您的面试历史记录，回顾面试表现，分析评估结果，持续提升面试技能。',
  robots: 'noindex, nofollow',
})

interface InterviewResponse {
  success: boolean
  data: {
    list: InterviewWithStringId[]
    pagination: {
      page: number
      limit: number
      total: number
    }
  }
  message: string
}

const page = ref(1)
const limit = ref(5)

const {
  data: interviewsResponse,
  pending,
  refresh,
} = await useFetch<InterviewResponse>('/api/interview/list', {
  query: {
    page,
    limit,
  },
})

const interviews = computed(() => interviewsResponse.value?.data.list || [])
const pagination = computed(() => interviewsResponse.value?.data.pagination)

watch([page, limit], () => {
  refresh()
})

const loadMore = async () => {
  if (pagination.value && page.value * limit.value < pagination.value.total) {
    page.value++
  }
}

const formatDate = (date: string | Date) => {
  const dateObj = date instanceof Date ? date : new Date(date)
  return dateObj.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  return `${minutes} 分钟`
}

const getInterviewTypeText = (type: string) => {
  const types = {
    frontend: '前端开发',
    backend: '后端开发',
    fullstack: '全栈开发',
    algorithm: '算法',
  }
  return types[type as keyof typeof types] || type
}

const getPositionIcon = (type: string) => {
  switch (type) {
    case 'frontend':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />`
    case 'backend':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />`
    case 'fullstack':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />`
    case 'algorithm':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />`
    default:
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />`
  }
}

const getPositionIconColor = (type: string) => {
  switch (type) {
    case 'frontend':
      return 'text-rose-500 bg-rose-50'
    case 'backend':
      return 'text-emerald-500 bg-emerald-50'
    case 'fullstack':
      return 'text-violet-500 bg-violet-50'
    case 'algorithm':
      return 'text-amber-500 bg-amber-50'
    default:
      return 'text-indigo-500 bg-indigo-50'
  }
}

const navigateToDetail = (id: string) => {
  navigateTo({
    path: '/detail',
    query: { id },
  })
}
</script>

<template>
  <div class="container relative mx-auto min-h-[calc(100vh-4rem)] px-6 pb-24 pt-12">
    <!-- 标题区域 -->
    <div class="mb-12">
      <div class="flex items-center justify-between">
        <div class="space-y-1">
          <div class="flex items-center gap-3">
            <svg class="h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <h1 class="bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-4xl font-bold text-transparent">面试记录</h1>
          </div>
          <div class="flex items-center gap-2">
            <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p class="text-sm font-medium text-gray-500">管理和回顾您的所有面试记录</p>
          </div>
        </div>
        <NuxtLink
          to="/interview"
          class="group relative inline-flex items-center overflow-hidden rounded-xl bg-primary px-8 py-3 text-white shadow-lg transition-all duration-300 hover:bg-primary-600 hover:shadow-primary/30 active:scale-95"
        >
          <div class="relative z-10 flex items-center gap-2">
            <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            <span class="font-medium">开始新的面试</span>
            <svg
              class="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </div>
          <div
            class="absolute inset-0 -z-10 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
          ></div>
        </NuxtLink>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex h-[60vh] flex-col items-center justify-center gap-4">
      <div class="relative h-16 w-16">
        <div class="absolute h-full w-full animate-ping rounded-full border-2 border-primary opacity-20"></div>
        <div class="absolute h-full w-full animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
      <p class="text-sm font-medium text-gray-500">加载中...</p>
    </div>

    <!-- 空状态 -->
    <div v-else-if="interviews.length === 0" class="flex h-[480px] flex-col items-center justify-center rounded-xl bg-gray-50/80">
      <div class="flex flex-col items-center space-y-4">
        <div class="mb-2 text-gray-300">
          <svg class="h-32 w-32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 7C8 5.34315 9.34315 4 11 4H13C14.6569 4 16 5.34315 16 7V9H8V7Z" stroke="currentColor" stroke-width="1.5" />
            <path
              d="M3 12C3 10.3431 4.34315 9 6 9H18C19.6569 9 21 10.3431 21 12V18C21 19.6569 19.6569 21 18 21H6C4.34315 21 3 19.6569 3 18V12Z"
              stroke="currentColor"
              stroke-width="1.5"
            />
            <path d="M7 13H17M7 17H13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
            <path d="M16 16H16.002V16.002H16V16Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M19 16H19.002V16.002H19V16Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </div>
        <div class="text-center">
          <p class="mb-2 text-lg font-medium text-gray-900">暂无面试记录</p>
          <p class="text-sm text-gray-500">开始你的第一次模拟面试，积累面试经验</p>
        </div>
        <NuxtLink
          to="/interview"
          class="group mt-4 inline-flex items-center gap-2 rounded-xl bg-primary px-6 py-3 text-white shadow-lg transition-all duration-300 hover:bg-primary-600 hover:shadow-primary/20 active:scale-95"
        >
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          <span class="font-medium">开始第一次面试</span>
          <svg
            class="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </NuxtLink>
      </div>
    </div>

    <!-- 面试卡片列表 -->
    <div v-else class="space-y-6">
      <div class="grid gap-6">
        <div
          v-for="interview in interviews"
          :key="interview._id"
          class="group relative overflow-hidden rounded-2xl bg-white p-6 shadow-sm ring-1 ring-gray-100 transition-all duration-300 hover:-translate-y-1 hover:shadow-xl hover:ring-primary/20"
        >
          <div class="mb-6 flex items-start justify-between">
            <div class="space-y-4">
              <div class="flex items-center gap-3">
                <div :class="['flex h-8 w-8 items-center justify-center rounded-lg', getPositionIconColor(interview.type)]">
                  <svg
                    class="h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    v-html="getPositionIcon(interview.type)"
                  ></svg>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800">{{ getInterviewTypeText(interview.type) }}面试</h3>
                  <div class="flex items-center gap-2">
                    <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                      />
                    </svg>
                    <span class="text-xs font-medium text-gray-500">ID: {{ interview._id.slice(-6) }}</span>
                  </div>
                </div>
              </div>
              <div class="flex items-center gap-6">
                <div class="flex items-center gap-2">
                  <div class="rounded-full bg-blue-50 p-1">
                    <svg class="h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                  <p class="text-sm text-gray-600">{{ interview.interviewer }}</p>
                </div>
                <div class="flex items-center gap-2">
                  <div class="rounded-full bg-green-50 p-1">
                    <svg class="h-4 w-4 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p class="text-sm text-gray-600">{{ formatDuration(interview.duration) }}</p>
                </div>
                <div class="flex items-center gap-2">
                  <div class="rounded-full bg-purple-50 p-1">
                    <svg class="h-4 w-4 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <p class="text-sm text-gray-600">{{ formatDate(interview.createdAt) }}</p>
                </div>
              </div>
            </div>

            <!-- 分数显示 -->
            <div
              class="relative flex h-16 w-16 items-center justify-center rounded-2xl text-lg font-bold transition-transform duration-300 group-hover:scale-110"
              :class="[
                interview.evaluation?.overallScore >= 80
                  ? 'bg-green-50 text-green-600'
                  : interview.evaluation?.overallScore >= 60
                    ? 'bg-blue-50 text-blue-600'
                    : 'bg-red-50 text-red-600',
              ]"
            >
              <svg class="absolute -right-1 -top-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
              {{ interview.evaluation?.overallScore || 0 }}
            </div>
          </div>

          <!-- 卡片底部 -->
          <div class="flex items-center justify-between border-t border-gray-100 pt-4">
            <div class="flex items-center gap-4">
              <div class="flex items-center gap-1">
                <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                  />
                </svg>
                <span class="text-sm text-gray-500">{{ interview.messages?.length || 0 }} 个问题</span>
              </div>
              <div class="flex items-center gap-1">
                <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <span class="text-sm text-gray-500">查看记录</span>
              </div>
            </div>
            <UiButton @click="navigateToDetail(interview._id)" variant="ghost" size="sm">
              查看详情
              <svg class="ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </UiButton>
          </div>
        </div>
      </div>

      <!-- 分页控件 -->
      <div v-if="pagination" class="fixed bottom-0 left-0 right-0 backdrop-blur-md lg:left-72 lg:w-[calc(100%-18rem)]">
        <div class="container mx-auto border-t border-gray-100 bg-white/80 px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
              <p class="text-sm text-gray-600">
                显示第
                <span class="font-medium text-gray-900">{{ (page - 1) * limit + 1 }}</span>
                至
                <span class="font-medium text-gray-900">{{ Math.min(page * limit, pagination.total) }}</span>
                条，共
                <span class="font-medium text-gray-900">{{ pagination.total }}</span>
                条记录
              </p>
            </div>

            <div class="flex items-center gap-2">
              <!-- 上一页按钮 -->
              <UiButton :disabled="page === 1" @click="page--" variant="outline" size="sm">
                <svg class="mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
                上一页
              </UiButton>

              <!-- 页码按钮 -->
              <div class="flex items-center gap-1">
                <UiButton
                  v-for="p in Math.min(5, Math.ceil(pagination.total / limit))"
                  :key="p"
                  :variant="page === p ? 'primary' : 'ghost'"
                  size="sm"
                  @click="page = p"
                  class="!h-9 !w-9 !px-0"
                >
                  {{ p }}
                </UiButton>
              </div>

              <!-- 下一页按钮 -->
              <UiButton :disabled="page * limit >= pagination.total" @click="loadMore" variant="outline" size="sm">
                下一页
                <svg class="ml-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </UiButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
