<template>
  <div class="h-auto min-h-screen bg-gradient-to-b from-indigo-50 to-purple-50 p-6">
    <!-- Progress Steps -->
    <StepProgress :currentStep="prepareInterviewStore.currentStep" class="mb-6 px-6" />

    <!-- Main Content -->
    <div class="mx-auto max-w-5xl px-6">
      <div v-if="prepareInterviewStore.currentStep === PrepareInterviewStep.POSITION_INFO">
        <!-- Title Section -->
        <div class="mb-8 text-center">
          <h2 class="mb-2 text-2xl font-bold text-gray-800">准备面试什么岗位呢?</h2>
          <p class="text-base text-gray-500">了解您的求职岗位，提高AI回答的针对性</p>
        </div>

        <!-- Main Grid -->
        <div class="mb-10 grid grid-cols-1 gap-6 md:grid-cols-[320px_1fr]">
          <!-- Left Sidebar - Position Selection -->
          <div class="flex h-[500px] flex-col overflow-hidden rounded-2xl bg-white shadow-md transition-all duration-300 hover:shadow-lg">
            <div class="relative mb-2 bg-gradient-to-r from-purple-100/80 via-pink-100/70 to-purple-50/60 p-4">
              <div class="absolute inset-0 bg-white/40 backdrop-blur-sm"></div>
              <div class="relative flex items-center space-x-2">
                <div class="flex h-7 w-7 items-center justify-center rounded-full bg-purple-100/80 shadow-sm shadow-purple-200/50">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-purple-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h3 class="relative font-medium text-gray-600">您的岗位</h3>
                  <p class="relative mt-0.5 text-xs font-medium text-gray-400">岗位示例</p>
                </div>
              </div>
            </div>
            <div class="custom-scrollbar flex-grow space-y-2 overflow-y-auto p-3">
              <UiSelectionButton
                v-for="position in prepareInterviewStore.positions"
                :key="position.id"
                @click="prepareInterviewStore.selectPosition(position.key)"
                :selected="prepareInterviewStore.formData.selectedPositionKey === position.key"
                size="md"
                variant="card"
                full-width
              >
                {{ position.name }}
              </UiSelectionButton>
            </div>
            <div class="relative h-8 bg-gradient-to-r from-purple-50/30 to-pink-50/30">
              <div class="absolute bottom-0 h-[1px] w-full bg-gradient-to-r from-transparent via-purple-200/50 to-transparent"></div>
              <div class="absolute bottom-4 left-1/2 h-1 w-10 -translate-x-1/2 rounded-full bg-purple-100/80"></div>
            </div>
          </div>

          <!-- Right Content - Form -->
          <div class="space-y-4">
            <!-- Position Name Input -->
            <div class="group rounded-2xl bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
              <input
                v-model="prepareInterviewStore.formData.positionName"
                placeholder="请输入岗位名称"
                class="w-full rounded-xl border border-gray-200 px-4 py-2 text-base outline-none transition-all duration-200 focus:border-purple-400 focus:ring-purple-300 focus:ring-offset-0 group-hover:border-purple-200"
              />
            </div>
            <!-- Position Description -->
            <div class="group rounded-2xl bg-white p-5 shadow-sm transition-all duration-300 hover:shadow-md">
              <div class="relative">
                <textarea
                  v-model="prepareInterviewStore.formData.positionDescription"
                  @input="prepareInterviewStore.updatePositionDescriptionCount"
                  placeholder="请输入岗位描述，AI 会根据具体的岗位要求，工作职责，生成面试押题、规划模拟面试流程、AI生成面试回答。"
                  class="min-h-40 w-full resize-none rounded-xl border border-gray-200 p-4 text-base outline-none transition-all duration-200 focus:border-purple-400 focus:ring-purple-300 focus:ring-offset-0 group-hover:border-purple-200"
                ></textarea>
                <div class="absolute bottom-3 right-3 text-xs text-gray-400">{{ prepareInterviewStore.characterCounts.positionDescription }} / 2000</div>
              </div>
            </div>

            <!-- Company Description -->
            <div class="group rounded-2xl bg-white p-5 shadow-sm transition-all duration-300 hover:shadow-md">
              <div class="relative">
                <textarea
                  v-model="prepareInterviewStore.formData.companyDescription"
                  @input="prepareInterviewStore.updateCompanyDescriptionCount"
                  placeholder="(可选)请输入公司简介，当面试官提及公司业务情况时，AI会根据描述，生成恰当的回答。"
                  class="min-h-32 w-full resize-none rounded-xl border border-gray-200 p-4 text-base outline-none transition-all duration-200 focus:border-purple-400 focus:ring-purple-300 focus:ring-offset-0 group-hover:border-purple-200"
                ></textarea>
                <div class="absolute bottom-3 right-3 text-xs text-gray-400">{{ prepareInterviewStore.characterCounts.companyDescription }} / 2000</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="prepareInterviewStore.currentStep === PrepareInterviewStep.RESUME_SELECTION">
        <!-- 选择简历内容 -->
        <div class="mb-8 text-center">
          <h2 class="mb-2 text-2xl font-bold text-gray-800">想用哪份简历?</h2>
          <p class="text-base text-gray-500">了解您的求职简历，提高AI回答的针对性</p>
        </div>

        <!-- Main Content for Resume Step -->
        <div class="mb-10">
          <!-- Upload Area -->
          <div
            class="relative mb-6 rounded-xl border-2 border-dashed border-purple-200 bg-gradient-to-br from-white via-purple-50/30 to-purple-50/40 p-8 text-center transition-all duration-300 hover:border-purple-300 hover:shadow-sm hover:shadow-purple-100"
            @dragover.prevent="handleDragOver"
            @dragleave.prevent="handleDragLeave"
            @drop.prevent="handleDrop"
            :class="{ 'border-purple-400 bg-purple-50/50': isDragging }"
          >
            <input
              type="file"
              ref="fileInput"
              class="absolute inset-0 h-full w-full cursor-pointer opacity-0"
              accept=".pdf,.doc,.docx"
              @change="handleFileSelect"
            />

            <!-- 上传进度条 -->
            <div v-if="isUploading" class="absolute inset-0 z-10 flex items-center justify-center bg-white/95">
              <div class="w-full max-w-md px-4">
                <div class="mb-2 flex items-center justify-between">
                  <span class="text-sm font-medium text-purple-600">正在上传...</span>
                  <span class="text-sm text-gray-500">{{ uploadProgress }}%</span>
                </div>
                <div class="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                  <div
                    class="h-full rounded-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300"
                    :style="{ width: `${uploadProgress}%` }"
                  ></div>
                </div>
              </div>
            </div>

            <!-- 文件占位显示 -->
            <div v-if="uploadedFile" class="flex flex-col items-center">
              <div class="mb-4 flex h-14 w-14 items-center justify-center rounded-full bg-purple-100">
                <DocumentIcon class="h-8 w-8 text-purple-600" />
              </div>
              <div class="text-center">
                <h3 class="text-base font-medium text-gray-800">{{ uploadedFile.name }}</h3>
                <p class="mt-1 text-sm text-gray-500">{{ formatFileSize(uploadedFile.size) }}</p>
              </div>
              <div class="mt-4 flex space-x-3">
                <UiButton @click="fileInput?.click()" variant="outline" size="sm" :icon="UploadButtonIcon"> 更换文件 </UiButton>
                <UiButton @click="removeFile" variant="secondary" size="sm" :icon="RemoveFileIcon"> 移除文件 </UiButton>
              </div>
            </div>

            <!-- 默认上传提示 -->
            <div v-else>
              <div
                class="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-r from-purple-100 to-purple-100 text-purple-500 shadow-inner shadow-purple-100/50 transition-all duration-300 hover:from-purple-200 hover:to-purple-200 hover:text-purple-600 hover:shadow-purple-200/50"
              >
                <FileUploadIcon class="h-9 w-9" />
              </div>
              <p class="mt-4 text-sm font-medium text-purple-600">拖放简历到此处上传</p>
              <p class="mt-1 text-xs text-purple-500/80">或点击选择文件上传</p>
              <p v-if="uploadError" class="mt-2 text-sm text-red-500">{{ uploadError }}</p>
            </div>
          </div>

          <!-- Text Input Area -->
          <div class="rounded-xl">
            <div class="relative">
              <textarea
                v-model="prepareInterviewStore.formData.resumeContent"
                @input="prepareInterviewStore.updateResumeContentCount"
                placeholder="您还可以直接粘贴您的简历文本到这里，特别是遇到文件解析失败的时候。"
                class="min-h-[250px] w-full resize-none rounded-lg border border-purple-200 bg-gradient-to-br from-white via-purple-50/10 to-purple-50/20 p-6 text-base text-gray-600 outline-none transition-all duration-200 hover:shadow-sm hover:shadow-purple-100/50 focus:border-purple-400 focus:ring-purple-300 focus:ring-offset-0"
              ></textarea>
              <div class="absolute bottom-6 right-6 text-xs text-purple-400">{{ prepareInterviewStore.characterCounts.resumeContent }} / 3000</div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="prepareInterviewStore.currentStep === PrepareInterviewStep.PREPARATION_COMPLETE">
        <PreInterviewGuide @start-interview="handleStartInterview" @go-back="prepareInterviewStore.prevStep" />
      </div>

      <!-- Footer Buttons -->
      <div v-if="prepareInterviewStore.currentStep < PrepareInterviewStep.PREPARATION_COMPLETE" class="flex justify-center gap-4 pb-8">
        <UiButton
          v-if="prepareInterviewStore.currentStep > PrepareInterviewStep.POSITION_INFO"
          @click="prepareInterviewStore.prevStep"
          variant="outline"
          size="md"
        >
          返回上一步
        </UiButton>
        <UiButton v-else @click="goToHome" variant="outline" size="md"> 返回首页 </UiButton>
        <UiButton @click="prepareInterviewStore.nextStep" variant="primary" size="md"> 下一步 </UiButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePrepareInterviewStore, PrepareInterviewStep } from '~/stores/prepare-interview'
import PreInterviewGuide from '~/components/interview-prep/PreInterviewGuide.vue'
import FileUploadIcon from '@/assets/icons/file-upload-icon.svg'
import DocumentIcon from '@/assets/icons/document-icon.svg'
import UploadButtonIcon from '@/assets/icons/upload-button-icon.svg'
import RemoveFileIcon from '@/assets/icons/remove-file-icon.svg'

// API 响应类型定义
interface ParseResponse {
  success: boolean
  data: {
    markdown: string
    message: string
  }
}

const router = useRouter()
const prepareInterviewStore = usePrepareInterviewStore()
const fileInput = ref<HTMLInputElement | null>(null)
const isDragging = ref(false)
const isUploading = ref(false)
const uploadProgress = ref(0)
const uploadError = ref('')
const uploadedFile = ref<File | null>(null)

// 返回首页
const goToHome = () => {
  router.push('/')
}

// 处理开始面试
const handleStartInterview = async (settings: any) => {
  Object.assign(prepareInterviewStore.formData, settings)

  //  暂时先不注入 面试题，还没想好流程怎么设计
  const { resumeContent: text, positionDescription, companyDescription } = prepareInterviewStore.formData
  // const jobInfo = `${positionDescription} ${companyDescription}`
  // const apiParams = { role: 'profession', text, jobInfo, questionCountObj: { questionGroupCount: '5', questionPerGroup: '3' } }
  // const response = (await $fetch('/api/resume/generate-questions', {
  //   method: 'POST',
  //   body: apiParams,
  //   responseType: 'stream',
  // })) as ReadableStream

  // if (!response) {
  //   throw new Error('模型调用失败')
  // }
  // const reader = response.getReader()
  // const decoder = new TextDecoder()
  // let result = ''
  // while (true) {
  //   const { done, value } = await reader.read()
  //   if (done) break
  //   const chunk = decoder.decode(value, { stream: true })
  //   result += chunk
  // }
  // prepareInterviewStore.formData.majorQuestions = result
  // console.log(prepareInterviewStore.formData.majorQuestions, 'majorQuestions')

  //  to do 一个加载的动画
  router.push('/interview')
}

// 处理拖拽进入
const handleDragOver = () => {
  isDragging.value = true
}

// 处理拖拽离开
const handleDragLeave = () => {
  isDragging.value = false
}

// 处理文件选择
const handleFileSelect = async (event: Event) => {
  const input = event.target as HTMLInputElement
  if (input.files && input.files.length > 0) {
    await processFile(input.files[0])
  }
}

// 处理文件拖放
const handleDrop = async (event: DragEvent) => {
  isDragging.value = false
  if (event.dataTransfer && event.dataTransfer.files.length > 0) {
    await processFile(event.dataTransfer.files[0])
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// 模拟上传进度
const simulateUploadProgress = () => {
  return new Promise<() => void>(resolve => {
    const interval = setInterval(() => {
      if (uploadProgress.value < 95) {
        uploadProgress.value = Math.floor(uploadProgress.value + Math.random() * 10)
        if (uploadProgress.value > 95) {
          uploadProgress.value = 95
        }
      }
    }, 1000)

    // 返回清理函数，允许外部控制进度条
    resolve(() => {
      clearInterval(interval)
    })
  })
}

// 处理文件上传和解析
const processFile = async (file: File) => {
  try {
    isUploading.value = true
    uploadProgress.value = 0
    uploadError.value = ''
    uploadedFile.value = file
    if (!['application/pdf'].includes(file.type)) {
      throw new Error('请上传 PDF 简历')
    }
    if (file.size > 5 * 1024 * 1024) {
      throw new Error('文件大小不能超过 5MB')
    }

    // 开始模拟上传进度，获取清理函数
    const clearProgress = await simulateUploadProgress()

    // 创建 FormData
    const formData = new FormData()
    formData.append('file', file)

    // 调用解析接口
    const res = await useFetch<ParseResponse>('/api/file/file-to-md', {
      method: 'POST',
      body: formData,
    })

    // 接口完成后立即清理进度条并更新状态
    clearProgress()
    uploadProgress.value = 100

    if (!res.data.value?.success || !res.data.value.data.markdown) {
      throw new Error('文件解析失败')
    }
    prepareInterviewStore.formData.resumeContent = res.data.value.data.markdown
    prepareInterviewStore.updateResumeContentCount()
  } catch (error: any) {
    uploadError.value = error.message || '上传失败，请重试'
    uploadedFile.value = null
  } finally {
    isUploading.value = false
  }
}

// 移除文件
const removeFile = () => {
  uploadedFile.value = null
  uploadProgress.value = 0
  prepareInterviewStore.formData.resumeContent = ''
  prepareInterviewStore.updateResumeContentCount()
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

onMounted(() => {
  const selectedPositionId = prepareInterviewStore.formData.selectedPositionKey
  if (selectedPositionId) {
    prepareInterviewStore.selectPosition(selectedPositionId)
  }
})
</script>

<style scoped>
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background-color: rgba(243, 244, 246, 0.2);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgba(192, 132, 252, 0.3), rgba(244, 114, 182, 0.3), rgba(192, 132, 252, 0.3));
  border-radius: 10px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgba(192, 132, 252, 0.5), rgba(244, 114, 182, 0.5), rgba(192, 132, 252, 0.5));
}

@keyframes softPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(168, 85, 247, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(168, 85, 247, 0);
  }
}
</style>
