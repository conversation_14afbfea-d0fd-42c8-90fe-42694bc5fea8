<template>
  <main class="grid min-h-screen place-items-center bg-white px-6 py-24 sm:py-32 lg:px-8">
    <div class="text-center">
      <p class="text-base font-semibold text-indigo-600">404</p>
      <h1 class="mt-4 text-balance text-5xl font-semibold tracking-tight text-gray-900 sm:text-7xl">页面未找到</h1>
      <p class="mt-6 text-pretty text-lg font-medium text-gray-500 sm:text-xl/8">抱歉，我们无法找到您要访问的页面。</p>
      <div class="mt-10 flex items-center justify-center gap-x-6">
        <NuxtLink
          to="/"
          class="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          返回首页
        </NuxtLink>
      </div>
    </div>
  </main>
</template>

<script setup lang="ts">
const { useAutoCanonicalUrl } = useCanonicalUrl()
const { useAutoHreflang } = useHreflang()
useAutoCanonicalUrl()
useAutoHreflang()

useHead({
  title: '页面未找到 - Hi-Offer',
  titleTemplate: '%s',
})

useSeoMeta({
  description: '抱歉，您访问的页面不存在。请返回Hi-offer首页继续您的职业发展之旅。',
  ogTitle: '页面未找到 - Hi-Offer',
  ogDescription: '抱歉，您访问的页面不存在。请返回Hi-offer首页继续您的职业发展之旅。',
  robots: 'noindex, nofollow',
})
</script>
