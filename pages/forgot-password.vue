<template>
  <div class="flex min-h-full flex-1 flex-col justify-center py-8 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <NuxtLink to="/">
        <Logo class="mx-auto h-8 w-auto" />
      </NuxtLink>
      <h2 class="mt-4 text-center text-xl font-semibold tracking-tight text-gray-900">重置密码</h2>
      <p class="mt-1 text-center text-sm text-gray-500">请输入您的邮箱，我们将向您发送验证码</p>
    </div>

    <div class="mt-6 sm:mx-auto sm:w-full sm:max-w-[480px]">
      <div class="bg-white px-4 py-8 shadow sm:rounded-lg sm:px-10">
        <div class="space-y-4">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">邮箱</label>
            <div class="mt-1">
              <input
                v-model="form.email"
                type="email"
                id="email"
                autocomplete="email"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600"
              />
            </div>
          </div>

          <div>
            <label for="code" class="block text-sm font-medium text-gray-700">验证码</label>
            <div class="mt-1 flex gap-2">
              <input
                v-model="form.code"
                type="text"
                id="code"
                maxlength="6"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600"
              />
              <button
                @click="sendVerificationCode"
                :disabled="isCodeSending || countdown > 0"
                class="shrink-0 rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-medium text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
              </button>
            </div>
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">新密码</label>
            <div class="mt-1">
              <input
                v-model="form.password"
                type="password"
                id="password"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600"
              />
            </div>
          </div>

          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700">确认密码</label>
            <div class="mt-1">
              <input
                v-model="form.confirmPassword"
                type="password"
                id="confirmPassword"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600"
              />
            </div>
          </div>

          <div class="pt-2">
            <button
              @click="handleSubmit"
              :disabled="isLoading"
              class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-medium text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {{ isLoading ? '处理中...' : '重置密码' }}
            </button>
          </div>

          <div class="pt-1 text-center">
            <NuxtLink to="/login" class="text-sm font-medium text-indigo-600 hover:text-indigo-500">返回登录</NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Logo from '@/assets/icons/logo.svg'
import { useEventBus } from '@/composables/useEventBus'
import { z } from 'zod'
import { apiFetch } from '~/utils/api'

const router = useRouter()
const eventBus = useEventBus()

interface ResetPasswordForm {
  email: string
  code: string
  password: string
  confirmPassword: string
}

const form = ref<ResetPasswordForm>({
  email: '',
  code: '',
  password: '',
  confirmPassword: '',
})

const isLoading = ref(false)
const isCodeSending = ref(false)
const countdown = ref(0)

// 表单验证
const emailSchema = z.object({
  email: z.string().min(1, '请输入邮箱').email('请输入有效的邮箱地址'),
})

const codeSchema = z.object({
  code: z.string().length(6, '验证码必须是6位数字'),
})

const passwordSchema = z
  .object({
    password: z.string().min(6, '密码长度不能少于6位'),
    confirmPassword: z.string().min(6, '密码长度不能少于6位'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: '两次输入的密码不一致',
    path: ['confirmPassword'],
  })

// 发送验证码
const sendVerificationCode = async () => {
  try {
    const result = emailSchema.safeParse({ email: form.value.email })
    if (!result.success) {
      const firstError = result.error.errors[0]
      eventBus.emit('showToast', { message: firstError.message, type: 'warning' })
      return
    }

    isCodeSending.value = true
    await apiFetch('/api/sms/email', {
      method: 'POST',
      body: { email: form.value.email },
    })
    eventBus.emit('showToast', { message: '验证码已发送，请查收邮件', type: 'success' })
    startCountdown()
  } catch (error) {
    console.error(error)
    eventBus.emit('showToast', { message: '发送验证码失败，请稍后重试', type: 'error' })
  } finally {
    isCodeSending.value = false
  }
}

// 倒计时
const startCountdown = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 处理表单提交
const handleSubmit = async () => {
  try {
    // 验证邮箱
    const emailResult = emailSchema.safeParse({ email: form.value.email })
    if (!emailResult.success) {
      const firstError = emailResult.error.errors[0]
      eventBus.emit('showToast', { message: firstError.message, type: 'warning' })
      return
    }

    // 验证验证码
    const codeResult = codeSchema.safeParse({ code: form.value.code })
    if (!codeResult.success) {
      const firstError = codeResult.error.errors[0]
      eventBus.emit('showToast', { message: firstError.message, type: 'warning' })
      return
    }

    // 验证密码
    const passwordResult = passwordSchema.safeParse({
      password: form.value.password,
      confirmPassword: form.value.confirmPassword,
    })
    if (!passwordResult.success) {
      const firstError = passwordResult.error.errors[0]
      eventBus.emit('showToast', { message: firstError.message, type: 'warning' })
      return
    }

    isLoading.value = true
    // 直接调用重置密码接口
    await apiFetch('/api/user/reset-password', {
      method: 'POST',
      body: {
        email: form.value.email,
        code: form.value.code,
        password: form.value.password,
      },
    })
    eventBus.emit('showToast', { message: '密码重置成功，请重新登录', type: 'success' })
    router.push('/login')
  } catch (error) {
    eventBus.emit('showToast', { message: '密码重置失败，请稍后重试', type: 'error' })
  } finally {
    isLoading.value = false
  }
}

const { useAutoCanonicalUrl } = useCanonicalUrl()
useAutoCanonicalUrl()

useHead({
  title: '忘记密码 - Hi-Offer',
  titleTemplate: '%s',
})

useSeoMeta({
  description: '重置您的Hi-offer账号密码，通过邮箱验证码安全找回账号。',
  ogTitle: '忘记密码 - Hi-Offer',
  ogDescription: '重置您的Hi-offer账号密码，通过邮箱验证码安全找回账号。',
  robots: 'noindex, nofollow',
})
</script>
