<script setup lang="ts">
import type { IInterview } from '~/server/models/interview.model'
import { useAvatar } from '@/composables/useAvatar'

definePageMeta({
  middleware: 'sidebase-auth',
  layout: 'dashboard',
})
const userStore = useUserStore()

type InterviewWithStringId = Omit<IInterview, '_id'> & { _id: string }

interface InterviewResponse {
  success: boolean
  data: InterviewWithStringId
  message: string
}

const route = useRoute()
const {
  data: interviewResponse,
  pending,
  error,
} = await useFetch<InterviewResponse>('/api/interview/detail', {
  query: {
    id: route.query.id,
  },
  key: route.query.id as string,
})

const interview = computed(() => interviewResponse.value?.data)

// 如果获取数据失败或没有 ID 参数，重定向到列表页
watchEffect(() => {
  if (error.value || !route.query.id) {
    navigateTo('/interviews')
  }
})

const formatDate = (date: string | Date) => {
  const dateObj = date instanceof Date ? date : new Date(date)
  return dateObj.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 判断两个时间是否需要显示时间分割线（间隔超过5分钟）
const shouldShowTimestamp = (current: string | Date, previous?: string | Date) => {
  if (!previous) return true
  const currentDate = new Date(current)
  const previousDate = new Date(previous)
  return currentDate.getTime() - previousDate.getTime() > 5 * 60 * 1000
}

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  return `${minutes} 分钟`
}

const getInterviewTypeText = (type: string) => {
  const types = {
    frontend: '前端开发',
    backend: '后端开发',
    fullstack: '全栈开发',
    algorithm: '算法',
  }
  return types[type as keyof typeof types] || type
}

const getPositionIcon = (type: string) => {
  switch (type) {
    case 'frontend':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />`
    case 'backend':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />`
    case 'fullstack':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />`
    case 'algorithm':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />`
    default:
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />`
  }
}

const getPositionIconColor = (type: string) => {
  switch (type) {
    case 'frontend':
      return 'text-rose-500 bg-rose-50'
    case 'backend':
      return 'text-emerald-500 bg-emerald-50'
    case 'fullstack':
      return 'text-violet-500 bg-violet-50'
    case 'algorithm':
      return 'text-amber-500 bg-amber-50'
    default:
      return 'text-indigo-500 bg-indigo-50'
  }
}

const goBack = () => {
  navigateTo('/interviews')
}

const { setCanonicalUrlForPath } = useCanonicalUrl()
const { setHreflangForPath } = useHreflang()

watchEffect(() => {
  if (route.query.id) {
    const path = `/detail?id=${route.query.id}`
    setCanonicalUrlForPath(path)
    setHreflangForPath(path)
  }
})

useHead({
  title: computed(() => {
    if (interview.value) {
      const typeText = getInterviewTypeText(interview.value.type)
      return `${typeText}面试详情 - Hi-Offer`
    }
    return '面试详情 - Hi-Offer'
  }),
  titleTemplate: '%s',
})

useSeoMeta({
  description: computed(() => {
    if (interview.value) {
      const typeText = getInterviewTypeText(interview.value.type)
      const score = interview.value.evaluation?.overallScore || 0
      return `查看您的${typeText}面试详情，总分${score}分。包含技术维度、软技能和职业发展评估。`
    }
    return '查看您的面试详情，包含完整的面试评估和对话记录。'
  }),
  ogTitle: computed(() => {
    if (interview.value) {
      const typeText = getInterviewTypeText(interview.value.type)
      return `${typeText}面试详情 - Hi-Offer`
    }
    return '面试详情 - Hi-Offer'
  }),
  ogDescription: computed(() => {
    if (interview.value) {
      const typeText = getInterviewTypeText(interview.value.type)
      const score = interview.value.evaluation?.overallScore || 0
      return `查看您的${typeText}面试详情，总分${score}分。包含技术维度、软技能和职业发展评估。`
    }
    return '查看您的面试详情，包含完整的面试评估和对话记录。'
  }),
  robots: 'noindex, nofollow',
})
</script>

<template>
  <div class="container mx-auto h-full px-4 pt-4">
    <button @click="goBack" class="group mb-1 flex items-center gap-2 rounded-lg px-4 py-2 text-gray-600 transition-all hover:bg-gray-100">
      <svg
        class="h-5 w-5 transition-transform group-hover:-translate-x-1"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
      </svg>
      返回列表
    </button>

    <div v-if="pending" class="flex h-[calc(100vh-10rem)] items-center justify-center">
      <div class="relative h-16 w-16">
        <div class="absolute h-full w-full animate-ping rounded-full border-2 border-primary opacity-20"></div>
        <div class="absolute h-full w-full animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
      </div>
    </div>

    <template v-else-if="interview">
      <div class="grid h-[calc(100vh-10rem)] grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- 左侧：面试信息和评价 -->
        <div class="flex h-full flex-col overflow-hidden rounded-2xl bg-white shadow-lg">
          <div class="flex-shrink-0 border-b border-gray-100 p-6">
            <div class="flex items-center gap-2">
              <svg class="h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h2 class="text-lg font-semibold">面试详情</h2>
            </div>
          </div>
          <div class="scrollbar flex-1 overflow-y-auto">
            <!-- 基本信息卡片 -->
            <div class="space-y-6 p-6">
              <div class="overflow-hidden rounded-2xl bg-gradient-to-br from-gray-50 to-gray-100">
                <div class="border-b border-gray-100/50 p-6">
                  <div class="mb-6 flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <div :class="['flex h-14 w-14 items-center justify-center rounded-2xl shadow-lg backdrop-blur-sm', getPositionIconColor(interview.type)]">
                        <svg
                          class="h-7 w-7"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          v-html="getPositionIcon(interview.type)"
                        ></svg>
                      </div>
                      <div>
                        <h1 class="bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-2xl font-bold text-transparent">
                          {{ getInterviewTypeText(interview.type) }}面试
                        </h1>
                        <p class="text-sm text-gray-500">面试官：{{ interview.interviewer }}</p>
                      </div>
                    </div>
                    <div class="flex flex-col items-end">
                      <div
                        class="mb-2 flex items-center gap-2 rounded-full px-6 py-2 text-lg font-semibold shadow-sm transition-all"
                        :class="[
                          interview.evaluation?.overallScore >= 80
                            ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-green-200'
                            : interview.evaluation?.overallScore >= 60
                              ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-blue-200'
                              : 'bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-red-200',
                        ]"
                      >
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        {{ interview.evaluation?.overallScore || 0 }} 分
                      </div>
                      <div class="text-sm text-gray-500">{{ formatDate(interview.date) }}</div>
                    </div>
                  </div>

                  <div class="grid grid-cols-2 gap-4">
                    <div class="group relative overflow-hidden rounded-xl bg-white p-4 shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-lg">
                      <div
                        class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent opacity-0 transition-all duration-300 ease-out group-hover:opacity-30"
                      ></div>
                      <div class="mb-1 flex items-center gap-2 text-sm text-gray-500">
                        <svg class="h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        面试时长
                      </div>
                      <div class="text-xl font-semibold text-gray-900">{{ formatDuration(interview.duration) }}</div>
                    </div>
                    <div class="group relative overflow-hidden rounded-xl bg-white p-4 shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-lg">
                      <div
                        class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-transparent opacity-0 transition-all duration-300 ease-out group-hover:opacity-30"
                      ></div>
                      <div class="mb-1 flex items-center gap-2 text-sm text-gray-500">
                        <svg class="h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                          />
                        </svg>
                        问题数量
                      </div>
                      <div class="text-xl font-semibold text-gray-900">{{ interview.messages?.length || 0 }} 个</div>
                    </div>
                  </div>
                </div>

                <!-- 技术维度评估 -->
                <div class="border-b border-gray-100/50 p-6">
                  <div class="mb-4 flex items-center gap-2">
                    <div class="rounded-lg bg-primary/10 p-2">
                      <svg class="h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <h2 class="text-lg font-semibold">技术维度评估</h2>
                  </div>
                  <div class="grid grid-cols-2 gap-4">
                    <div
                      v-for="(score, key) in interview.evaluation?.technicalDimension"
                      :key="key"
                      class="group relative overflow-hidden rounded-xl bg-white p-4 shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-lg"
                    >
                      <div
                        class="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-transparent opacity-0 transition-all duration-300 ease-out group-hover:opacity-100"
                      ></div>
                      <div class="relative mb-2 text-sm font-medium text-gray-700">
                        {{
                          key === 'basicKnowledge'
                            ? '基础知识'
                            : key === 'practicalAbility'
                              ? '实践能力'
                              : key === 'problemSolving'
                                ? '问题解决'
                                : key === 'technicalVision'
                                  ? '技术视野'
                                  : key === 'comments'
                                    ? '评语'
                                    : key
                        }}
                      </div>
                      <div v-if="key !== 'comments'" class="relative flex items-baseline gap-1">
                        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-3xl font-bold text-transparent">{{ score }}</span>
                        <span class="text-sm text-gray-500">分</span>
                      </div>
                      <div v-else class="relative text-sm text-gray-600">{{ score }}</div>
                    </div>
                  </div>
                </div>

                <!-- 软技能评估 -->
                <div class="border-b border-gray-100/50 p-6">
                  <div class="mb-4 flex items-center gap-2">
                    <div class="rounded-lg bg-primary/10 p-2">
                      <svg class="h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                        />
                      </svg>
                    </div>
                    <h2 class="text-lg font-semibold">软技能评估</h2>
                  </div>
                  <div class="grid grid-cols-2 gap-4">
                    <div
                      v-for="(score, key) in interview.evaluation?.softSkillDimension"
                      :key="key"
                      class="group relative overflow-hidden rounded-xl bg-white p-4 shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-lg"
                    >
                      <div
                        class="absolute inset-0 bg-gradient-to-br from-purple-50/30 via-transparent to-transparent opacity-0 transition-all duration-300 ease-out group-hover:opacity-100"
                      ></div>
                      <div class="relative mb-2 text-sm font-medium text-gray-700">
                        {{
                          key === 'communication'
                            ? '沟通能力'
                            : key === 'learning'
                              ? '学习能力'
                              : key === 'teamwork'
                                ? '团队协作'
                                : key === 'pressure'
                                  ? '抗压能力'
                                  : key === 'comments'
                                    ? '评语'
                                    : key
                        }}
                      </div>
                      <div v-if="key !== 'comments'" class="relative flex items-baseline gap-1">
                        <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-3xl font-bold text-transparent">{{ score }}</span>
                        <span class="text-sm text-gray-500">分</span>
                      </div>
                      <div v-else class="relative text-sm text-gray-600">{{ score }}</div>
                    </div>
                  </div>
                </div>

                <!-- 职业发展评估 -->
                <div class="border-b border-gray-100/50 p-6">
                  <div class="mb-4 flex items-center gap-2">
                    <div class="rounded-lg bg-primary/10 p-2">
                      <svg class="h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                    </div>
                    <h2 class="text-lg font-semibold">职业发展评估</h2>
                  </div>
                  <div class="grid grid-cols-2 gap-4">
                    <div
                      v-for="(score, key) in interview.evaluation?.careerDimension"
                      :key="key"
                      class="group relative overflow-hidden rounded-xl bg-white p-4 shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-lg"
                    >
                      <div
                        class="absolute inset-0 bg-gradient-to-br from-amber-50/30 via-transparent to-transparent opacity-0 transition-all duration-300 ease-out group-hover:opacity-100"
                      ></div>
                      <div class="relative mb-2 text-sm font-medium text-gray-700">
                        {{
                          key === 'careerPlanning'
                            ? '职业规划'
                            : key === 'growthPotential'
                              ? '成长潜力'
                              : key === 'jobMatch'
                                ? '岗位匹配度'
                                : key === 'comments'
                                  ? '评语'
                                  : key
                        }}
                      </div>
                      <div v-if="key !== 'comments'" class="relative flex items-baseline gap-1">
                        <span class="bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-3xl font-bold text-transparent">{{ score }}</span>
                        <span class="text-sm text-gray-500">分</span>
                      </div>
                      <div v-else class="relative text-sm text-gray-600">{{ score }}</div>
                    </div>
                  </div>
                </div>

                <!-- 总结评价 -->
                <div class="p-6">
                  <div class="mb-4 flex items-center gap-2">
                    <div class="rounded-lg bg-primary/10 p-2">
                      <svg class="h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <h2 class="text-lg font-semibold">总结评价</h2>
                  </div>
                  <div class="group relative overflow-hidden rounded-xl bg-white p-6 shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-lg">
                    <div
                      class="absolute inset-0 bg-gradient-to-br from-gray-50/30 via-transparent to-transparent opacity-0 transition-all duration-300 ease-out group-hover:opacity-100"
                    ></div>
                    <p class="relative mb-6 text-gray-700">{{ interview.evaluation?.overallSummary }}</p>
                    <div class="relative space-y-3">
                      <h3 class="font-medium text-gray-900">建议</h3>
                      <ul class="space-y-3">
                        <li
                          v-for="suggestion in interview.evaluation?.suggestions"
                          :key="suggestion"
                          class="flex items-start gap-3 rounded-lg bg-gray-50/50 p-3"
                        >
                          <svg
                            class="mt-0.5 h-5 w-5 flex-shrink-0 text-primary"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                          </svg>
                          <span class="text-gray-600">{{ suggestion }}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：对话记录 -->
        <div class="flex h-full flex-col overflow-hidden rounded-2xl bg-white shadow-lg">
          <div class="flex-shrink-0 border-b border-gray-100 p-6">
            <div class="flex items-center gap-2">
              <svg class="h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                />
              </svg>
              <h2 class="text-lg font-semibold">面试对话记录</h2>
            </div>
          </div>
          <div class="scrollbar flex-1 overflow-y-auto px-6 pb-6">
            <div class="space-y-6">
              <template v-for="(message, index) in interview.messages" :key="index">
                <!-- 时间分割线 -->
                <div
                  v-if="shouldShowTimestamp(message.timestamp, index > 0 ? interview.messages[index - 1].timestamp : undefined)"
                  class="my-4 flex items-center justify-center"
                >
                  <div class="rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-500">
                    {{ formatDate(message.timestamp) }}
                  </div>
                </div>

                <!-- 消息内容 -->
                <div class="flex gap-4" :class="message.role === 'assistant' ? 'flex-row' : 'flex-row-reverse'">
                  <div class="flex-shrink-0">
                    <template v-if="message.role === 'assistant'">
                      <div class="flex h-10 w-10 items-center justify-center rounded-full bg-primary text-white">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                    </template>
                    <template v-else>
                      <img :src="userStore.image" class="h-10 w-10 rounded-full object-cover" alt="用户头像" />
                    </template>
                  </div>
                  <div class="group relative flex max-w-[80%] flex-col rounded-2xl p-4" :class="[message.role === 'assistant' ? 'bg-gray-50' : 'bg-primary/5']">
                    <div class="break-all text-gray-700">{{ message.content }}</div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
