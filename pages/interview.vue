<template>
  <div class="flex h-[100vh] flex-col bg-gray-200/40 text-gray-900">
    <!-- 顶部导航 -->
    <header class="flex items-center justify-between border-b border-gray-200 bg-white px-6 py-3 shadow-sm">
      <div class="flex items-center gap-2">
        <Logo class="h-8 w-8" />
        <h1 class="bg-gradient-to-r from-purple-700 to-pink-600 bg-clip-text text-center text-xl font-bold tracking-tight text-transparent">Hi-Offer</h1>
      </div>
      <div class="flex flex-1 justify-center">
        <div class="flex items-center rounded-full bg-gray-100 px-6 py-1">
          <TimerIcon class="mr-2 h-4 w-4 text-gray-500" />
          <span class="font-mono font-medium">{{ interviewTime }}</span>
        </div>
      </div>
      <div class="flex items-center gap-4">
        <UiButton @click="showSettingsModal = true" variant="outline" size="sm" :icon="SettingsIcon"> 面试设置 </UiButton>
        <UiButton @click="showEndInterviewModal = true" variant="info" size="sm"> 结束面试 </UiButton>
      </div>
    </header>
    <!-- 主要内容 -->
    <div class="scrollbar grid flex-1 grid-cols-9 gap-4 overflow-y-auto p-4">
      <!-- 左侧面试官区域 -->
      <div class="col-span-3 flex flex-col overflow-hidden rounded-lg bg-white">
        <div class="flex items-center gap-3 border-b border-gray-200 p-4">
          <div class="flex h-6 w-6 items-center justify-center rounded-full bg-amber-500/10">
            <ChatBubbleIcon class="h-4 w-4 text-amber-500" />
          </div>
          <h2 class="font-medium">面试官</h2>
        </div>
        <div class="scrollbar flex-1 overflow-y-auto p-4">
          <div class="mb-6 flex flex-col items-center">
            <div class="mb-3 h-32 w-32 overflow-hidden rounded-full border-2 border-gray-100 shadow-md">
              <img :src="interviewer.avatarUrl" alt="面试官" class="h-full w-full object-cover" />
            </div>
            <h3 class="text-lg font-medium">{{ interviewer.name }}</h3>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoadingQuestionText || isSynthesizingAudio" class="flex h-32 items-center justify-center">
            <div class="flex flex-col items-center">
              <div class="mb-2 h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
              <p class="text-sm text-gray-500">正在思考问题...</p>
            </div>
          </div>

          <!-- 空状态 (Initial state before first question is loaded and not loading text/audio) -->
          <div
            v-else-if="!revealedQuestionContent && !isLoadingQuestionText && !isSynthesizingAudio && !isPlaybackBlockedByInteraction"
            class="flex h-32 flex-col items-center justify-center text-center"
          >
            <p class="text-sm text-gray-400">面试即将开始</p>
            <p class="text-xs text-gray-400">请稍等片刻，面试官正在准备问题</p>
          </div>

          <!-- 问题内容 和 手动播放按钮 -->
          <div v-else class="space-y-3">
            <div class="whitespace-pre-wrap leading-relaxed text-gray-700">
              <span
                v-for="(segment, index) in parseMarkedTextToSegments(displayQuestionContent)"
                :key="index"
                :class="{ 'font-medium': segment.isKeyword, [interviewerKeywordColor]: segment.isKeyword }"
              >
                {{ segment.text }}
              </span>
            </div>

            <!-- 手动播放音频按钮 -->
            <div v-if="isPlaybackBlockedByInteraction" class="pt-2 text-center">
              <p class="mb-2 text-sm text-orange-600">音频自动播放失败，请点击下方按钮允许自动播放语音。</p>
              <UiButton @click="triggerManualAudioPlayback" variant="info" size="sm" :icon="PlayIcon"> 允许自动播放 </UiButton>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间分析区域 -->
      <div class="col-span-4 flex flex-col gap-4 overflow-hidden">
        <!-- 分析 -->
        <div class="flex flex-col gap-2 rounded-md border-b border-gray-200 bg-white p-4">
          <div class="flex h-8 items-center gap-4">
            <i class="flex h-6 w-6 items-center justify-center rounded-full bg-purple-500/10">
              <ChartBarIcon class="h-4 w-4 text-purple-500" />
            </i>
            <h2 class="font-medium">分析</h2>
          </div>
          <div class="scrollbar h-full max-h-20 overflow-y-auto rounded-lg border border-purple-100 bg-purple-50 p-4 text-sm">
            <!-- 加载状态 for Analysis -->
            <div v-if="isLoadingAnalysis && !isAnalysisStreaming && !liveAnalysisContent" class="flex h-12 items-center justify-center">
              <div class="flex flex-col items-center">
                <div class="mb-1 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-purple-600"></div>
                <p class="text-xs text-gray-500">分析中...</p>
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-else-if="!isLoadingAnalysis && !isAnalysisStreaming && !analysis.content && !liveAnalysisContent"
              class="flex h-12 flex-col items-center justify-center text-center"
            >
              <p class="text-xs text-gray-400">等待分析内容</p>
            </div>

            <!-- 分析内容 -->
            <p v-else-if="liveAnalysisContent || analysis.content" class="whitespace-pre-wrap text-gray-700">
              <span
                v-for="(segment, index) in parseMarkedTextToSegments(isAnalysisStreaming && liveAnalysisContent ? liveAnalysisContent : analysis.content)"
                :key="index"
                :class="{ 'font-medium': segment.isKeyword, [analysisKeywordColor]: segment.isKeyword }"
              >
                {{ segment.text }}
              </span>
            </p>
          </div>
        </div>

        <!-- AI 回答建议 -->
        <div class="flex min-h-0 flex-1 flex-col gap-2 rounded-md bg-white p-4">
          <div class="flex h-8 justify-between gap-4 border-b border-gray-200">
            <div class="flex h-full gap-4">
              <i class="flex h-6 w-6 items-center justify-center rounded-full bg-green-500/10">
                <SuggestionIcon class="h-4 w-4 text-green-500" />
              </i>
              <h2 class="font-medium">AI 回答</h2>
            </div>
            <Popover :offset="8">
              <template #default="{ isShow }">
                <button class="flex h-6 w-6 items-center justify-center rounded-full hover:bg-gray-100">
                  <MoreIcon class="h-5 w-5 text-gray-500" />
                </button>
              </template>
              <template #content="{ close }">
                <div class="w-48 p-2">
                  <div class="flex items-center justify-between px-2 py-1.5">
                    <span
                      class="w-full rounded-md px-2 py-1 text-sm text-gray-700 hover:cursor-pointer hover:bg-gray-100"
                      @click="handleSuggestionSwitch(close)"
                      >{{ suggestionSwitchTips }}</span
                    >
                    <!-- <span class="w-full bg-red-200 py-1 text-sm text-gray-700 hover:cursor-pointer" @click="showSuggestion = !showSuggestion">重新生成</span> -->
                  </div>
                </div>
              </template>
            </Popover>
          </div>
          <div
            class="scrollbar relative min-h-0 flex-1 overflow-y-auto p-2 text-[14px] text-gray-700"
            :class="showSuggestion ? 'overflow-y-auto' : 'overflow-y-hidden'"
          >
            <div
              v-if="!showSuggestion"
              class="absolute inset-0 z-20 cursor-pointer"
              style="background: rgba(255, 255, 255, 0.65); backdrop-filter: blur(6px); pointer-events: auto"
            ></div>

            <!-- 加载状态 for Suggestion -->
            <div v-if="isLoadingSuggestion && !isSuggestionStreaming && !liveSuggestionContent" class="flex h-32 items-center justify-center">
              <div class="flex flex-col items-center">
                <div class="mb-2 h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-green-600"></div>
                <p class="text-sm text-gray-500">生成建议中...</p>
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-else-if="
                !isLoadingSuggestion && !isSuggestionStreaming && (!aiSuggestion.content || aiSuggestion.content.length === 0) && !liveSuggestionContent
              "
              class="flex h-32 flex-col items-center justify-center text-center"
            >
              <p class="text-sm text-gray-400">等待AI建议</p>
              <p class="text-xs text-gray-400">回答问题后将在此显示AI建议</p>
            </div>

            <!-- 建议内容 -->
            <template v-else>
              <!-- If actively streaming, show liveSuggestionContent as a single block -->
              <p v-if="isSuggestionStreaming && liveSuggestionContent" class="whitespace-pre-wrap">
                <span
                  v-for="(segment, sIndex) in parseMarkedTextToSegments(liveSuggestionContent)"
                  :key="`live-${sIndex}`"
                  :class="{ 'font-medium': segment.isKeyword, [aiSuggestionKeywordColor]: segment.isKeyword }"
                >
                  {{ segment.text }}
                </span>
              </p>
              <!-- Else, if not streaming, show the final (paragraph) content -->
              <template v-else-if="aiSuggestion.content && aiSuggestion.content.length > 0">
                <p v-for="(paragraph, pIndex) in aiSuggestion.content" :key="pIndex" class="mb-2 last:mb-0">
                  <span
                    v-for="(segment, sIndex) in parseMarkedTextToSegments(paragraph)"
                    :key="`${pIndex}-${sIndex}`"
                    :class="{ 'font-medium': segment.isKeyword, [aiSuggestionKeywordColor]: segment.isKeyword }"
                  >
                    {{ segment.text }}
                  </span>
                </p>
              </template>
              <!-- Fallback: if not streaming, final content is empty, but live content had something -->
              <p v-else-if="liveSuggestionContent && liveSuggestionContent.trim()" class="whitespace-pre-wrap">
                <span
                  v-for="(segment, sIndex) in parseMarkedTextToSegments(liveSuggestionContent)"
                  :key="`live-fb-${sIndex}`"
                  :class="{ 'font-medium': segment.isKeyword, [aiSuggestionKeywordColor]: segment.isKeyword }"
                >
                  {{ segment.text }}
                </span>
              </p>
            </template>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="w-full">
          <div class="relative rounded-lg border bg-white">
            <div class="min-h-[60px]">
              <textarea
                v-model="userResponse"
                @keydown.enter.prevent="sendTextMessage"
                placeholder="请输入你的回答..."
                class="min-h-[60px] w-full resize-none border-none bg-transparent p-3 text-gray-700 placeholder:text-gray-400 focus:outline-none focus:ring-0"
              />
            </div>
            <div class="flex items-center justify-between border-t border-gray-100 px-4 py-3">
              <div class="text-xs text-gray-500">按回车键发送回答 | 按住空格键语音输入</div>
              <div class="flex items-center gap-3">
                <!-- 语音输入按钮 -->
                <VoiceInput
                  :disabled="isLoadingQuestionText || isSynthesizingAudio || isStreamingText"
                  :maxDuration="60"
                  @result="handleVoiceResult"
                  @error="handleVoiceError"
                  @start="handleVoiceStart"
                  @stop="handleVoiceStop"
                />
                <UiButton @click="clearText" variant="ghost" size="xs" :icon="TrashIcon"> 清空文本 </UiButton>
                <UiButton
                  @click="sendTextMessage"
                  :disabled="!userResponse.trim() || isLoadingQuestionText || isSynthesizingAudio || isStreamingText"
                  variant="info"
                  size="sm"
                >
                  {{ isStreamingText ? '回答中...' : '回答完毕' }}
                </UiButton>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧对话历史 -->
      <div class="z-500 col-span-2 flex flex-col overflow-y-hidden overflow-x-visible rounded-lg bg-white">
        <div class="flex items-center gap-3 border-b border-gray-200 p-4">
          <div class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-500/10">
            <MessageIcon class="h-4 w-4 text-blue-500" />
          </div>
          <h2 class="font-medium">对话历史</h2>
        </div>
        <div ref="messageHistoryRef" class="scrollbar flex-1 overflow-y-auto overflow-x-visible p-3">
          <!-- 空状态 -->
          <div v-if="messages.length === 0" class="flex h-full flex-col items-center justify-center text-center">
            <ChatBubbleLeftIcon class="h-12 w-12 text-gray-300" />
            <p class="mt-4 text-sm text-gray-400">暂无对话记录</p>
            <p class="text-xs text-gray-400">面试开始后将在此显示对话内容</p>
          </div>

          <!-- 消息列表 -->
          <div
            v-else
            v-for="(message, index) in messages"
            :key="message.id || index"
            class="group relative mb-3 rounded-lg border border-gray-200 bg-gray-50 p-0 shadow-sm transition-all last:mb-0 hover:border-gray-300 hover:shadow"
            @mouseenter="handleMessageMouseEnter(index)"
            @mouseleave="handleMessageMouseLeave(index)"
          >
            <!-- Hover 小窗 - 使用 Popover 组件显示完整消息内容 -->
            <Popover
              :isShow="showHoverMenu === index"
              position="left"
              :showArrow="false"
              :offset="8"
              :margin="[10, 10]"
              :disableCloseByClickOutside="true"
              containerClass="bg-white border border-gray-200 shadow-xl max-w-md"
            >
              <template #content>
                <div class="p-4" @mouseenter="handlePopupMouseEnter" @mouseleave="handlePopupMouseLeave">
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-xs font-medium text-gray-500">
                      {{ message.role === 'interviewer' ? '面试官' : '你' }}
                    </span>
                    <span class="text-xs text-gray-400">{{ message.time }}</span>
                  </div>
                  <div class="popup-scrollbar max-h-[600px] min-h-[100px] overflow-y-auto">
                    <p class="whitespace-pre-wrap p-2 text-sm leading-relaxed text-gray-700">{{ message.content }}</p>
                  </div>
                  <div class="mt-2 flex items-center gap-2 border-t border-gray-200 pt-3">
                    <UiButton @click="copyMessage(message.content)" variant="ghost" size="xs" :icon="CopyIcon"> 复制 </UiButton>
                    <!-- <button
                      @click="deleteMessage(index)"
                      class="flex items-center gap-1 rounded px-2 py-1 text-xs text-red-600 transition-colors hover:bg-red-50"
                    >
                      <DeleteIcon class="h-3 w-3" />
                      删除
                    </button> -->
                  </div>
                </div>
              </template>
            </Popover>

            <div class="flex items-center justify-between border-b border-gray-100 px-3 py-2">
              <div class="flex items-center gap-2">
                <span class="text-sm font-medium text-gray-700">
                  {{ message.role === 'interviewer' ? '面试官' : '你' }}
                </span>
              </div>
              <div class="flex items-center gap-2">
                <div
                  v-if="message.type === 'voice' && message.audioDuration"
                  class="flex items-center gap-1 rounded bg-gray-100 px-1.5 py-0.5 text-xs text-gray-500"
                >
                  <MicrophoneIconHero class="h-3 w-3" />
                  <span>{{ formatDuration(message.audioDuration) }}</span>
                </div>
                <div class="text-xs text-gray-500">{{ message.time }}</div>
              </div>
            </div>
            <div class="p-3">
              <!-- SVG Loader for interviewer streaming message -->
              <div v-if="message.role === 'interviewer' && message.isStreaming" class="mb-1 flex items-center text-sm text-gray-500">
                <LoadingIcon class="mr-2 h-4 w-4 animate-spin text-blue-600" />
                <span v-if="!message.content.trim()">面试官思考中...</span>
              </div>
              <!-- Message content (will be updated by stream if applicable) -->
              <p class="line-clamp-4 h-20 overflow-hidden text-sm text-gray-600">{{ message.content }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模态框 -->
    <SettingsModal
      v-if="showSettingsModal"
      @close="showSettingsModal = false"
      @updateTtsSettings="handleUpdateTtsSettings"
      :timerValue="interviewTime"
      :isTimerRunning="isRunning"
      :startTimer="startTimer"
      :pauseTimer="pauseTimer"
      :resetTimer="resetTimer"
      :ttsSettings="currentTtsSettings"
    />
    <EndInterviewModal v-if="showEndInterviewModal" @close="showEndInterviewModal = false" @confirm="endInterview" :timerValue="interviewTime" />

    <!-- 面试评价面板 -->
    <div v-if="showEvaluationPanel" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      <InterviewEvaluationPanel
        :evaluation="evaluation"
        :isGenerating="isGeneratingEvaluation"
        @stop="handleEvaluationStop"
        @check="handleEvaluationCheck"
        @reset="handleEvaluationReset"
      />
    </div>

    <audio ref="interviewerAudioPlayer" class="hidden"></audio>
  </div>
</template>

<script setup lang="ts">
import ChartBarIcon from '@/assets/icons/chart-bar-icon.svg'
import ChatBubbleIcon from '@/assets/icons/chat-bubble-icon.svg'
import CopyIcon from '@/assets/icons/copy-icon.svg'
import DeleteIcon from '@/assets/icons/delete-icon.svg'
import LoadingIcon from '@/assets/icons/loading-icon.svg'
import Logo from '@/assets/icons/logo.svg'
import MessageIcon from '@/assets/icons/message-icon.svg'
import MoreIcon from '@/assets/icons/more-icon.svg'
import PlayIcon from '@/assets/icons/play-icon.svg'
import SettingsIcon from '@/assets/icons/settings-icon.svg'
import SuggestionIcon from '@/assets/icons/suggestion-icon.svg'
import TimerIcon from '@/assets/icons/timer-icon.svg'
import TrashIcon from '@/assets/icons/trash-icon.svg'
import type { ApiResponse } from '~/types'
import type { InterviewStage } from '~/types/interview'
import { MicrophoneIcon as MicrophoneIconHero, ChatBubbleLeftIcon } from '@heroicons/vue/24/solid'
import { parseMarkedTextToSegments, stripMarkers } from '@/utils/textUtils'
import { formatDuration } from '@/utils/timeUtils'
import { useInterviewStore } from '@/stores/interview'
import type { ChatMessage } from '@/composables/useInterviewerAgent'
import { useInterviewerAgentWithMinimax } from '@/composables/useInterviewerAgentWithMinimax'
import { usePrepareInterviewStore } from '@/stores/prepare-interview'
import { useToast } from 'vue-toast-notification'
import type { ComprehensiveEvaluation } from '~/server/prompt-service/interview-evaluation'

const { useAutoCanonicalUrl } = useCanonicalUrl()
const { useAutoHreflang } = useHreflang()
useAutoCanonicalUrl()
useAutoHreflang()

useHead({
  title: '面试进行中 - Hi-Offer',
  titleTemplate: '%s',
})

useSeoMeta({
  description: 'Hi-offer AI模拟面试正在进行中，实时对话训练，提升您的面试技能。',
  ogTitle: '面试进行中 - Hi-Offer',
  ogDescription: 'Hi-offer AI模拟面试正在进行中，实时对话训练，提升您的面试技能。',
  robots: 'noindex, nofollow',
})

const toast = useToast()
const prepareInterviewStore = usePrepareInterviewStore()
const router = useRouter()
// 定义用于突出显示文本中关键字的颜色
const keywordColors = ['text-blue-600', 'text-green-600', 'text-teal-600', 'text-indigo-600', 'text-red-500']
const interviewerKeywordColor = keywordColors[0]
const analysisKeywordColor = keywordColors[1]
const aiSuggestionKeywordColor = keywordColors[2]

// 面试计时器相关状态与控制函数
const { formattedTime: interviewTime, isRunning, start: startTimer, pause: pauseTimer, reset: resetTimer } = useInterviewTimer()

// 控制模态框显示的响应式变量
const showSettingsModal = ref(false)
const showEndInterviewModal = ref(false)

// DOM 引用，用于自动滚动消息历史记录和控制音频播放
const messageHistoryRef = ref<HTMLElement | null>(null)
const interviewerAudioPlayer = ref<HTMLAudioElement | null>(null)

// 消息处理逻辑，包括用户输入、发送消息和清空文本
const { messages, userInput: userResponse, sendTextMessage: _sendTextMessage, clearText } = useMessageHandler(messageHistoryRef)

const interviewStore = useInterviewStore()
// const interviewType = computed(() => interviewStore.currentType || 'frontend')
const interviewType = computed(() => prepareInterviewStore.formData.selectedInterviewType)

// 用于在聊天中显示面试官流式消息的引用
const streamingInterviewerMessageInChat = ref<ChatMessage | null>(null)

// 当前 TTS 设置状态
const currentTtsSettings = ref({
  model: 'speech-02-hd',
  voiceId: 'male-qn-jingying',
  speed: 1.0,
  volume: 1.0,
  emotion: 'happy',
})

// 面试官代理，处理面试官行为、问题生成、语音合成等 - 使用 MiniMax TTS
const {
  interviewer,
  isLoadingQuestionText,
  isSynthesizingAudio,
  isStreamingText,
  revealedQuestionContent,
  displayQuestionContent,
  handleQuestionCycle,
  getInitialQuestion,
  getNextQuestion,
  setInterviewerName,
  resetInterviewerState,
  isPlaybackBlockedByInteraction,
  triggerManualAudioPlayback,
  updateTTSOptions,
} = useInterviewerAgentWithMinimax({
  interviewerAudioPlayer,
  messages,
  streamingInterviewerMessageInChat,
  messageHistoryRef,
  interviewerInitialName: interviewStore.currentInterviewer?.name,
  // MiniMax TTS 配置选项
  ttsOptions: currentTtsSettings.value,
})

// 面试分析，获取和处理对用户回答的分析
const {
  isLoading: isLoadingAnalysis,
  error: errorAnalysis,
  getAnalysis,
  processedContent: liveAnalysisContent,
  isStreamProcessing: isAnalysisStreaming,
} = useInterviewAnalysis()

// AI 回答建议，获取和处理对用户回答的建议
const {
  isLoading: isLoadingSuggestion,
  error: errorSuggestion,
  getSuggestion,
  processedContent: liveSuggestionContent,
  isStreamProcessing: isSuggestionStreaming,
} = useInterviewSuggestion()

// 存储面试分析结果
const analysis = ref<{ content: string }>({
  content: '',
})

// 存储 AI 回答建议
const aiSuggestion = ref<{ content: string[] }>({
  content: [],
})

const currentStage = ref<InterviewStage>('introduction')

const showSuggestion = ref(false)
const suggestionSwitchTips = ref('显示建议')
const showHoverMenu = ref<number | null>(null)
const hoverTimeout = ref<ReturnType<typeof setTimeout> | null>(null)

watch(showSuggestion, newVal => {
  if (newVal) return (suggestionSwitchTips.value = '隐藏建议')
  return (suggestionSwitchTips.value = '显示建议')
})

const sendTextMessage = async () => {
  if (!userResponse.value.trim()) return
  _sendTextMessage()

  // 重置分析和建议内容
  analysis.value = { content: '' }
  aiSuggestion.value = { content: [] }

  const currentInterviewType = interviewType.value

  // 1. 获取当前所有对话历史，组装成API需要的格式
  const currentMessagesSnapshot = [...messages.value]
  const messagesForApi = currentMessagesSnapshot.map(msg => ({
    role: msg.role as 'interviewer' | 'user' | 'assistant',
    content: stripMarkers(msg.content),
  }))

  // 2. 检查是否需要更新面试阶段
  try {
    const { data } = await $fetch<ApiResponse>('/api/interview/stage-update', {
      method: 'POST',
      body: {
        messages: messagesForApi,
        currentStage: currentStage.value,
      },
    })
    if (data?.result?.shouldTransition) {
      currentStage.value = data.result.nextStage
      console.log('面试阶段已更新:', data.result.nextStage, '原因:', data.result.reason)
    }
  } catch (error) {
    console.error('更新面试阶段失败:', error)
  }

  // 3. 传递消息数组和当前阶段给 getNextQuestion
  const nextQuestionFullContent = await handleQuestionCycle(() => getNextQuestion(messagesForApi, currentInterviewType, currentStage.value))

  if (nextQuestionFullContent) {
    // 获取对用户回答的分析
    getAnalysis(messagesForApi, currentInterviewType)
      .then(analysisData => {
        analysis.value.content = analysisData.content
      })
      .catch(err => {
        console.error('获取分析内容失败:', errorAnalysis.value || err)
        analysis.value.content = '无法加载分析内容。'
      })

    // 获取对用户回答的 AI 建议
    getSuggestion(messagesForApi, stripMarkers(nextQuestionFullContent), currentInterviewType)
      .then(suggestionData => {
        aiSuggestion.value.content = suggestionData.content
      })
      .catch(err => {
        console.error('获取建议内容失败:', errorSuggestion.value || err)
        aiSuggestion.value.content = ['无法加载建议内容。']
      })
  } else {
    // 如果未能获取到面试官的下一个问题，则设置错误提示
    console.warn('No content from next question to generate analysis/suggestion.')
    analysis.value.content = '无法加载分析，问题获取失败。'
    aiSuggestion.value.content = ['无法加载建议，问题获取失败。']
  }
}

// 评价生成相关状态
const isGeneratingEvaluation = ref(false)
const evaluation = ref<ComprehensiveEvaluation | null>(null)
const showEvaluationPanel = ref(false)

// 结束面试逻辑
const endInterview = () => {
  pauseTimer()
  showEndInterviewModal.value = false
  showEvaluationPanel.value = true
  generateEvaluation()
}

// 生成面试评价
const generateEvaluation = async () => {
  try {
    isGeneratingEvaluation.value = true
    evaluation.value = null

    const currentInterviewType = interviewType.value
    const resumeText = prepareInterviewStore.formData.resumeContent
    const jobInfo = `${prepareInterviewStore.formData.positionDescription} ${prepareInterviewStore.formData.companyDescription}`.trim()

    // 获取当前所有对话历史，组装成API需要的格式
    const currentMessagesSnapshot = [...messages.value]
    const messagesForApi = currentMessagesSnapshot.map(msg => ({
      role: msg.role as 'interviewer' | 'user' | 'assistant',
      content: stripMarkers(msg.content),
    }))

    const { data, error } = await useFetch<{
      data: {
        evaluation: ComprehensiveEvaluation
        message: string
      }
    }>('/api/interview/evaluation', {
      method: 'POST',
      body: {
        messages: messagesForApi,
        type: currentInterviewType,
        resumeText,
        jobInfo,
      },
    })

    if (error.value) {
      throw new Error(error.value.message || '生成评价失败')
    }

    if (data.value?.data?.evaluation) {
      evaluation.value = data.value.data.evaluation
    } else {
      throw new Error('评价数据格式错误')
    }
  } catch (error: any) {
    console.error('生成面试评价失败:', error)
    toast.error(error.message || '生成面试评价失败，请重试')
  } finally {
    isGeneratingEvaluation.value = false
  }
}

// 处理评价面板事件
const handleEvaluationStop = () => {
  showEvaluationPanel.value = false
  router.push('/dashboard')
}

const handleEvaluationCheck = () => {
  generateEvaluation()
}

const handleEvaluationReset = () => {
  evaluation.value = null
  isGeneratingEvaluation.value = false
}

// 消息操作相关方法
const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    toast.success('已复制消息内容', {
      position: 'top',
      duration: 1500,
      dismissible: true,
    })
  } catch (err) {
    toast.error('复制失败', {
      position: 'top',
    })
  }
}

const deleteMessage = (messageIndex: number) => {
  if (confirm('确定要删除这条消息吗？')) {
    // to do 删除消息
    toast.success('已删除消息', {
      position: 'top',
      duration: 1500,
      dismissible: true,
    })
  }
}

// 鼠标事件处理函数
const handleMessageMouseEnter = (index: number) => {
  // 清除之前的延时关闭
  if (hoverTimeout.value) {
    clearTimeout(hoverTimeout.value)
    hoverTimeout.value = null
  }
  showHoverMenu.value = index
}

// 延时关闭，给用户时间移动到popup
const handleMessageMouseLeave = (index: number) => {
  hoverTimeout.value = setTimeout(() => {
    if (showHoverMenu.value === index) {
      showHoverMenu.value = null
    }
  }, 150) // 150ms延时
}

// 鼠标进入popup时，取消关闭
const handlePopupMouseEnter = () => {
  if (hoverTimeout.value) {
    clearTimeout(hoverTimeout.value)
    hoverTimeout.value = null
  }
}

// 鼠标离开popup时，立即关闭
const handlePopupMouseLeave = () => {
  showHoverMenu.value = null
}

const handleSuggestionSwitch = (close: () => void) => {
  showSuggestion.value = !showSuggestion.value
  close()
}

// 修改 initInterviewData 函数
const initInterviewData = async () => {
  resetInterviewerState()
  // 重置阶段为初始阶段
  currentStage.value = 'introduction'

  // 重置分析和建议内容
  analysis.value = { content: '' }
  aiSuggestion.value = { content: [] }
  messages.value.splice(0, messages.value.length)

  // 设置面试官名称
  if (interviewStore.currentInterviewer) {
    setInterviewerName(interviewStore.currentInterviewer.name || '面试官')
  }

  const currentInterviewType = interviewType.value
  // 获取初始问题
  const initialQuestionFullContent = await handleQuestionCycle(() => getInitialQuestion(currentInterviewType))

  // 处理初始问题获取失败或内容为空的情况，提供一个默认的回退问题
  const hasErrorSetByCycle = revealedQuestionContent.value.includes('错误') || revealedQuestionContent.value.includes('失败')
  if (!initialQuestionFullContent && !hasErrorSetByCycle && !revealedQuestionContent.value.trim()) {
    console.warn('Initial question fetch seems to have failed without specific error message, using fallback.')
    const fallbackQuestionContent = '欢迎参加面试，请做个简单的自我介绍吧。'

    // 尝试更新由 handleQuestionCycle 添加的最后一条消息
    if (messages.value.length > 0) {
      const lastMessage = messages.value[messages.value.length - 1]
      if (lastMessage && lastMessage.role === 'interviewer' && (!lastMessage.content.trim() || lastMessage.content === '抱歉，处理时发生未知错误。')) {
        lastMessage.content = stripMarkers(fallbackQuestionContent)
        // isStreaming 已经是 false，不需要修改
      }
    } else {
      // 理论上 handleQuestionCycle 总会添加消息，此分支可能不会进入
      messages.value.push({
        role: 'interviewer',
        content: stripMarkers(fallbackQuestionContent),
        type: 'text',
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        isStreaming: false,
      })
    }

    // 更新面试官揭示的问题内容 (左侧面板)
    if (!revealedQuestionContent.value.trim() || revealedQuestionContent.value === '抱歉，处理时发生未知错误。') {
      revealedQuestionContent.value = fallbackQuestionContent
    }
  }

  // 为初始问题获取 AI 回答建议
  const lastChatMessage = messages.value.length > 0 ? messages.value[messages.value.length - 1] : null

  if (lastChatMessage && lastChatMessage.role === 'interviewer' && lastChatMessage.content.trim()) {
    const questionTextForSuggestion = lastChatMessage.content // 此内容已被 handleQuestionCycle剥离标记
    const apiMessagesForSuggestion = [
      {
        role: 'interviewer' as const,
        content: questionTextForSuggestion,
      },
    ]

    getSuggestion(apiMessagesForSuggestion, questionTextForSuggestion, currentInterviewType)
      .then(suggestionData => {
        aiSuggestion.value.content = suggestionData.content
      })
      .catch(err => {
        console.error('获取初始建议内容失败:', errorSuggestion.value || err)
        aiSuggestion.value.content = ['无法加载初始建议内容。']
      })
  } else if (revealedQuestionContent.value.trim() && !(revealedQuestionContent.value.includes('错误') || revealedQuestionContent.value.includes('失败'))) {
    // 如果聊天记录中的最后一条消息无效，但UI上显示了有效的非错误问题文本（例如，备选问题），则尝试基于UI文本生成建议
    console.warn('聊天记录中的初始问题消息无效，尝试使用界面显示的文本生成建议。')
    const questionTextFromUI = stripMarkers(revealedQuestionContent.value)
    const apiMessagesForUISuggestion = [
      {
        role: 'interviewer' as const,
        content: questionTextFromUI,
      },
    ]
    getSuggestion(apiMessagesForUISuggestion, questionTextFromUI, currentInterviewType)
      .then(suggestionData => {
        aiSuggestion.value.content = suggestionData.content
      })
      .catch(err => {
        console.error('获取初始建议内容 (基于UI文本) 失败:', errorSuggestion.value || err)
        aiSuggestion.value.content = ['无法加载初始建议内容 (UI)。']
      })
  } else {
    console.warn('初始问题内容为空或获取失败，无法生成AI建议。')
    aiSuggestion.value.content = ['问题加载失败或内容为空，无法生成AI建议。']
  }
}

// 处理 TTS 设置更新
const handleUpdateTtsSettings = (newSettings: typeof currentTtsSettings.value) => {
  currentTtsSettings.value = { ...newSettings }
  updateTTSOptions(newSettings)

  // 可选：保存到本地存储
  localStorage.setItem('minimax-tts-settings', JSON.stringify(newSettings))
}

// 语音输入事件处理函数
const handleVoiceResult = (text: string) => {
  // 将语音识别结果填充到文本输入框
  if (text.trim()) {
    userResponse.value = text.trim()
    toast.success('语音识别完成', {
      position: 'top',
      duration: 2000,
      dismissible: true,
    })
  }
}

const handleVoiceError = (error: string) => {
  console.error('语音输入错误:', error)
  toast.error(`语音输入失败: ${error}`, {
    position: 'top',
    duration: 3000,
    dismissible: true,
  })
}

const handleVoiceStart = () => {
  // 语音录音开始时的处理
  console.log('开始语音录音')
}

const handleVoiceStop = () => {
  // 语音录音停止时的处理
  console.log('停止语音录音')
}

onMounted(() => {
  // 从本地存储加载 TTS 设置
  const savedSettings = localStorage.getItem('minimax-tts-settings')
  if (savedSettings) {
    try {
      const parsed = JSON.parse(savedSettings)
      currentTtsSettings.value = { ...currentTtsSettings.value, ...parsed }
      updateTTSOptions(currentTtsSettings.value)
    } catch (error) {
      console.warn('加载保存的 TTS 设置失败:', error)
    }
  }

  // 监听消息变化，自动滚动到最新消息
  watch(
    messages,
    () => {
      nextTick(() => {
        if (messageHistoryRef.value) {
          messageHistoryRef.value.scrollTop = messageHistoryRef.value.scrollHeight
        }
      })
    },
    { deep: true },
  )

  // 初始化面试数据并开始计时
  initInterviewData()
  startTimer()
})
</script>

<style scoped>
/* 自定义popup滚动条样式 */
.popup-scrollbar::-webkit-scrollbar {
  width: 2px;
}

.popup-scrollbar::-webkit-scrollbar-track {
  border-radius: 2px;
}

.popup-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.popup-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox 滚动条样式 */
.popup-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}
</style>
