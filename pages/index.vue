<template>
  <div class="relative flex min-h-screen flex-col items-center overflow-x-hidden bg-[#F5F8FF]">
    <Header />
    <IconBg class="pointer-events-none fixed left-0 top-0 z-0 h-screen w-screen object-cover" />
    <div class="relative z-10 flex w-full flex-col items-center px-4 sm:px-6 md:px-12 lg:px-16">
      <h1
        class="mt-20 max-w-4xl text-center text-3xl font-bold leading-tight text-[#252432] sm:mt-24 sm:text-4xl md:mt-32 md:text-5xl lg:mt-40 lg:text-6xl xl:mt-48 xl:text-[74px]"
      >
        <span class="text-gradient-01 text-shadow">Hi-Offer</span><br />
        <span class="mt-2 inline-block">全方位职业发展助手</span>
      </h1>
      <p class="animate-fadeIn mt-6 max-w-2xl text-center text-base leading-relaxed text-gray-700 sm:text-lg md:mt-8">
        从职业评估、简历优化到面试准备，Hi-offer助您找到职业核心价值，投射未来潜力，实现职业蜕变
      </p>
      <div class="mt-8 flex flex-col items-center gap-4 sm:mt-10 sm:flex-row sm:gap-6 md:mt-12 lg:mt-16">
        <NuxtLink
          to="/register"
          class="w-full text-nowrap rounded-xl bg-[#252432] px-8 py-4 text-center font-semibold text-white transition-all duration-300 hover:scale-105 hover:bg-[#252432]/90 hover:shadow-lg sm:w-auto sm:px-12 sm:py-4"
        >
          开启职业新篇章
        </NuxtLink>
        <NuxtLink
          to="/about"
          class="w-full text-nowrap rounded-xl border-2 border-[#252432] px-8 py-4 text-center font-semibold text-[#252432] transition-all duration-300 hover:scale-105 hover:bg-[#252432]/5 hover:shadow-md sm:w-auto sm:px-12 sm:py-4"
        >
          关于 Hi-Offer
        </NuxtLink>
      </div>

      <!-- 核心功能展示 -->
      <div class="mt-16 w-full max-w-7xl sm:mt-20 md:mt-24 lg:mt-32">
        <h2 class="mb-8 text-center text-2xl font-bold text-[#252432] sm:mb-10 sm:text-3xl md:mb-12 md:text-4xl lg:mb-16">Hi-Offer 能为您提供什么？</h2>

        <!-- 功能卡片网格 -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 sm:gap-8 lg:grid-cols-3">
          <!-- 职业评估 -->
          <div
            class="group relative overflow-hidden rounded-2xl bg-white p-6 shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl sm:p-8"
          >
            <div
              class="absolute -right-10 -top-10 h-40 w-40 rounded-full bg-gradient-to-r from-purple-400 to-indigo-500 opacity-5 transition-all duration-500 group-hover:opacity-10"
            ></div>
            <div class="relative z-10">
              <div class="mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-500 to-indigo-600 text-white shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <h3 class="mb-3 text-xl font-bold text-gray-900 sm:text-2xl">职业评估</h3>
              <p class="leading-relaxed text-gray-600">全面评估您的职业潜力，明确核心竞争力，推荐适合的职业方向（运营、技术、产品等）和岗位选择</p>
            </div>
          </div>

          <!-- 简历诊断辅导 -->
          <div class="group relative overflow-hidden rounded-xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl">
            <div
              class="absolute -right-10 -top-10 h-40 w-40 rounded-full bg-gradient-to-r from-pink-400 to-rose-500 opacity-5 transition-all duration-500 group-hover:opacity-10"
            ></div>
            <div class="relative z-10">
              <div class="mb-4 flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-r from-pink-500 to-rose-500 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 class="mb-2 text-xl font-bold text-gray-900">简历诊断辅导</h3>
              <p class="text-gray-700">诊断简历中的错漏和不足，提供专业的修改建议，帮助您打造一份能够脱颖而出的简历</p>
            </div>
          </div>

          <!-- 简历内容优化 -->
          <div class="group relative overflow-hidden rounded-xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl">
            <div
              class="absolute -right-10 -top-10 h-40 w-40 rounded-full bg-gradient-to-r from-blue-400 to-cyan-500 opacity-5 transition-all duration-500 group-hover:opacity-10"
            ></div>
            <div class="relative z-10">
              <div class="mb-4 flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                  />
                </svg>
              </div>
              <h3 class="mb-2 text-xl font-bold text-gray-900">简历内容优化</h3>
              <p class="text-gray-700">智能优化简历内容，提升表达力和专业性，突出您的核心竞争力和成就</p>
            </div>
          </div>

          <!-- 简历多语言翻译 -->
          <div class="group relative overflow-hidden rounded-xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl">
            <div
              class="absolute -right-10 -top-10 h-40 w-40 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 opacity-5 transition-all duration-500 group-hover:opacity-10"
            ></div>
            <div class="relative z-10">
              <div class="mb-4 flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-r from-amber-500 to-orange-500 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
                  />
                </svg>
              </div>
              <h3 class="mb-2 text-xl font-bold text-gray-900">简历多语言翻译</h3>
              <p class="text-gray-700">专业翻译简历内容，支持多种语言，帮助您拓展国际职业机会</p>
            </div>
          </div>

          <!-- 简历帮写 -->
          <div class="group relative overflow-hidden rounded-xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl">
            <div
              class="absolute -right-10 -top-10 h-40 w-40 rounded-full bg-gradient-to-r from-emerald-400 to-teal-500 opacity-5 transition-all duration-500 group-hover:opacity-10"
            ></div>
            <div class="relative z-10">
              <div class="mb-4 flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-r from-emerald-500 to-teal-500 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
              </div>
              <h3 class="mb-2 text-xl font-bold text-gray-900">简历帮写</h3>
              <p class="text-gray-700">根据您提供的基本信息，智能扩写简历模块，让您的简历更加丰富和专业</p>
            </div>
          </div>

          <!-- 简历迁移 -->
          <!-- <div class="group relative overflow-hidden rounded-xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl">
            <div
              class="absolute -right-10 -top-10 h-40 w-40 rounded-full bg-gradient-to-r from-violet-400 to-purple-500 opacity-5 transition-all duration-500 group-hover:opacity-10"
            ></div>
            <div class="relative z-10">
              <div class="mb-4 flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-r from-violet-500 to-purple-500 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                </svg>
              </div>
              <h3 class="mb-2 text-xl font-bold text-gray-900">简历迁移</h3>
              <p class="text-gray-700">根据已有简历内容，智能迁移至不同职业领域，帮助您顺利转型（如运营转产品、技术转管理等）</p>
            </div>
          </div> -->

          <!-- 模拟面试 -->
          <div class="group relative overflow-hidden rounded-xl bg-white p-6 shadow-lg transition-all duration-300 hover:shadow-xl">
            <div
              class="absolute -right-10 -top-10 h-40 w-40 rounded-full bg-gradient-to-r from-indigo-400 to-blue-500 opacity-5 transition-all duration-500 group-hover:opacity-10"
            ></div>
            <div class="relative z-10">
              <div class="mb-4 flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-r from-indigo-500 to-blue-500 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"
                  />
                </svg>
              </div>
              <h3 class="mb-2 text-xl font-bold text-gray-900">模拟面试</h3>
              <p class="text-gray-700">AI驱动的面试模拟系统，提供真实场景练习和即时反馈，全面提升面试表现</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 特点展示区域 -->
      <div class="mt-32 w-full max-w-7xl">
        <h2 class="mb-16 text-center text-3xl font-bold md:text-4xl">为什么选择 <span class="text-gradient-01 text-shadow">Hi-Offer</span></h2>
        <div class="grid grid-cols-1 gap-8 md:grid-cols-3">
          <FeatureCard v-for="(feature, index) in features" :key="index" :feature="feature" :index="index" />
        </div>
      </div>

      <!-- 统计数据展示 -->
      <div class="mt-32 w-full max-w-7xl">
        <div class="relative px-4">
          <h2 class="mb-16 text-center text-3xl font-bold md:text-4xl">值得信赖的职业伙伴</h2>

          <!-- 统计数据容器 -->
          <div class="relative mx-auto max-w-5xl overflow-hidden rounded-3xl shadow-lg">
            <!-- 背景装饰 -->
            <div class="absolute inset-0 -z-10 bg-gradient-to-br from-purple-100/80 via-pink-100/80 to-indigo-100/80 backdrop-blur-sm"></div>

            <!-- 装饰元素 -->
            <div class="absolute -left-10 -top-10 h-40 w-40 rounded-full bg-gradient-to-r from-purple-300 to-pink-300 opacity-20 blur-xl"></div>
            <div class="absolute -bottom-10 -right-10 h-40 w-40 rounded-full bg-gradient-to-r from-blue-300 to-indigo-300 opacity-20 blur-xl"></div>

            <!-- 分隔线 -->
            <div class="absolute left-0 right-0 top-0 h-[2px] bg-gradient-to-r from-transparent via-purple-300 to-transparent opacity-70"></div>
            <div class="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-purple-300 to-transparent opacity-70"></div>

            <!-- 统计数据 -->
            <div class="grid grid-cols-2 divide-x divide-purple-200/50 md:grid-cols-4">
              <div class="group px-4 py-10 transition-all duration-300 hover:bg-white/30 md:py-16">
                <StatCard :stat="stats[0]" :index="0" />
              </div>
              <div class="group px-4 py-10 transition-all duration-300 hover:bg-white/30 md:py-16">
                <StatCard :stat="stats[1]" :index="1" />
              </div>
              <div class="group px-4 py-10 transition-all duration-300 hover:bg-white/30 md:border-l md:border-purple-200/50 md:py-16">
                <StatCard :stat="stats[2]" :index="2" />
              </div>
              <div class="group px-4 py-10 transition-all duration-300 hover:bg-white/30 md:py-16">
                <StatCard :stat="stats[3]" :index="3" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 品牌故事区域 -->
      <div class="mt-32 w-full max-w-7xl">
        <div class="relative overflow-hidden rounded-3xl bg-gradient-to-br from-white to-[#f9f5ff] p-8 shadow-sm">
          <!-- 装饰元素 -->
          <div class="absolute -right-16 -top-16 h-48 w-48 rounded-full bg-gradient-to-r from-purple-200 to-pink-200 opacity-20 blur-xl"></div>
          <div class="absolute -bottom-16 -left-16 h-48 w-48 rounded-full bg-gradient-to-r from-blue-200 to-indigo-200 opacity-20 blur-xl"></div>
          <div class="absolute right-1/4 top-1/3 h-6 w-6 rounded-full bg-gradient-to-r from-pink-400 to-purple-400 opacity-30"></div>
          <div class="absolute bottom-1/4 left-1/3 h-4 w-4 rounded-full bg-gradient-to-r from-blue-400 to-indigo-400 opacity-30"></div>

          <h2 class="relative mb-10 text-center text-3xl font-bold md:text-4xl">为什么会有 <span class="text-gradient-01"> Hi-Offer?</span></h2>

          <div class="relative grid grid-cols-1 gap-10 md:grid-cols-2">
            <!-- 左侧：品牌名称的深层含义 -->
            <div class="flex flex-col space-y-6 transition-all duration-300 hover:translate-y-[-4px]">
              <h3 class="text-xl font-semibold text-[#252432]">Hi-Offer 的深层含义</h3>

              <div class="group relative overflow-hidden rounded-xl bg-white/80 p-5 shadow-sm transition-all duration-300 hover:shadow-md">
                <div
                  class="absolute -right-4 -top-4 h-16 w-16 rounded-full bg-gradient-to-r from-purple-400/10 to-indigo-400/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                ></div>
                <h4 class="mb-2 flex items-center text-lg font-medium text-purple-700">
                  <span class="mr-2 flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-purple-500 to-indigo-500">
                    <TargetCoreIcon class="h-6 w-6 text-white" />
                  </span>
                  核心与本质
                </h4>
                <p class="text-gray-700">Hi-Offer 帮助您找到职业的核心价值，明确职业方向。</p>
              </div>

              <div class="group relative overflow-hidden rounded-xl bg-white/80 p-5 shadow-sm transition-all duration-300 hover:shadow-md">
                <div
                  class="absolute -right-4 -top-4 h-16 w-16 rounded-full bg-gradient-to-r from-pink-400/10 to-rose-400/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                ></div>
                <h4 class="mb-2 flex items-center text-lg font-medium text-pink-700">
                  <span class="mr-2 flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-pink-500 to-rose-500">
                    <ArrowUpwardIcon class="h-6 w-6 text-white" />
                  </span>
                  投射与拓展
                </h4>
                <p class="text-gray-700">Hi-Offer 帮助您投射未来的职业路径，拓展潜在能力。</p>
              </div>

              <div class="group relative overflow-hidden rounded-xl bg-white/80 p-5 shadow-sm transition-all duration-300 hover:shadow-md">
                <div
                  class="absolute -right-4 -top-4 h-16 w-16 rounded-full bg-gradient-to-r from-blue-400/10 to-cyan-400/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                ></div>
                <h4 class="mb-2 flex items-center text-lg font-medium text-blue-700">
                  <span class="mr-2 flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-cyan-500">
                    <RefreshCoreIcon class="h-6 w-6 text-white" />
                  </span>
                  重启与恢复
                </h4>
                <p class="text-gray-700">Hi-Offer 帮助您在职业道路上遇到挫折后，重新站起来，继续前进。</p>
              </div>
            </div>

            <!-- 右侧：我们的使命 -->
            <div class="flex flex-col space-y-6">
              <h3 class="text-xl font-semibold text-[#252432]">我们的使命</h3>

              <div class="relative overflow-hidden rounded-xl bg-white/80 p-6 shadow-sm transition-all duration-300 hover:translate-y-[-4px] hover:shadow-md">
                <div class="absolute -right-8 -top-8 h-24 w-24 rounded-full bg-gradient-to-r from-indigo-400/10 to-purple-400/10"></div>

                <div class="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-indigo-100 to-purple-100">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4"
                    />
                  </svg>
                </div>

                <p class="mb-4 text-gray-700">
                  Hi-Offer 的诞生源于对现代职场人困境的深刻理解。许多人在职业发展中感到迷茫，不知如何评估自己的职业方向，简历不够突出，面试表现不佳。
                </p>

                <div class="relative mt-6 rounded-lg bg-gradient-to-r from-indigo-50 to-purple-50 p-4">
                  <p class="text-gray-700">
                    我们不仅仅是一个工具，更是您职业道路上的伙伴。从职业评估、简历优化到面试准备，Hi-offer提供全方位的职业发展服务，帮助您找到方向，提升自信，迎接挑战。
                  </p>

                  <div class="mt-4 flex flex-wrap gap-2">
                    <span class="rounded-full bg-indigo-100 px-3 py-1 text-xs font-medium text-indigo-800">职业评估</span>
                    <span class="rounded-full bg-purple-100 px-3 py-1 text-xs font-medium text-purple-800">简历优化</span>
                    <span class="rounded-full bg-pink-100 px-3 py-1 text-xs font-medium text-pink-800">面试准备</span>
                    <span class="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800">职业规划</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户评价 -->
      <div class="mb-32 mt-32 w-full max-w-7xl overflow-x-hidden">
        <h2 class="mb-16 text-center text-3xl font-bold md:text-4xl">用户评价</h2>
        <Testimonials :testimonials="testimonials" />
      </div>

      <!-- CTA 区域 -->
      <div class="relative mb-32 w-full max-w-5xl overflow-hidden rounded-3xl bg-[#1F1E2C] px-8 py-16 text-center">
        <!-- 背景装饰图案 -->
        <div class="absolute inset-0 opacity-10">
          <div class="absolute -left-10 -top-10 h-40 w-40 rounded-full border-2 border-purple-300"></div>
          <div class="absolute bottom-10 right-10 h-24 w-24 rounded-full border-2 border-blue-300"></div>
          <div class="absolute left-1/2 top-1/3 h-16 w-16 -translate-x-1/2 rounded-full border-2 border-pink-300"></div>
          <div class="absolute bottom-0 left-20 h-32 w-32 rounded-full border-2 border-indigo-300"></div>
          <div class="absolute right-0 top-0 h-64 w-64 rounded-full border-2 border-cyan-300"></div>
          <svg class="absolute -bottom-10 -right-10 h-64 w-64 text-white opacity-5" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M50 0L56.1 43.9L100 50L56.1 56.1L50 100L43.9 56.1L0 50L43.9 43.9L50 0Z" fill="currentColor" />
          </svg>
          <svg class="absolute -left-10 top-1/3 h-32 w-32 text-white opacity-5" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="50" cy="50" r="50" fill="currentColor" />
          </svg>
        </div>

        <div class="relative z-10">
          <h2 class="animate-fadeIn mb-6 text-3xl font-bold text-white md:text-4xl">准备好开启您的职业蜕变之旅了吗？</h2>
          <p class="animate-fadeIn mx-auto mb-10 max-w-2xl text-gray-300" style="animation-delay: 0.2s">
            加入数万名使用 Hi-Offer 的职场人士，从职业评估、简历优化到面试准备，全方位提升您的职业竞争力
          </p>

          <NuxtLink
            to="/register"
            class="animate-fadeIn group relative inline-flex items-center overflow-hidden rounded-[10px] bg-[#6366F1] px-[58px] py-[18px] text-center font-bold text-white transition-all hover:bg-[#4F46E5]"
            style="animation-delay: 0.4s"
          >
            <span class="relative z-10 transition-transform duration-300 group-hover:translate-x-1">开始职业蜕变</span>
            <svg class="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </NuxtLink>
        </div>

        <!-- 装饰性元素 -->
        <div class="absolute -bottom-2 left-0 right-0 h-2 bg-[#6366F1]"></div>
        <div class="absolute -right-2 bottom-0 top-0 w-2 bg-[#6366F1]"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import IconBg from '@/assets/icons/icon-homepage-bg.svg'
import { useIntersectionObserver } from '@vueuse/core'
import ArrowUpwardIcon from '@/assets/icons/arrow-upward.svg'
import TargetCoreIcon from '@/assets/icons/target-core.svg'
import RefreshCoreIcon from '@/assets/icons/refresh-cycle.svg'

interface SessionData {
  user: {
    id: string
    [key: string]: any
  } | null
}

const router = useRouter()

const { data: session } = useFetch<SessionData>('/api/auth/session')

watchEffect(() => {
  if (session.value?.user) {
    router.push('/dashboard')
  }
})

// 特点数据
const features = [
  {
    iconText: 'target-core',
    title: '发掘职业本质',
    description: '通过职业评估，帮助您找到职业的核心价值和竞争力，明确最适合的职业方向和岗位选择',
    color: 'from-purple-500 to-indigo-600',
  },
  {
    iconText: 'arrow-upward',
    title: '投射职业未来',
    description: '通过简历优化和迁移，帮助您展现最佳职业形象，投射理想的职业发展路径，发掘潜在机会',
    color: 'from-pink-500 to-rose-500',
  },
  {
    iconText: 'refresh-cycle',
    title: '职业反弹与成长',
    description: '通过模拟面试和全方位辅导，帮助您在职业挫折后重新站起，提升能力，实现职业目标',
    color: 'from-blue-500 to-cyan-500',
  },
]

// 统计数据
const stats = [
  { value: '50万+', label: '职场蜕变者' },
  { value: '85%', label: '职业目标达成率' },
  { value: '98%', label: '用户满意度' },
  { value: '3倍', label: '职业竞争力提升' },
]

// 客户评价
const testimonials = [
  {
    content: '通过Hi-offer的模拟面试，我克服了紧张感，加上用面试题预测了几轮，准备了一个面试问题集，基本上面试官问的问题都在问题集里面嘿嘿。',
    author: '李同学',
    position: '后端开发',
    company: '某科技公司',
    avatar: '/avatars/user5.jpg',
    language: '中文',
  },
  {
    content: '通过Hi-offer的简历优化，更新简历后我拿到了心仪公司的offer！真的太感谢了！',
    author: '张同学',
    position: '前端开发',
    company: '某知名互联网公司',
    avatar: '/avatars/user1.jpg',
    language: '中文',
  },
  {
    content: 'Hi-offer的面试题预测功能太神奇了！很多一些面试都压中，有种期末划重点的感觉。',
    author: '王同学',
    position: '产品经理',
    company: '某科技公司',
    avatar: '/avatars/user3.jpg',
    language: '中文',
  },
  {
    content: '我很喜欢Hi-offer的简历模块优化，我就只需要优化技能和个人优势部分，这个就很方便！',
    author: '张同学',
    position: '运营增长',
    company: '某外贸公司',
    avatar: '/avatars/user4.jpg',
    language: '中文',
  },
]

// 使用VueUse的交叉观察器实现滚动动画
const isVisible = ref(false)
const targetRef = ref(null)

// 只在客户端使用 IntersectionObserver
onMounted(() => {
  if (import.meta.client && targetRef.value) {
    useIntersectionObserver(
      targetRef,
      ([{ isIntersecting }]) => {
        if (isIntersecting) {
          isVisible.value = true
        }
      },
      { threshold: 0.1 },
    )
  }
})

const { useAutoCanonicalUrl, getSiteUrl } = useCanonicalUrl()
const { useAutoHreflang } = useHreflang()
useAutoCanonicalUrl()
useAutoHreflang()

const siteUrl = getSiteUrl()

const structuredData = computed(() => {
  const baseUrl = siteUrl

  return [
    // Organization Schema
    {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'Hi-Offer',
      alternateName: 'Hi-offer',
      description:
        'Hi-offer是一个全方位的职业发展平台，提供职业评估、简历优化、简历翻译、简历帮写、简历迁移和模拟面试等服务，帮助您找到职业核心价值，投射未来潜力，实现职业蜕变。',
      url: baseUrl,
      logo: `${baseUrl}/favicon.ico`,
      foundingDate: '2024',
      sameAs: ['https://hi-offer.net'],
      contactPoint: {
        '@type': 'ContactPoint',
        contactType: 'customer service',
        availableLanguage: ['Chinese', 'English'],
      },
      areaServed: {
        '@type': 'Country',
        name: 'China',
      },
      knowsAbout: ['职业评估', '简历优化', '简历翻译', '简历帮写', '简历迁移', '模拟面试', '职业规划', '职业发展', 'AI面试训练'],
    },
    // WebSite Schema with Search Action
    {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: 'Hi-Offer - 全方位职业发展助手',
      alternateName: 'Hi-offer',
      description: 'Hi-offer是一个全方位的职业发展平台，提供职业评估、简历优化、简历翻译、简历帮写、简历迁移和模拟面试等服务。',
      url: baseUrl,
      inLanguage: 'zh-CN',
      publisher: {
        '@type': 'Organization',
        name: 'Hi-Offer',
      },
      potentialAction: {
        '@type': 'SearchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${baseUrl}/search?q={search_term_string}`,
        },
        'query-input': 'required name=search_term_string',
      },
    },
    // BreadcrumbList Schema for Navigation
    {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: '首页',
          item: baseUrl,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: '关于我们',
          item: `${baseUrl}/about`,
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: '价格',
          item: `${baseUrl}/pricing`,
        },
      ],
    },
    // Service Schema for Career Development Services
    {
      '@context': 'https://schema.org',
      '@type': 'Service',
      name: 'Hi-Offer 职业发展服务',
      description: '全方位职业发展助手，提供职业评估、简历优化、简历翻译、简历帮写、简历迁移和模拟面试等专业服务。',
      provider: {
        '@type': 'Organization',
        name: 'Hi-Offer',
      },
      areaServed: {
        '@type': 'Country',
        name: 'China',
      },
      availableChannel: {
        '@type': 'ServiceChannel',
        serviceUrl: baseUrl,
        serviceSmsNumber: null,
        servicePhone: null,
      },
      category: '职业发展服务',
      serviceType: '在线职业咨询和培训',
      hasOfferCatalog: {
        '@type': 'OfferCatalog',
        name: 'Hi-Offer 服务目录',
        itemListElement: [
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Service',
              name: '职业评估',
              description: '全面评估您的职业潜力，明确核心竞争力，推荐适合的职业方向和岗位选择',
            },
          },
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Service',
              name: '简历优化',
              description: '智能优化简历内容，提升表达力和专业性，突出您的核心竞争力和成就',
            },
          },
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Service',
              name: '模拟面试',
              description: 'AI驱动的面试模拟系统，提供真实场景练习和即时反馈，全面提升面试表现',
            },
          },
        ],
      },
    },
    // FAQPage Schema for Common Questions
    {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: [
        {
          '@type': 'Question',
          name: 'Hi-Offer 提供哪些服务？',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Hi-Offer 提供全方位的职业发展服务，包括职业评估、简历优化、简历翻译、简历帮写、简历迁移和模拟面试等专业服务，帮助您实现职业蜕变。',
          },
        },
        {
          '@type': 'Question',
          name: '为什么选择 Hi-Offer？',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Hi-Offer 帮助您发掘职业本质、投射职业未来、实现职业反弹与成长。我们不仅仅是一个工具，更是您职业道路上的伙伴。',
          },
        },
        {
          '@type': 'Question',
          name: 'Hi-Offer 的用户满意度如何？',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Hi-Offer 拥有98%的用户满意度，85%的职业目标达成率，已帮助50万+职场人士实现职业蜕变，职业竞争力平均提升3倍。',
          },
        },
      ],
    },
  ]
})

useHead({
  title: 'Hi-Offer - 全方位职业发展助手',
  titleTemplate: '%s',
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(structuredData.value),
    },
  ],
})

useSeoMeta({
  description:
    'Hi-offer是一个全方位的职业发展平台，提供职业评估、简历优化、简历翻译、简历帮写、简历迁移和模拟面试等服务，帮助您找到职业核心价值，投射未来潜力，实现职业蜕变。',
  ogTitle: 'Hi-Offer - 全方位职业发展助手',
  ogDescription: 'Hi-offer提供职业评估、简历优化、简历翻译、简历帮写、简历迁移和模拟面试等全方位职业发展服务。',
  twitterTitle: 'Hi-Offer - 全方位职业发展助手',
  twitterDescription: 'Hi-offer提供职业评估、简历优化、简历翻译、简历帮写、简历迁移和模拟面试等全方位职业发展服务。',
  keywords: '职业评估,简历优化,简历翻译,简历帮写,简历迁移,模拟面试,职业规划,职业核心价值,职业潜力,职业反弹,职场信心,求职成功',
})
</script>

<style scoped>
.text-gradient-01 {
  background: linear-gradient(90deg, #ff3bff 0%, #ecbfbf 38.02%, #5c24ff 75.83%, #d94fd5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
}

.text-shadow {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 1s ease-out forwards;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scaleIn {
  animation: scaleIn 0.6s ease-out forwards;
}
</style>
