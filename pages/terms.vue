<template>
  <div class="container mx-auto mt-10 max-w-4xl px-4 py-8">
    <h1 class="mb-6 text-3xl font-bold text-gray-800">用户协议</h1>

    <div class="space-y-6 text-gray-700">
      <p class="text-lg">
        <i class="font-medium">Hi-Offer</i>（以下简称"我们"）依据本协议为用户（以下简称"你"）提供
        <i class="font-medium">Hi-Offer</i> 服务。本协议对你和我们均具有法律约束力。
      </p>

      <section>
        <h2 class="mb-3 text-xl font-bold text-gray-800">一、本服务的功能</h2>
        <p>你可以使用本服务进行AI简历生成，AI模拟面试。</p>
      </section>

      <section>
        <h2 class="mb-3 text-xl font-bold text-gray-800">二、责任范围及限制</h2>
        <p>你使用本服务得到的结果仅供参考，实际情况以官方为准。</p>
      </section>

      <section>
        <h2 class="mb-3 text-xl font-bold text-gray-800">三、隐私保护</h2>
        <p>
          我们重视对你隐私的保护，你的个人隐私信息将根据《隐私政策》受到保护与规范， 详情请参阅<NuxtLink to="/privacy" class="text-blue-600 hover:underline"
            >《隐私政策》</NuxtLink
          >。
        </p>
      </section>

      <section>
        <h2 class="mb-3 text-xl font-bold text-gray-800">四、其他条款</h2>
        <div class="space-y-3">
          <p>4.1 本协议所有条款的标题仅为阅读方便，本身并无实际涵义，不能作为本协议涵义解释的依据。</p>
          <p>4.2 本协议条款无论因何种原因部分无效或不可执行，其余条款仍有效，对双方具有约束力。</p>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
const { useAutoCanonicalUrl } = useCanonicalUrl()
const { useAutoHreflang } = useHreflang()
useAutoCanonicalUrl()
useAutoHreflang()

useHead({
  title: '用户协议 - Hi-Offer',
  titleTemplate: '%s',
})

useSeoMeta({
  description: 'Hi-Offer应用的用户协议，详细说明使用条款和服务规范，保障用户权益。',
  ogTitle: '用户协议 - Hi-Offer',
  ogDescription: 'Hi-Offer应用的用户协议，详细说明使用条款和服务规范，保障用户权益。',
})
</script>
