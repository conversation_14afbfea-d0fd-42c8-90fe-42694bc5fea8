#!/usr/bin/env node
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const rootDir = path.resolve(__dirname, '../../');

const args = process.argv.slice(2);
const command = args[0];
const envFile = args[1] || '.env';

if (!['encrypt', 'decrypt'].includes(command)) {
  console.error('请指定有效的命令: encrypt 或 decrypt');
  process.exit(1);
}

const sourceFile = command === 'encrypt'
  ? path.join(rootDir, envFile)
  : path.join(rootDir, `${envFile}.dev.vault`);

const targetFile = command === 'encrypt'
  ? path.join(rootDir, `${envFile}.dev.vault`)
  : path.join(rootDir, envFile);

if (!fs.existsSync(sourceFile)) {
  console.error(`文件 ${sourceFile} 不存在`);
  process.exit(1);
}

let privateKey = '';
const keysFile = path.join(rootDir, '.env.keys');

if (fs.existsSync(keysFile)) {
  try {
    const keysContent = fs.readFileSync(keysFile, 'utf8');
    const match = keysContent.match(/DOTENV_PRIVATE_KEY=([^\r\n]+)/);
    if (match && match[1]) {
      privateKey = match[1].trim();
      console.log('已从 .env.keys 文件加载私钥');
    } else {
      console.warn('在 .env.keys 文件中未找到 DOTENV_PRIVATE_KEY');
    }
  } catch (error) {
    console.warn('无法读取 .env.keys 文件:', error.message);
  }
}

if (!privateKey && !process.env.DOTENV_PRIVATE_KEY) {
  console.error('未找到 DOTENV_PRIVATE_KEY，请设置环境变量或在 .env.keys 文件中提供');
  process.exit(1);
}

try {
  const envVars = privateKey
    ? { DOTENV_PRIVATE_KEY: privateKey, ...process.env }
    : process.env;

  const cmd = command === 'encrypt'
    ? `npx @dotenvx/dotenvx encrypt -f ${sourceFile} --stdout > ${targetFile}`
    : `npx @dotenvx/dotenvx decrypt -f ${sourceFile} --stdout > ${targetFile}`;

  if (command === 'decrypt' && fs.existsSync(targetFile)) {
    const backupFile = `${targetFile}.bak`;
    fs.copyFileSync(targetFile, backupFile);
    console.log(`已创建备份文件: ${backupFile}`);
  }

  execSync(cmd, {
    cwd: rootDir,
    env: envVars,
    stdio: ['inherit', 'inherit', 'inherit']
  });

  console.log(`成功${command === 'encrypt' ? '加密' : '解密'}文件: ${targetFile}`);
} catch (error) {
  console.error(`${command === 'encrypt' ? '加密' : '解密'}失败:`, error.message);
  process.exit(1);
}