class AudioProcessor extends AudioWorkletProcessor {
  constructor() {
    super()
  }

  process(inputs, outputs, parameters) {
    const input = inputs[0]
    
    if (input && input.length > 0) {
      const inputChannel = input[0]
      
      if (inputChannel && inputChannel.length > 0) {
        // 发送音频数据到主线程
        this.port.postMessage({
          type: 'audioData',
          data: inputChannel
        })
      }
    }
    
    return true
  }
}

registerProcessor('audio-processor', AudioProcessor)
