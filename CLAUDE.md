# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Package Management
- Use `pnpm` for package management (v8.14.3)
- Install dependencies: `pnpm install`

### Development Server
- Start development server: `pnpm dev` (runs on http://localhost:3000)
- Start with Stagewise toolbar: `pnpm dev:stagewise`

### Build & Deploy
- Build for production: `pnpm build`
- Generate static site: `pnpm generate`
- Preview production build: `pnpm preview`

### Environment Variables
- Decrypt environment variables: `pnpm env:decrypt`
- Encrypt environment variables: `pnpm env:encrypt`

### Infrastructure
- Start MongoDB & Redis: `docker-compose up -d`
- Stop services: `docker-compose down`
- MongoDB runs on port 27017, Redis on port 10637

## Architecture Overview

### Tech Stack
- **Framework**: Nuxt 3 (Vue.js full-stack framework)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Pinia
- **Authentication**: @sidebase/nuxt-auth with GitHub/Google OAuth
- **Database**: MongoDB with Mongoose
- **Cache**: Redis
- **AI Integration**: OpenAI API
- **File Processing**: PDF/image parsing, voice transcription

### Project Structure

#### Core Directories
- `pages/`: Route-based pages (dashboard, interview, login, etc.)
- `components/`: Reusable Vue components organized by feature
- `composables/`: Vue composables for shared logic
- `stores/`: Pinia stores for state management
- `server/`: Backend API routes and services
- `assets/`: Static assets (CSS, icons, images)
- `types/`: TypeScript type definitions

#### Key Backend Components
- `server/api/`: API endpoints organized by feature
- `server/services/`: Business logic services (ASR, TTS, auth)
- `server/models/`: Database models (user, interview, order)
- `server/utils/`: Utility functions and helpers
- `server/middleware/`: Request middleware (auth, logging)

#### Key Frontend Features
- Interview system with real-time chat and voice
- Resume optimization and analysis
- Payment integration (zpay)
- Dashboard with analytics
- Offer management tools

### Database Models
- `User`: User profiles and authentication
- `Interview`: Interview sessions and data
- `Order`: Payment orders and transactions
- `Wallet`: User credit/quota management

### External Integrations
- **Voice Services**: Xunfei ASR/TTS, SenseVoice, CosyVoice, Minimax
- **AI Services**: OpenAI, DeepSeek
- **Payment**: zpay integration
- **File Storage**: S3 integration
- **Email**: MailerSend

### Development Environment
- Uses encrypted environment variables for security
- MongoDB and Redis run via Docker Compose
- Hot reloading enabled for development
- Cursor editor integration configured

### Key Configuration
- Server runs on port 3131 in development
- TypeScript with module resolution: bundler
- SVG loader with auto-prefixing for unique IDs
- Tailwind CSS with custom animations and variables