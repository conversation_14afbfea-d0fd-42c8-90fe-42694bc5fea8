name: Docker Build and Deploy

on:
  push:
    branches:
      - main
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.vscode/**'
  # pull_request:
  #   branches:
  #     - main
  #   paths-ignore:
  #     - '**.md'
  #     - 'docs/**'
  #     - '.vscode/**'
  workflow_dispatch:

env:
  REGISTRY: 'docker.io'
  IMAGE_NAME: 'peanutsplash/resilo'

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr,prefix=v
            type=sha,prefix=v
            type=raw,value=latest,enable={{is_default_branch}}

      - name: 登录到 DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: 构建并推送镜像
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            NODE_ENV=production
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && (github.event_name == 'push' || github.event_name == 'workflow_dispatch')

    steps:
      - name: 触发远程部署
        run: |
          echo "触发部署到生产环境..."
          response=$(curl -s -w "%{http_code}" -o /tmp/deploy_response.txt \
            -H "Authorization: Bearer ${{ secrets.DEPLOY_TOKEN }}" \
            -X GET \
            "${{ secrets.DEPLOY_WEBHOOK_URL }}")

          http_code="${response: -3}"

          if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 201 ]; then
            echo "✅ 部署触发成功 (HTTP $http_code)"
            cat /tmp/deploy_response.txt
          else
            echo "❌ 部署触发失败 (HTTP $http_code)"
            cat /tmp/deploy_response.txt
            exit 1
          fi

      - name: 部署状态通知
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "🚀 部署已成功触发"
            echo "镜像: ${{ needs.build-and-push.outputs.image-tag }}"
            echo "提交: ${{ github.sha }}"
          else
            echo "💥 部署触发失败，请检查日志"
          fi
