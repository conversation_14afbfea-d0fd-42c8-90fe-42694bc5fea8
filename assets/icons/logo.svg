<svg viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#prefix__clip0_1_9)">
        <path fill="transparent" d="M0 0h40v40H0z" />
        <path fill-rule="evenodd" clip-rule="evenodd"
            d="M11.255 1.354C5.591 1.354 1 5.946 1 11.61v16.782c0 5.663 4.591 10.255 10.255 10.255h16.781c5.664 0 10.255-4.592 10.255-10.255V11.61c0-5.664-4.59-10.256-10.255-10.256h-16.78zm14.7 16.13a.433.433 0 01.217.375c-.001.076-.023.15-.062.216l-6.857 11.98a.44.44 0 01-.51.178.44.44 0 01-.228-.17.426.426 0 01-.073-.273l.762-7.214h-5.647a.445.445 0 01-.382-.22.423.423 0 01.01-.434l6.971-11.98a.448.448 0 01.741.005.425.425 0 01.066.275L20 17.425h5.733c.077 0 .153.02.22.058z"
            fill="url(#prefix__paint0_linear_1_9)" />
    </g>
    <defs>
        <linearGradient id="prefix__paint0_linear_1_9" x1="19.646" y1="1.354" x2="19.646" y2="38.646"
            gradientUnits="userSpaceOnUse">
            <stop offset=".002" stop-color="#6B64E8" stop-opacity=".72" />
            <stop offset=".521" stop-color="#C37FCD" />
            <stop offset="1" stop-color="#F452C8" stop-opacity=".67" />
        </linearGradient>
        <clipPath id="prefix__clip0_1_9">
            <path fill="#fff" d="M0 0h40v40H0z" />
        </clipPath>
    </defs>
</svg>