.scrollbar::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: transparent;
  /* or add it to the track */
}

.scrollbar::-webkit-scrollbar-thumb {
  background: #9e9e9e;
  border-radius: 5px;
}

.scrollbar::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: transparent;
  /* or add it to the track */
}

.scrollbar::-webkit-scrollbar-track {
  margin: var(--scrollbar-margin, 0px);
}

.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  padding-right: 5px;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.scrollbar-margin-4 {
  --scrollbar-margin: 16px;
}

.scrollbar-margin-6 {
  --scrollbar-margin: 24px;
}

.scrollbar-margin-10 {
  --scrollbar-margin: 40px;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.dark .scrollbar::-webkit-scrollbar-thumb {
  background: #585858;
}
