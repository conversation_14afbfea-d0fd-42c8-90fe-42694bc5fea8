/* 方块移动加载动画 */
.loader-square {
  --s: 20px;

  --_d: calc(0.353 * var(--s));
  width: calc(var(--s) + var(--_d));
  aspect-ratio: 1;
  display: grid;
}
.loader-square:before,
.loader-square:after {
  content: '';
  grid-area: 1/1;
  clip-path: polygon(var(--_d) 0, 100% 0, 100% calc(100% - var(--_d)), calc(100% - var(--_d)) 100%, 0 100%, 0 var(--_d));
  background: conic-gradient(from -90deg at calc(100% - var(--_d)) var(--_d), #fff 135deg, #666 0 270deg, #aaa 0);
  animation: loaderSquare 2s infinite;
}
.loader-square:after {
  animation-delay: -1s;
}
@keyframes loaderSquare {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(30px, 0);
  }
  50% {
    transform: translate(30px, 30px);
  }
  75% {
    transform: translate(0, 30px);
  }
  100% {
    transform: translate(0, 0);
  }
}

/* 进度条加载动画 */
.loader-progress {
  width: 120px;
  height: 20px;
  border-radius: 20px;
  background:
    repeating-linear-gradient(135deg, #f03355 0 10px, #ffa516 0 20px) 0/0% no-repeat,
    repeating-linear-gradient(135deg, #ddd 0 10px, #eee 0 20px) 0/100%;
  animation: loaderProgress 2s infinite;
}
@keyframes loaderProgress {
  100% {
    background-size: 100%;
  }
}

/* 弹跳球加载动画 */
.loader-bouncing-ball {
  height: 60px;
  aspect-ratio: 1;
  position: relative;
}
.loader-bouncing-ball::before,
.loader-bouncing-ball::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 50%;
  transform-origin: bottom;
}
.loader-bouncing-ball::after {
  background:
    radial-gradient(at 75% 15%, #fffb, #0000 35%),
    radial-gradient(at 80% 40%, #0000, #0008),
    radial-gradient(circle 5px, #fff 94%, #0000),
    radial-gradient(circle 10px, #000 94%, #0000),
    linear-gradient(#f93318 0 0) top / 100% calc(50% - 5px),
    linear-gradient(#fff 0 0) bottom / 100% calc(50% - 5px) #000;
  background-repeat: no-repeat;
  animation: bouncingBall 1s infinite cubic-bezier(0.5, 120, 0.5, -120);
}
.loader-bouncing-ball::before {
  background: #ddd;
  filter: blur(8px);
  transform: scaleY(0.4) translate(-13px, 0px);
}
@keyframes bouncingBall {
  30%,
  70% {
    transform: rotate(0deg);
  }
  49.99% {
    transform: rotate(0.2deg);
  }
  50% {
    transform: rotate(-0.2deg);
  }
}
