<template>
  <div class="flex min-h-screen flex-col">
    <Header v-if="shouldShowHeaderFooter" />
    <main class="flex-1">
      <slot />
    </main>
    <Footer v-if="shouldShowHeaderFooter" />
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { computed } from 'vue'

const route = useRoute()

const pagesWithHeaderFooter = ['/', '/about', '/privacy', '/terms', '/pricing']

const shouldShowHeaderFooter = computed(() => pagesWithHeaderFooter.includes(route.path))
</script>
