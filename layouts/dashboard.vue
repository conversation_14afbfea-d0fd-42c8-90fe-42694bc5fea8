<template>
  <div class="scrollbar">
    <TransitionRoot as="template" :show="sidebarOpen">
      <Dialog class="relative z-50 lg:hidden" @close="sidebarOpen = false">
        <TransitionChild
          as="template"
          enter="transition-opacity ease-linear duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="transition-opacity ease-linear duration-300"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-gray-900/80" />
        </TransitionChild>

        <div class="fixed inset-0 flex">
          <TransitionChild
            as="template"
            enter="transition ease-in-out duration-300 transform"
            enter-from="-translate-x-full"
            enter-to="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leave-from="translate-x-0"
            leave-to="-translate-x-full"
          >
            <DialogPanel class="relative mr-16 flex w-full max-w-xs flex-1">
              <TransitionChild
                as="template"
                enter="ease-in-out duration-300"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="ease-in-out duration-300"
                leave-from="opacity-100"
                leave-to="opacity-0"
              >
                <div class="absolute left-full top-0 flex w-16 justify-center pt-5">
                  <button type="button" class="-m-2.5 p-2.5" @click="sidebarOpen = false">
                    <span class="sr-only">关闭侧边栏</span>
                    <XMarkIcon class="size-6 text-white" aria-hidden="true" />
                  </button>
                </div>
              </TransitionChild>
              <!-- Sidebar component, swap this element with another sidebar if you like -->
              <div class="flex grow flex-col gap-y-6 overflow-y-auto bg-white px-4 py-6 shadow-lg">
                <div class="flex h-12 shrink-0 items-center">
                  <Logo class="h-8 w-auto" />
                  <span class="ml-2 text-xl font-semibold text-gray-900">Hi-Offer</span>
                </div>

                <div class="mt-2">
                  <div class="mb-4 text-sm font-medium text-gray-500">功能</div>

                  <ul role="list" class="space-y-3">
                    <li v-for="item in navigation" :key="item.name">
                      <NuxtLink
                        :to="item.href"
                        :class="[
                          item.current
                            ? 'border-l-4 border-indigo-600 bg-indigo-50 text-indigo-700 shadow-sm'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600',
                          'group flex items-center gap-x-3 rounded-md p-3 text-sm font-medium transition-all duration-200 ease-in-out',
                        ]"
                        @click="sidebarOpen = false"
                      >
                        <component
                          :is="item.icon"
                          :class="[
                            item.current ? 'text-indigo-700' : 'text-gray-400 group-hover:text-indigo-600',
                            'size-5 shrink-0 transition-colors duration-200',
                          ]"
                          aria-hidden="true"
                        />
                        {{ item.name }}
                      </NuxtLink>
                    </li>
                  </ul>
                </div>

                <div class="mt-6">
                  <div class="mb-4 text-sm font-medium text-gray-500">工具</div>

                  <ul role="list" class="space-y-3">
                    <li>
                      <NuxtLink
                        to="/settings"
                        :class="[
                          route.path === '/settings'
                            ? 'border-l-4 border-indigo-600 bg-indigo-50 text-indigo-700 shadow-sm'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600',
                          'group flex items-center gap-x-3 rounded-md p-3 text-sm font-medium transition-all duration-200 ease-in-out',
                        ]"
                        @click="sidebarOpen = false"
                      >
                        <Cog6ToothIcon
                          :class="[
                            route.path === '/settings' ? 'text-indigo-700' : 'text-gray-400 group-hover:text-indigo-600',
                            'size-5 shrink-0 transition-colors duration-200',
                          ]"
                          aria-hidden="true"
                        />
                        设置
                      </NuxtLink>
                    </li>
                  </ul>
                </div>

                <!-- 信用额度卡片 (移动端) -->
                <div class="mt-3">
                  <div class="rounded-lg bg-purple-50 p-4">
                    <div class="mb-1 flex items-center gap-x-2">
                      <CurrencyDollarIcon class="size-5 text-purple-600" aria-hidden="true" />
                      <span class="text-base font-medium text-purple-600">Credits</span>
                    </div>
                    <div class="mb-3 flex items-center justify-between">
                      <p class="text-[14px] font-medium text-gray-600">
                        剩余:<span class="ml-3 text-xl font-bold text-purple-500">{{ remainingCredits }}</span>
                      </p>
                    </div>
                    <UiButton
                      @click="
                        () => {
                          showPaywallModal = true
                          sidebarOpen = false
                        }
                      "
                      variant="primary"
                      size="sm"
                      full-width
                    >
                      购买更多
                    </UiButton>
                  </div>
                </div>

                <!-- 用户信息 (移动端) -->
                <div class="mt-auto pt-6">
                  <div class="rounded-lg bg-gradient-to-br from-gray-50 to-gray-100 p-4 shadow-sm ring-1 ring-gray-200/50 backdrop-blur-sm">
                    <div class="flex items-center">
                      <div class="relative">
                        <img class="size-10 rounded-full object-cover ring-2 ring-white/90 ring-offset-1" :src="userInfo.image" :alt="userInfo.name" />
                        <div class="absolute -bottom-1 -right-1 size-3 rounded-full bg-green-400 ring-1 ring-white"></div>
                      </div>
                      <div class="ml-3 flex flex-col">
                        <p class="text-sm font-medium text-gray-800">{{ userInfo.name }}</p>
                        <UiButton @click="handleLogout" variant="ghost" size="xs" class="!mt-0.5 !justify-start !px-0 !py-0 !text-xs"> 退出登录 </UiButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Static sidebar for desktop -->
    <div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col lg:p-4">
      <!-- Sidebar component, swap this element with another sidebar if you like -->
      <div class="flex grow flex-col gap-y-6 overflow-y-auto rounded-xl bg-white px-4 py-6 shadow-xl ring-1 ring-gray-200/60 [&::-webkit-scrollbar]:hidden">
        <div class="flex h-12 shrink-0 items-center">
          <Logo class="h-8 w-auto" />
          <span class="ml-2 bg-gradient-to-r from-purple-400 to-indigo-600 bg-clip-text text-xl font-semibold text-transparent">Hi-Offer</span>
        </div>

        <div class="mt-2">
          <div class="mb-4 text-sm font-medium text-gray-500">功能</div>

          <ul role="list" class="space-y-3">
            <li v-for="item in navigation" :key="item.name">
              <NuxtLink
                :to="item.href"
                :class="[
                  item.current
                    ? 'border-l-4 border-indigo-600 bg-indigo-50 text-indigo-700 shadow-sm'
                    : 'text-gray-700 hover:bg-gray-50/80 hover:text-indigo-600',
                  'group flex items-center gap-x-3 rounded-md p-3 text-sm font-medium transition-all duration-200 ease-in-out',
                ]"
              >
                <component
                  :is="item.icon"
                  :class="[item.current ? 'text-indigo-700' : 'text-gray-400 group-hover:text-indigo-600', 'size-5 shrink-0 transition-colors duration-200']"
                  aria-hidden="true"
                />
                {{ item.name }}
              </NuxtLink>
            </li>
          </ul>
        </div>

        <div class="mt-6">
          <div class="mb-4 text-sm font-medium text-gray-500">工具</div>

          <ul role="list" class="space-y-3">
            <li>
              <NuxtLink
                to="/settings"
                :class="[
                  route.path === '/settings'
                    ? 'border-l-4 border-indigo-600 bg-indigo-50 text-indigo-700 shadow-sm'
                    : 'text-gray-700 hover:bg-gray-50/80 hover:text-indigo-600',
                  'group flex items-center gap-x-3 rounded-md p-3 text-sm font-medium transition-all duration-200 ease-in-out',
                ]"
              >
                <Cog6ToothIcon
                  :class="[
                    route.path === '/settings' ? 'text-indigo-700' : 'text-gray-400 group-hover:text-indigo-600',
                    'size-5 shrink-0 transition-colors duration-200',
                  ]"
                  aria-hidden="true"
                />
                设置
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- 信用额度卡片 -->
        <div class="mt-3">
          <div class="rounded-lg bg-purple-50 p-4">
            <div class="mb-1 flex items-center gap-x-2">
              <CurrencyDollarIcon class="size-5 text-purple-600" aria-hidden="true" />
              <span class="text-base font-medium text-purple-600">Credits</span>
            </div>
            <div class="mb-3 flex items-center justify-between">
              <p class="text-[14px] font-medium text-gray-600">
                剩余:<span class="ml-3 text-xl font-bold text-purple-500">{{ remainingCredits }}</span>
              </p>
            </div>
            <UiButton @click="showPaywallModal = true" variant="primary" size="sm" full-width> 购买更多 </UiButton>
          </div>
        </div>

        <div class="mt-auto pt-6">
          <div class="rounded-lg bg-gradient-to-br from-gray-50 to-gray-100 p-4 shadow-sm ring-1 ring-gray-200/50 backdrop-blur-sm">
            <div class="flex items-center">
              <div class="relative">
                <img class="size-10 rounded-full object-cover ring-2 ring-white/90 ring-offset-1" :src="userInfo.image" :alt="userInfo.name" />
                <div class="absolute -bottom-1 -right-1 size-3 rounded-full bg-green-400 ring-1 ring-white"></div>
              </div>
              <div class="ml-3 flex flex-col">
                <p class="text-sm font-medium text-gray-800">{{ userInfo.name }}</p>
                <UiButton @click="handleLogout" variant="ghost" size="xs" class="!mt-0.5 !justify-start !px-0 !py-0 !text-xs"> 退出登录 </UiButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="lg:pl-72">
      <!-- Mobile header with hamburger menu -->
      <div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:hidden">
        <button type="button" class="-m-2.5 p-2.5 text-gray-700" @click="sidebarOpen = true">
          <span class="sr-only">打开侧边栏</span>
          <Bars3Icon class="size-6" aria-hidden="true" />
        </button>

        <!-- Separator -->
        <div class="h-6 w-px bg-gray-200" aria-hidden="true" />

        <div class="flex flex-1 items-center gap-x-4 self-stretch">
          <div class="flex items-center gap-x-2">
            <Logo class="h-7 w-auto" />
            <span class="bg-gradient-to-r from-purple-400 to-indigo-600 bg-clip-text text-lg font-semibold text-transparent">Hi-Offer</span>
          </div>

          <!-- Credits display for mobile -->
          <div class="ml-auto flex items-center gap-x-1 text-sm">
            <CurrencyDollarIcon class="size-4 text-purple-600" aria-hidden="true" />
            <span class="font-medium text-purple-600">{{ remainingCredits }}</span>
          </div>
        </div>
      </div>

      <main>
        <div class="">
          <slot />
        </div>
      </main>
    </div>
  </div>

  <!-- Paywall Modal -->
  <PaywallModal :isOpen="showPaywallModal" @close="showPaywallModal = false" />

  <!-- Credits 不足通知 -->
  <CreditsNotification :isVisible="showCreditsNotification" @close="handleCreditsNotificationClose" @purchase="handleCreditsNotificationPurchase" />
</template>

<script setup>
import { Dialog, DialogPanel, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { Cog6ToothIcon, HomeIcon, XMarkIcon, DocumentTextIcon, CurrencyDollarIcon, BriefcaseIcon, Bars3Icon } from '@heroicons/vue/24/outline'
import Logo from '@/assets/icons/logo.svg'
import { useEventBus, useEvent } from '@/composables/useEventBus'
import { useRoute } from 'vue-router'
import { isEqual } from 'lodash-es'

const { signOut, data: session } = useAuth()
const userStore = useUserStore()
const { remainingCredits } = useCredit()
const eventBus = useEventBus()
const { on: onShowPaywall } = useEvent('showPaywall')
const { on: onShowCreditsNotification } = useEvent('showCreditsNotification')

watch(
  () => session.value?.user,
  (newUser, oldUser) => {
    if (newUser && (!oldUser || !isEqual(newUser, oldUser))) {
      userStore.fetchProfile()
    }
  },
  { immediate: true },
)

const userInfo = computed(() => ({
  name: userStore.name || '未设置姓名',
  image: userStore.image,
}))

const handleLogout = async () => {
  try {
    await signOut({ callbackUrl: '/' })
  } catch (error) {
    console.error('退出登录失败:', error)
    eventBus.emit('showToast', { message: '退出登录失败', type: 'error' })
  }
}

const route = useRoute()

const pageTitle = computed(() => {
  if (route.path.startsWith('/interviews/')) {
    return '面试详情'
  }
  if (route.path === '/interviews') {
    return '面试记录'
  }
  return '工作台'
})

useHead({
  title: computed(() => `${pageTitle.value} - Hi-Offer`),
})

const navigation = computed(() => [
  { name: '工作台', href: '/dashboard', icon: HomeIcon, current: route.path === '/dashboard' },
  { name: '简历助手', href: '/dashboard/resume', icon: DocumentTextIcon, current: route.path === '/dashboard/resume' },
  { name: '简历诊断', href: '/dashboard/resume-diagnosis', icon: DocumentTextIcon, current: route.path === '/dashboard/resume-diagnosis' },
  // { name: '面试记录', href: '/interviews', icon: DocumentDuplicateIcon, current: route.path.startsWith('/interviews') },
  { name: '模拟面试', href: '/dashboard', icon: HomeIcon, current: route.path === '/dashboard' },
  { name: 'Offer助手', href: '/dashboard/offer', icon: BriefcaseIcon, current: route.path === '/dashboard/offer' },
])

const sidebarOpen = ref(false)
const showPaywallModal = ref(false)
const showCreditsNotification = ref(false)

onShowPaywall(() => {
  showPaywallModal.value = true
})

// 监听显示credits不足通知事件
onShowCreditsNotification(() => {
  showCreditsNotification.value = true
})

// 处理credits通知的购买按钮点击
const handleCreditsNotificationPurchase = () => {
  showPaywallModal.value = true
}

// 处理credits通知关闭
const handleCreditsNotificationClose = () => {
  showCreditsNotification.value = false
}
</script>
