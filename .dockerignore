# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
.nuxt
.output
dist
build

# Environment files
.env
.env.*
!.env.example

# Logs
logs
*.log

# Git and version control
.git
.gitignore
.gitattributes
.husky

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
CHANGELOG.md
LICENSE
*.md
docs/

# Development tools
.eslintrc*
.prettierrc*
.editorconfig
dev-tools/

# Test files
test/
tests/
__tests__/
*.test.*
*.spec.*

# Cache directories
.cache
.parcel-cache
.npm
.yarn
.pnpm-store

# Temporary files
tmp/
temp/

# Coverage reports
coverage/
.nyc_output

# GitHub workflows (not needed in container)
.github/