<script setup lang="ts">
import { useMessages } from '~/composables/useMessages'
import VoiceMessage from './VoiceMessage.vue'

const { messages } = useMessages()

const messageListRef = ref<HTMLElement>()

watch(
  messages,
  () => {
    nextTick(() => {
      if (messageListRef.value) {
        messageListRef.value.scrollTo({
          top: messageListRef.value.scrollHeight,
          behavior: 'smooth',
        })
      }
    })
  },
  { deep: true },
)
</script>

<template>
  <div ref="messageListRef" class="scrollbar flex h-full flex-col gap-4 overflow-y-auto px-1">
    <div v-for="(message, index) in messages" :key="index" class="flex items-end" :class="[message.role === 'assistant' ? 'justify-start' : 'justify-end']">
      <!-- 语音消息 -->
      <VoiceMessage
        v-if="message.type === 'voice'"
        :role="message.role"
        :text="message.content"
        :audio-url="message.audioUrl"
        :audio-duration="message.audioDuration"
        :is-temp="message.isTemp"
      />

      <!-- 普通文本消息 -->
      <div
        v-else
        class="relative max-w-[85%] rounded-2xl px-4 py-3 text-sm/6"
        :class="[
          message.isTemp
            ? message.role === 'assistant'
              ? 'bg-gray-50/80 text-gray-600 backdrop-blur'
              : 'bg-indigo-500/90 text-white backdrop-blur'
            : message.role === 'assistant'
              ? 'bg-white text-gray-900 shadow-sm ring-1 ring-gray-200/50'
              : 'bg-indigo-600 text-white',
          message.role === 'assistant' ? 'rounded-tl-md' : 'rounded-tr-md',
          message.isTemp ? 'animate-pulse' : 'animate-message-in',
        ]"
      >
        <p class="break-words">{{ message.content }}</p>
        <span
          v-if="message.isTemp"
          class="dots-animation absolute -bottom-6 left-0 text-sm text-gray-400"
          :class="{ 'text-white/60': message.role === 'user' }"
        ></span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dots-animation::after {
  content: '...';
  display: inline-block;
  animation: dots 1.5s infinite;
  width: 12px;
}

@keyframes dots {
  0%,
  20% {
    content: '.';
  }
  40%,
  60% {
    content: '..';
  }
  80%,
  100% {
    content: '...';
  }
}

@keyframes message-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-message-in {
  animation: message-in 0.2s ease-out;
}
</style>
