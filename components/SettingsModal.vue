<template>
  <!-- 背景遮罩 -->
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm">
    <!-- 模态框容器 -->
    <div class="w-full max-w-2xl overflow-hidden rounded-xl bg-white shadow-2xl ring-1 ring-gray-200/50">
      <!-- 头部 -->
      <div class="relative border-b border-gray-100 bg-gradient-to-r from-gray-50 to-blue-50/30 px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-500/10">
              <SettingsIcon class="h-5 w-5 text-blue-600" />
            </div>
            <h2 class="text-lg font-semibold text-gray-800">面试设置</h2>
          </div>
          <UiButton @click="$emit('close')" variant="ghost" size="xs" :icon="XMarkIcon" class="!h-8 !w-8 !px-0" />
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="max-h-[80vh] overflow-y-auto p-6">
        <div class="space-y-8">
          <!-- 计时器控制区域 -->
          <div class="space-y-4">
            <div class="flex items-center gap-2">
              <div class="flex h-6 w-6 items-center justify-center rounded-full bg-amber-500/10">
                <ClockIcon class="h-4 w-4 text-amber-600" />
              </div>
              <h3 class="font-medium text-gray-800">计时器控制</h3>
            </div>

            <div class="space-y-3">
              <!-- 时间显示 -->
              <div class="flex items-center justify-between rounded-xl border border-gray-200/60 bg-gradient-to-r from-gray-50/50 to-amber-50/30 p-4">
                <div class="flex items-center gap-3">
                  <div class="flex h-10 w-10 items-center justify-center rounded-lg bg-amber-500/10">
                    <ClockIcon class="h-5 w-5 text-amber-600" />
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-800">当前时间</p>
                    <p class="text-xs text-gray-500">面试进行时间</p>
                  </div>
                </div>
                <div class="rounded-lg bg-white/80 px-3 py-2 font-mono text-lg font-semibold text-gray-800 shadow-sm">
                  {{ timerValue }}
                </div>
              </div>

              <!-- 控制按钮 -->
              <div class="grid grid-cols-2 gap-3">
                <UiButton @click="isTimerRunning ? pauseTimer() : startTimer()" variant="outline" size="sm" :icon="isTimerRunning ? PauseIcon : PlayIcon">
                  {{ isTimerRunning ? '暂停计时' : '开始计时' }}
                </UiButton>
                <UiButton @click="resetTimer()" variant="outline" size="sm" :icon="ArrowPathIcon"> 重置计时 </UiButton>
              </div>
            </div>
          </div>

          <!-- 语音设置区域 -->
          <div class="space-y-5">
            <div class="flex items-center gap-2">
              <div class="flex h-6 w-6 items-center justify-center rounded-full bg-purple-500/10">
                <SpeakerWaveIcon class="h-4 w-4 text-purple-600" />
              </div>
              <h3 class="font-medium text-gray-800">语音设置</h3>
            </div>

            <div class="grid gap-6 md:grid-cols-2">
              <!-- 音色选择 -->
              <div class="space-y-3">
                <label class="block text-sm font-medium text-gray-700">
                  <div class="flex items-center gap-2">
                    <UserIcon class="h-4 w-4 text-gray-500" />
                    音色选择
                  </div>
                </label>
                <Listbox v-model="localTtsSettings.voiceId">
                  <div class="relative">
                    <ListboxButton
                      class="relative w-full cursor-pointer rounded-lg border border-gray-200 bg-white py-3 pl-4 pr-10 text-left text-sm transition-all hover:border-gray-300"
                    >
                      <span class="block truncate">{{ getVoiceLabel(localTtsSettings.voiceId) }}</span>
                      <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <ChevronDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
                      </span>
                    </ListboxButton>
                    <transition
                      enter-active-class="transition duration-200 ease-out"
                      enter-from-class="transform scale-95 opacity-0"
                      enter-to-class="transform scale-100 opacity-100"
                      leave-active-class="transition duration-150 ease-in"
                      leave-from-class="transform scale-100 opacity-100"
                      leave-to-class="transform scale-95 opacity-0"
                    >
                      <ListboxOptions
                        class="absolute z-30 mt-1 max-h-60 w-full overflow-auto rounded-lg border border-gray-200 bg-white py-1 shadow-lg focus:outline-none"
                      >
                        <ListboxOption
                          v-for="voice in voiceOptions"
                          :key="voice.value"
                          :value="voice.value"
                          v-slot="{ active, selected }"
                          class="relative cursor-pointer select-none"
                        >
                          <div
                            :class="[
                              active ? 'bg-blue-50 text-blue-700' : 'text-gray-700',
                              'relative flex items-center gap-3 py-2 pl-4 pr-4 transition-colors',
                            ]"
                          >
                            <span class="text-lg">{{ voice.emoji }}</span>
                            <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">
                              {{ voice.label }}
                            </span>
                            <CheckIcon v-if="selected" class="ml-auto h-4 w-4 text-blue-600" />
                          </div>
                        </ListboxOption>
                      </ListboxOptions>
                    </transition>
                  </div>
                </Listbox>
              </div>

              <!-- 情绪选择 -->
              <div class="space-y-3">
                <label class="block text-sm font-medium text-gray-700">
                  <div class="flex items-center gap-2">
                    <FaceSmileIcon class="h-4 w-4 text-gray-500" />
                    情绪风格
                  </div>
                </label>
                <Listbox v-model="localTtsSettings.emotion">
                  <div class="relative">
                    <ListboxButton
                      class="relative w-full cursor-pointer rounded-lg border border-gray-200 bg-white py-3 pl-4 pr-10 text-left text-sm transition-all hover:border-gray-300"
                    >
                      <span class="block truncate">{{ getEmotionLabel(localTtsSettings.emotion) }}</span>
                      <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <ChevronDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
                      </span>
                    </ListboxButton>
                    <transition
                      enter-active-class="transition duration-200 ease-out"
                      enter-from-class="transform scale-95 opacity-0"
                      enter-to-class="transform scale-100 opacity-100"
                      leave-active-class="transition duration-150 ease-in"
                      leave-from-class="transform scale-100 opacity-100"
                      leave-to-class="transform scale-95 opacity-0"
                    >
                      <ListboxOptions
                        class="absolute z-30 mt-1 max-h-60 w-full overflow-auto rounded-lg border border-gray-200 bg-white py-1 shadow-lg focus:outline-none"
                      >
                        <ListboxOption
                          v-for="emotion in emotionOptions"
                          :key="emotion.value"
                          :value="emotion.value"
                          v-slot="{ active, selected }"
                          class="relative cursor-pointer select-none"
                        >
                          <div
                            :class="[
                              active ? 'bg-blue-50 text-blue-700' : 'text-gray-700',
                              'relative flex items-center gap-3 py-2 pl-4 pr-4 transition-colors',
                            ]"
                          >
                            <span class="text-lg">{{ emotion.emoji }}</span>
                            <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">
                              {{ emotion.label }}
                            </span>
                            <CheckIcon v-if="selected" class="ml-auto h-4 w-4 text-blue-600" />
                          </div>
                        </ListboxOption>
                      </ListboxOptions>
                    </transition>
                  </div>
                </Listbox>
              </div>
            </div>

            <!-- 滑块控制区域 -->
            <div class="space-y-6">
              <!-- 语速控制 -->
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <label class="flex items-center gap-2 text-sm font-medium text-gray-700">
                    <ForwardIcon class="h-4 w-4 text-gray-500" />
                    语速调节
                  </label>
                  <span class="rounded-md bg-blue-50 px-2 py-1 text-sm font-medium text-blue-700">{{ localTtsSettings.speed }}x</span>
                </div>
                <div class="space-y-2">
                  <input
                    v-model.number="localTtsSettings.speed"
                    type="range"
                    min="0.5"
                    max="2.0"
                    step="0.1"
                    class="speed-slider h-2 w-full cursor-pointer appearance-none rounded-lg bg-gray-200"
                  />
                  <div class="relative flex justify-between text-xs text-gray-500">
                    <span class="flex-1">0.5x 慢速</span>
                    <span class="relative flex-1 -translate-x-[2em]">1.0x 正常</span>
                    <span class="flex flex-1 justify-end">2.0x 快速</span>
                  </div>
                </div>
              </div>

              <!-- 音量控制 -->
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <label class="flex items-center gap-2 text-sm font-medium text-gray-700">
                    <SpeakerWaveIcon class="h-4 w-4 text-gray-500" />
                    音量调节
                  </label>
                  <span class="rounded-md bg-green-50 px-2 py-1 text-sm font-medium text-green-700">{{ Math.round(localTtsSettings.volume * 100) }}%</span>
                </div>
                <div class="space-y-2">
                  <input
                    v-model.number="localTtsSettings.volume"
                    type="range"
                    min="0.1"
                    max="2.0"
                    step="0.1"
                    class="volume-slider h-2 w-full cursor-pointer appearance-none rounded-lg bg-gray-200"
                  />
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>10% 轻声</span>
                    <span>100% 正常</span>
                    <span>200% 增强</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 模型质量选择 -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                <div class="flex items-center gap-2">
                  <CpuChipIcon class="h-4 w-4 text-gray-500" />
                  模型质量
                </div>
              </label>
              <div class="grid grid-cols-2 gap-3">
                <label v-for="model in modelOptions" :key="model.value" class="relative cursor-pointer">
                  <input v-model="localTtsSettings.model" :value="model.value" type="radio" class="peer sr-only" />
                  <div
                    class="rounded-lg border-2 border-gray-200 bg-white p-3 transition-all hover:border-gray-300 peer-checked:border-blue-500 peer-checked:bg-blue-50"
                  >
                    <div class="flex items-center gap-2">
                      <div class="text-sm font-medium text-gray-800">{{ model.label }}</div>
                      <div v-if="model.recommended" class="rounded-full bg-orange-100 px-2 py-0.5 text-xs font-medium text-orange-700">推荐</div>
                    </div>
                    <div class="mt-1 text-xs text-gray-500">{{ model.description }}</div>
                  </div>
                </label>
              </div>
            </div>

            <!-- 测试按钮 -->
            <div class="pt-2">
              <UiButton
                @click="testTTS"
                :disabled="isTesting"
                :loading="isTesting"
                variant="info"
                size="md"
                full-width
                :icon="!isTesting ? PlayIcon : undefined"
              >
                {{ isTesting ? '正在测试语音...' : '测试语音效果' }}
              </UiButton>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作区 -->
      <div class="border-t border-gray-100 bg-gray-50/30 px-6 py-4">
        <div class="flex gap-3">
          <UiButton @click="resetToDefaults" variant="outline" size="md" :icon="ArrowPathIcon" class="flex-1"> 恢复默认 </UiButton>
          <UiButton @click="saveAndClose" variant="primary" size="md" :icon="CheckIcon" class="flex-1"> 保存设置 </UiButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  XMarkIcon,
  ClockIcon,
  ArrowPathIcon,
  ChevronDownIcon,
  SpeakerWaveIcon,
  UserIcon,
  FaceSmileIcon,
  ForwardIcon,
  CpuChipIcon,
  CheckIcon,
} from '@heroicons/vue/24/outline'
import { PlayIcon, PauseIcon } from '@heroicons/vue/24/solid'
import { Listbox, ListboxButton, ListboxOptions, ListboxOption } from '@headlessui/vue'
import SettingsIcon from '@/assets/icons/settings-icon.svg'
import { ref, reactive, computed } from 'vue'

const emit = defineEmits(['close', 'updateTtsSettings'])

const props = defineProps<{
  timerValue: string
  isTimerRunning: boolean
  startTimer: () => void
  pauseTimer: () => void
  resetTimer: () => void
  ttsSettings?: {
    model?: string
    voiceId?: string
    speed?: number
    volume?: number
    emotion?: string
  }
}>()

// 音色选项配置
const voiceOptions = [
  { value: 'female-tianmei', label: '亲和型面试官（女）', emoji: '👩‍💼' },
  { value: 'female-yujie', label: '资深面试官（女）', emoji: '👩‍💻' },
  { value: 'female-chengshu', label: '专业面试官（女）', emoji: '👩‍🎓' },
  { value: 'female-shaonv', label: '温和面试官（女）', emoji: '👩‍🏫' },
  { value: 'male-qn-jingying', label: '专业面试官（男）', emoji: '👨‍💼' },
  { value: 'male-qn-qingse', label: '友善面试官（男）', emoji: '👨‍🎓' },
  { value: 'presenter_female', label: '标准面试官（女）', emoji: '🎯' },
  { value: 'presenter_male', label: '标准面试官（男）', emoji: '🎯' },
]

// 情绪选项配置
const emotionOptions = [
  { value: 'happy', label: '愉快', emoji: '😊' },
  { value: 'neutral', label: '中性', emoji: '😐' },
  { value: 'sad', label: '悲伤', emoji: '😢' },
  { value: 'angry', label: '愤怒', emoji: '😠' },
  { value: 'surprised', label: '惊讶', emoji: '😲' },
  { value: 'fearful', label: '恐惧', emoji: '😨' },
  { value: 'disgusted', label: '厌恶', emoji: '🤢' },
]

// 模型选项配置
const modelOptions = [
  {
    value: 'speech-02-hd',
    label: '高清模型',
    description: '高质量语音，推荐使用',
    recommended: true,
  },
  {
    value: 'speech-02-turbo',
    label: '快速模型',
    description: '快速生成，适合实时场景',
    recommended: false,
  },
  {
    value: 'speech-01-hd',
    label: '标准高清',
    description: '标准质量语音',
    recommended: false,
  },
  {
    value: 'speech-01-turbo',
    label: '标准快速',
    description: '基础快速生成',
    recommended: false,
  },
]

// 本地 TTS 设置状态
const localTtsSettings = reactive({
  model: props.ttsSettings?.model || 'speech-02-hd',
  voiceId: props.ttsSettings?.voiceId || 'male-qn-jingying',
  speed: props.ttsSettings?.speed || 1.0,
  volume: props.ttsSettings?.volume || 1.0,
  emotion: props.ttsSettings?.emotion || 'happy',
})

const isTesting = ref(false)

// 默认设置
const defaultSettings = {
  model: 'speech-02-hd',
  voiceId: 'male-qn-jingying',
  speed: 1.0,
  volume: 1.0,
  emotion: 'happy',
}

// 获取音色标签
const getVoiceLabel = (voiceId: string) => {
  const voice = voiceOptions.find(v => v.value === voiceId)
  return voice ? `${voice.emoji} ${voice.label}` : voiceId
}

// 获取情绪标签
const getEmotionLabel = (emotion: string) => {
  const emotionOption = emotionOptions.find(e => e.value === emotion)
  return emotionOption ? `${emotionOption.emoji} ${emotionOption.label}` : emotion
}

// 测试 TTS 功能
const testTTS = async () => {
  isTesting.value = true
  try {
    const testText = '您好，这是语音测试效果展示。我是您的面试官，很高兴与您进行这次面试。'

    const response = await $fetch('/api/tts/minimax', {
      method: 'POST',
      body: {
        text: testText,
        model: localTtsSettings.model,
        voice_id: localTtsSettings.voiceId,
        speed: localTtsSettings.speed,
        volume: localTtsSettings.volume,
        emotion: localTtsSettings.emotion,
        format: 'mp3',
        sample_rate: 32000,
        bitrate: 128000,
        channel: 1,
        language_boost: 'Chinese',
      },
      responseType: 'blob',
    })

    if (response instanceof Blob) {
      // 播放测试音频
      const audioUrl = URL.createObjectURL(response)
      const audio = new Audio(audioUrl)

      audio.onended = () => {
        URL.revokeObjectURL(audioUrl)
      }

      await audio.play()
    }
  } catch (error) {
    console.error('TTS 测试失败:', error)
    // 这里可以添加 toast 提示
    alert('语音测试失败，请检查网络连接或稍后重试')
  } finally {
    isTesting.value = false
  }
}

// 恢复默认设置
const resetToDefaults = () => {
  Object.assign(localTtsSettings, defaultSettings)
}

// 保存设置并关闭
const saveAndClose = () => {
  emit('updateTtsSettings', { ...localTtsSettings })
  emit('close')
}
</script>

<style scoped>
/* 自定义滑块样式 */
.speed-slider::-webkit-slider-thumb {
  appearance: none;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

.speed-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.speed-slider::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(90deg, #dbeafe 0%, #3b82f6 50%, #1e40af 100%);
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981, #047857);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
  transition: all 0.2s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.volume-slider::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(90deg, #d1fae5 0%, #10b981 50%, #047857 100%);
}

/* Firefox 滑块样式 */
.speed-slider,
.volume-slider {
  background: transparent;
}

.speed-slider::-moz-range-thumb,
.volume-slider::-moz-range-thumb {
  border: none;
  height: 18px;
  width: 18px;
  border-radius: 50%;
  cursor: pointer;
}

.speed-slider::-moz-range-thumb {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.volume-slider::-moz-range-thumb {
  background: linear-gradient(135deg, #10b981, #047857);
}

/* 动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.fixed > div {
  animation: slideIn 0.2s ease-out;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .max-w-2xl {
    max-width: calc(100vw - 2rem);
    margin: 1rem;
  }

  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .md\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}

/* 滚动条美化 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 移除选择后的 focus 状态 */
.voice-dropdown:focus,
.emotion-dropdown:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* HeadlessUI 特定的 focus 状态重置 */
[role='option']:focus,
[role='listbox']:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* 选择完成后移除按钮的 focus */
button[role='button']:not(:focus-visible) {
  outline: none;
  box-shadow: none;
}

/* 自定义下拉框滚动条样式 */
.voice-dropdown::-webkit-scrollbar,
.emotion-dropdown::-webkit-scrollbar {
  width: 4px; /* 更细的滚动条 */
}

.voice-dropdown::-webkit-scrollbar-track,
.emotion-dropdown::-webkit-scrollbar-track {
  background: #f8fafc; /* 浅灰色轨道 */
  border-radius: 2px;
  margin: 8px 0; /* 顶部和底部留白 */
}

.voice-dropdown::-webkit-scrollbar-thumb,
.emotion-dropdown::-webkit-scrollbar-thumb {
  background: #cbd5e1; /* 柔和的灰色滑块 */
  border-radius: 2px;
  transition: all 0.2s ease; /* 平滑过渡 */
}
</style>
