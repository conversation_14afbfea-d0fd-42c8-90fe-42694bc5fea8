<template>
  <div class="relative py-8">
    <div class="overflow-hidden">
      <div class="flex transition-all duration-700 ease-out" :style="{ transform: `translateX(-${currentTranslate}%)` }">
        <div v-for="(testimonial, index) in extendedTestimonials" :key="`testimonial-${index}`" class="flex w-full flex-shrink-0 justify-center px-4">
          <div
            class="my-4 w-full max-w-2xl transform rounded-2xl bg-white p-8 shadow-lg transition-all duration-500 hover:scale-[1.02]"
            :class="{ 'animate-fadeIn': isVisible }"
            :style="index >= visibleRange.start && index <= visibleRange.end ? `animation-delay: ${0.3 * (index - visibleRange.start)}s` : ''"
          >
            <div class="mb-6 text-lg italic leading-relaxed text-gray-700">"{{ testimonial.content }}"</div>
            <div class="flex items-center">
              <div class="flex h-12 w-12 items-center justify-center rounded-full font-bold text-white shadow-md" :class="getAvatarColor(index)">
                {{ testimonial.author.charAt(0) }}
              </div>
              <div class="ml-4">
                <div class="font-bold text-gray-800">{{ testimonial.author }}</div>
                <div class="text-sm text-gray-600">{{ testimonial.position }}，{{ testimonial.company }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <button
      @click="prevSlide"
      @mouseenter="isPrevHovered = true"
      @mouseleave="isPrevHovered = false"
      class="absolute left-4 top-1/2 z-10 flex h-10 w-10 -translate-y-1/2 items-center justify-center rounded-full bg-white/90 shadow-lg backdrop-blur-sm transition-all duration-300 hover:bg-white"
      :class="{ 'scale-110': isPrevHovered }"
      aria-label="上一个"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-6 w-6 text-gray-700 transition-transform duration-300"
        :class="{ 'scale-90': isPrevHovered }"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
      </svg>
    </button>
    <button
      @click="nextSlide"
      @mouseenter="isNextHovered = true"
      @mouseleave="isNextHovered = false"
      class="absolute right-4 top-1/2 z-10 flex h-10 w-10 -translate-y-1/2 items-center justify-center rounded-full bg-white/90 shadow-lg backdrop-blur-sm transition-all duration-300 hover:bg-white"
      :class="{ 'scale-110': isNextHovered }"
      aria-label="下一个"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-6 w-6 text-gray-700 transition-transform duration-300"
        :class="{ 'scale-90': isNextHovered }"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
      </svg>
    </button>

    <div class="mt-8 flex justify-center gap-3">
      <button
        v-for="(_, index) in testimonials"
        :key="index"
        @click="goToSlide(index)"
        @mouseenter="hoverIndex = index"
        @mouseleave="hoverIndex = -1"
        class="h-2.5 w-2.5 rounded-full transition-all duration-300"
        :class="[
          currentIndex === index ? 'scale-125 bg-gradient-to-r from-purple-500 to-pink-500 shadow-sm' : 'bg-gray-300',
          hoverIndex === index ? 'scale-110' : '',
        ]"
        :aria-label="`切换到第${index + 1}个评价`"
      ></button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Testimonial {
  content: string
  author: string
  position: string
  company: string
}

const props = defineProps({
  testimonials: {
    type: Array as PropType<Testimonial[]>,
    required: true,
  },
})

const currentIndex = ref(0)
const isPrevHovered = ref(false)
const isNextHovered = ref(false)
const hoverIndex = ref(-1)
const isVisible = ref(false)
const isTransitioning = ref(false)
const currentTranslate = ref(100) // 初始位置在第一个真实项目

const extendedTestimonials = computed(() => {
  if (!props.testimonials.length) return []
  return [...props.testimonials, ...props.testimonials, ...props.testimonials]
})

const visibleRange = computed(() => {
  const total = props.testimonials.length
  if (!total) return { start: 0, end: 0 }
  return {
    start: total,
    end: total * 2 - 1,
  }
})

const nextSlide = () => {
  if (isTransitioning.value || props.testimonials.length <= 1) return

  isTransitioning.value = true
  currentIndex.value = (currentIndex.value + 1) % props.testimonials.length

  const newPosition = (currentIndex.value + props.testimonials.length) * 100
  currentTranslate.value = newPosition

  setTimeout(() => {
    isTransitioning.value = false
  }, 700)
}

const prevSlide = () => {
  if (isTransitioning.value || props.testimonials.length <= 1) return

  isTransitioning.value = true
  currentIndex.value = (currentIndex.value - 1 + props.testimonials.length) % props.testimonials.length

  const newPosition = (currentIndex.value + props.testimonials.length) * 100
  currentTranslate.value = newPosition

  setTimeout(() => {
    isTransitioning.value = false
  }, 700)
}

const goToSlide = (index: number) => {
  if (isTransitioning.value) return

  currentIndex.value = index
  currentTranslate.value = (index + props.testimonials.length) * 100
}

let autoplayInterval: number | null = null

onMounted(() => {
  isVisible.value = true

  // 初始化位置 - 从中间部分的第一个项目开始
  currentTranslate.value = props.testimonials.length * 100

  autoplayInterval = window.setInterval(() => {
    nextSlide()
  }, 5000)
})

onUnmounted(() => {
  if (autoplayInterval) {
    clearInterval(autoplayInterval)
  }
})

const getAvatarColor = (index: number) => {
  const colors = ['bg-blue-600', 'bg-emerald-600', 'bg-amber-600', 'bg-rose-600', 'bg-indigo-600', 'bg-teal-600', 'bg-violet-600', 'bg-orange-600']
  const normalizedIndex = index % props.testimonials.length
  return colors[normalizedIndex % colors.length]
}
</script>

<style scoped>
.flex {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}
</style>
