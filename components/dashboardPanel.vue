<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50">
    <div class="px-4 py-8 sm:px-6 lg:px-8">
      <!-- 欢迎横幅 -->
      <div class="mb-8">
        <div class="relative overflow-hidden rounded-3xl bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-500 p-8 shadow-2xl">
          <!-- 装饰背景 -->
          <div class="absolute inset-0 bg-black/10"></div>
          <div class="absolute -right-40 -top-40 h-80 w-80 rounded-full bg-white/10 blur-3xl"></div>
          <div class="absolute -bottom-40 -left-40 h-80 w-80 rounded-full bg-white/10 blur-3xl"></div>

          <div class="relative z-10">
            <h1 class="text-3xl font-bold text-white sm:text-4xl">欢迎回来！ 👋</h1>
            <p class="mt-2 text-lg text-indigo-100">准备好开始你的 AI 面试练习了吗？</p>
          </div>
        </div>
      </div>

      <!-- 快速开始区域 -->
      <div class="mb-12 grid gap-8 lg:grid-cols-3">
        <!-- 主要操作卡片 -->
        <div class="lg:col-span-2">
          <div class="relative overflow-hidden rounded-2xl bg-white p-8 shadow-lg ring-1 ring-gray-900/5 transition-all hover:shadow-xl">
            <!-- 装饰元素 -->
            <div
              class="absolute right-0 top-0 h-32 w-32 -translate-y-8 translate-x-8 rounded-full bg-gradient-to-br from-indigo-100 to-purple-100 opacity-50"
            ></div>

            <div class="relative z-10">
              <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">开始模拟面试</h2>
                <p class="mt-2 text-gray-600">选择面试时长，开始你的 AI 面试体验</p>
              </div>

              <!-- 面试时长选择 -->
              <div class="mb-8 space-y-4">
                <label class="text-sm font-semibold text-gray-700">选择面试时长</label>
                <div class="grid grid-cols-2 gap-4">
                  <UiSelectionButton
                    v-for="duration in durations"
                    :key="duration.value"
                    @click="selectedDuration = duration.value"
                    :selected="selectedDuration === duration.value"
                    size="lg"
                    variant="default"
                    class="!p-4"
                  >
                    <div class="text-2xl font-bold">{{ duration.value }}</div>
                    <div class="text-sm">分钟</div>
                  </UiSelectionButton>
                </div>
              </div>

              <!-- 开始面试按钮 -->
              <UiButton @click="startInterview" :icon="PlayIcon" size="lg" full-width> 开始模拟面试 </UiButton>
            </div>
          </div>
        </div>

        <!-- 快速统计卡片 -->
        <div class="space-y-6">
          <!-- 今日面试 -->
          <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-50 p-6 shadow-lg ring-1 ring-blue-200/50">
            <!-- 原始内容 -->
            <div class="flex items-center gap-3 opacity-40">
              <div class="rounded-xl bg-blue-100 p-3">
                <DocumentTextIcon class="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <div class="text-sm font-medium text-blue-700">今日面试</div>
                <div class="text-2xl font-bold text-blue-800">{{ stats.todayInterviews || 0 }}</div>
              </div>
            </div>

            <!-- 即将上线覆盖层 -->
            <div
              class="absolute inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-blue-600/10 backdrop-blur-[1px]"
            >
              <div class="text-center">
                <div
                  class="mb-2 inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-lg"
                >
                  <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L8.107 10.5a.75.75 0 00-1.214 1.029l1.5 2.25a.75.75 0 001.214-.094l3.75-5.25z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  即将上线
                </div>
                <p class="text-xs font-medium text-blue-700">今日面试统计</p>
              </div>
            </div>
          </div>

          <!-- 总面试次数 -->
          <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 to-violet-50 p-6 shadow-lg ring-1 ring-purple-200/50">
            <!-- 原始内容 -->
            <div class="flex items-center gap-3 opacity-40">
              <div class="rounded-xl bg-purple-100 p-3">
                <ChartBarIcon class="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <div class="text-sm font-medium text-purple-700">总面试次数</div>
                <div class="text-2xl font-bold text-purple-800">{{ stats.totalInterviews || 0 }}</div>
              </div>
            </div>

            <!-- 即将上线覆盖层 -->
            <div
              class="absolute inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-purple-500/5 via-violet-500/5 to-purple-600/10 backdrop-blur-[1px]"
            >
              <div class="text-center">
                <div
                  class="mb-2 inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-purple-500 to-violet-600 px-4 py-2 text-sm font-semibold text-white shadow-lg"
                >
                  <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L8.107 10.5a.75.75 0 00-1.214 1.029l1.5 2.25a.75.75 0 001.214-.094l3.75-5.25z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  即将上线
                </div>
                <p class="text-xs font-medium text-purple-700">面试次数统计</p>
              </div>
            </div>
          </div>

          <!-- 平均得分 -->
          <!-- <div class="rounded-2xl bg-gradient-to-br from-emerald-50 to-green-50 p-6 shadow-lg ring-1 ring-emerald-200/50">
            <div class="flex items-center gap-3">
              <div class="rounded-xl bg-emerald-100 p-3">
                <StarIcon class="h-6 w-6 text-emerald-600" />
              </div>
              <div>
                <div class="text-sm font-medium text-emerald-700">平均得分</div>
                <div class="text-2xl font-bold text-emerald-800">{{ stats.averageScore || '-' }}</div>
              </div>
            </div>
          </div> -->
        </div>
      </div>

      <!-- 右侧：继续上次面试或学习资源 -->
      <!-- <div class="relative overflow-hidden rounded-xl bg-gradient-to-br from-indigo-50 via-white to-purple-50 p-8">
            <div class="relative z-10">
              <template v-if="lastInterview">
                <div class="mb-6">
                  <span
                    class="inline-flex items-center gap-1.5 rounded-full bg-gradient-to-r from-rose-500/10 to-pink-500/10 px-3 py-1.5 text-xs font-medium text-rose-600 shadow-sm ring-1 ring-rose-500/20 transition-all"
                  >
                    <span class="relative flex h-2 w-2">
                      <span class="absolute inline-flex h-full w-full animate-ping rounded-full bg-rose-400 opacity-75"></span>
                      <span class="relative inline-flex h-2 w-2 rounded-full bg-rose-500"></span>
                    </span>
                    未完成面试
                  </span>
                  <h3 class="mt-3 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-xl font-bold text-transparent">继续上次面试</h3>
                </div>

                <div class="mb-8 space-y-4">
                  <div class="rounded-lg bg-white/80 p-4 shadow-sm backdrop-blur">
                    <div class="mb-4 space-y-3">
                      <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">岗位</span>
                        <span class="font-medium text-gray-900">{{ getPositionName(lastInterview.positionId) }}</span>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">进度</span>
                        <div class="flex items-center gap-2">
                          <div class="h-2 w-24 overflow-hidden rounded-full bg-gray-100">
                            <div
                              class="h-full rounded-full bg-gradient-to-r from-indigo-500 to-purple-500"
                              :style="{ width: `${lastInterview.progress}%` }"
                            ></div>
                          </div>
                          <span class="text-sm font-medium text-gray-900">{{ lastInterview.progress }}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <button
                  @click="continueInterview"
                  class="group relative w-full overflow-hidden rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 px-4 py-2.5 text-sm font-medium text-white shadow-sm transition-all hover:shadow-lg"
                >
                  <span class="relative z-10 flex items-center justify-center gap-2">
                    继续面试
                    <ArrowRightIcon class="h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </span>
                </button>
              </template>

              <template v-else>
                <div class="mb-6">
                  <span
                    class="inline-flex items-center gap-1.5 rounded-full bg-gradient-to-r from-emerald-500/10 to-teal-500/10 px-3 py-1.5 text-xs font-medium text-emerald-600 shadow-sm ring-1 ring-emerald-500/20 transition-all"
                  >
                    <span class="relative flex h-2 w-2">
                      <span class="absolute inline-flex h-full w-full animate-ping rounded-full bg-emerald-400 opacity-75"></span>
                      <span class="relative inline-flex h-2 w-2 rounded-full bg-emerald-500"></span>
                    </span>
                    今日推荐
                  </span>
                  <h3 class="mt-3 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-xl font-bold text-transparent">提升面试技能</h3>
                </div>

                <div class="mb-8 space-y-4">
                  <div class="rounded-lg bg-white/80 p-4 shadow-sm backdrop-blur transition-all hover:shadow-md">
                    <h4 class="mb-3 text-sm font-medium text-gray-900">热门岗位技能</h4>
                    <div class="flex flex-wrap gap-2">
                      <span
                        v-for="skill in hotSkills"
                        :key="skill"
                        class="rounded-full bg-gradient-to-r from-indigo-50 to-purple-50 px-3 py-1 text-xs font-medium text-indigo-600"
                      >
                        {{ skill }}
                      </span>
                    </div>
                  </div>

                  <div class="group cursor-pointer rounded-lg bg-white/80 p-4 shadow-sm backdrop-blur transition-all hover:shadow-md">
                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="text-sm font-medium text-gray-900">系统设计进阶课程</h4>
                        <p class="mt-1 text-xs text-gray-500">4.9 分 · 2000+ 人学习</p>
                      </div>
                      <div class="rounded-full bg-indigo-50 p-2 text-indigo-600 transition-all group-hover:bg-indigo-100">
                        <PlayIcon class="h-4 w-4" />
                      </div>
                    </div>
                  </div>
                </div>

                <button
                  @click="router.push('/learning-center')"
                  class="group relative w-full overflow-hidden rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 px-4 py-2.5 text-sm font-medium text-white shadow-sm transition-all hover:shadow-lg"
                >
                  <span class="relative z-10 flex items-center justify-center gap-2">
                    探索更多资源
                    <ArrowRightIcon class="h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </span>
                </button>
              </template>
            </div>
            <div
              class="absolute right-0 top-0 -z-10 h-64 w-64 -translate-y-1/3 translate-x-1/3 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 blur-3xl"
            ></div>
            <div
              class="absolute bottom-0 left-0 -z-10 h-64 w-64 -translate-x-1/3 translate-y-1/3 bg-gradient-to-tr from-purple-200/20 to-indigo-200/20 blur-3xl"
            ></div>
          </div> -->

      <!-- 面试记录和数据分析 -->
      <div class="grid gap-8 lg:grid-cols-3">
        <!-- 最近面试记录 -->
        <div class="lg:col-span-2">
          <div class="group relative overflow-hidden rounded-2xl bg-white shadow-lg ring-1 ring-gray-900/5">
            <!-- 原始内容 - 半透明显示 -->
            <div class="p-8 opacity-30">
              <div class="mb-6 flex items-center justify-between">
                <div>
                  <h2 class="text-xl font-bold text-gray-900">最近面试记录</h2>
                  <p class="text-sm text-gray-600">查看你的面试历史和表现</p>
                </div>
                <UiButton @click="fetchDashboardData" variant="ghost" size="xs" :icon="ArrowPathIcon" class="!p-2" />
              </div>

              <div class="space-y-4">
                <!-- 模拟面试记录项 -->
                <div class="rounded-xl border border-gray-200/50 bg-gradient-to-r from-white to-gray-50/50 p-5">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                      <div class="rounded-xl bg-indigo-50 p-3">
                        <DocumentTextIcon class="h-5 w-5 text-indigo-500" />
                      </div>
                      <div>
                        <div class="mb-1 flex items-center gap-2">
                          <h3 class="font-semibold text-gray-900">前端开发</h3>
                        </div>
                        <div class="flex items-center gap-4 text-sm text-gray-500">
                          <div class="flex items-center gap-1">
                            <ClockIcon class="h-4 w-4" />
                            2024年1月15日 14:30
                          </div>
                          <div class="flex items-center gap-1">
                            <span>时长: 30分钟</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center gap-4">
                      <div class="text-right">
                        <div class="flex items-center gap-1">
                          <StarIcon class="h-4 w-4 text-amber-400" />
                          <span class="text-lg font-bold text-gray-900">85</span>
                          <span class="text-sm text-gray-500">分</span>
                        </div>
                      </div>
                      <ArrowRightIcon class="h-5 w-5 text-gray-400" />
                    </div>
                  </div>
                </div>

                <!-- 第二个模拟记录 -->
                <div class="rounded-xl border border-gray-200/50 bg-gradient-to-r from-white to-gray-50/50 p-5">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                      <div class="rounded-xl bg-purple-50 p-3">
                        <ChartBarIcon class="h-5 w-5 text-purple-500" />
                      </div>
                      <div>
                        <div class="mb-1 flex items-center gap-2">
                          <h3 class="font-semibold text-gray-900">后端开发</h3>
                        </div>
                        <div class="flex items-center gap-4 text-sm text-gray-500">
                          <div class="flex items-center gap-1">
                            <ClockIcon class="h-4 w-4" />
                            2024年1月14日 10:15
                          </div>
                          <div class="flex items-center gap-1">
                            <span>时长: 15分钟</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center gap-4">
                      <div class="text-right">
                        <div class="flex items-center gap-1">
                          <StarIcon class="h-4 w-4 text-amber-400" />
                          <span class="text-lg font-bold text-gray-900">92</span>
                          <span class="text-sm text-gray-500">分</span>
                        </div>
                      </div>
                      <ArrowRightIcon class="h-5 w-5 text-gray-400" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 即将上线覆盖层 -->
            <div
              class="absolute inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-indigo-500/5 via-purple-500/5 to-pink-500/5 backdrop-blur-[2px]"
            >
              <div class="px-8 py-12 text-center">
                <div
                  class="mb-6 inline-flex items-center gap-3 rounded-2xl bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 px-8 py-4 text-white shadow-2xl ring-1 ring-white/30"
                >
                  <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L8.107 10.5a.75.75 0 00-1.214 1.029l1.5 2.25a.75.75 0 001.214-.094l3.75-5.25z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  <span class="text-lg font-bold">面试记录即将上线</span>
                </div>
                <p class="mb-2 text-base font-medium text-indigo-700">完整的面试历史和数据分析功能</p>
                <p class="text-sm text-indigo-600/80">敬请期待更强大的面试体验</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据洞察 -->
        <div class="space-y-6">
          <!-- 学习建议 -->
          <div class="rounded-2xl bg-gradient-to-br from-violet-50 to-purple-50 p-6 shadow-lg ring-1 ring-violet-200/50">
            <div class="mb-4">
              <h3 class="text-lg font-bold text-violet-900">学习建议</h3>
              <p class="text-sm text-violet-700">基于你的面试表现</p>
            </div>
            <div class="space-y-3">
              <div v-for="(tip, index) in improvementTips.slice(0, 3)" :key="index" class="flex items-start gap-3">
                <LightBulbIcon class="mt-0.5 h-5 w-5 flex-shrink-0 text-violet-600" />
                <p class="text-sm leading-relaxed text-violet-800">{{ tip }}</p>
              </div>
            </div>
          </div>

          <!-- 面试技巧 -->
          <div class="rounded-2xl bg-gradient-to-br from-emerald-50 to-green-50 p-6 shadow-lg ring-1 ring-emerald-200/50">
            <div class="mb-4">
              <h3 class="text-lg font-bold text-emerald-900">面试技巧</h3>
              <p class="text-sm text-emerald-700">帮助你更好地表现</p>
            </div>
            <div class="space-y-3">
              <div v-for="(tip, index) in interviewTips.slice(0, 3)" :key="index" class="flex items-start gap-3">
                <CheckCircleIcon class="mt-0.5 h-5 w-5 flex-shrink-0 text-emerald-600" />
                <p class="text-sm leading-relaxed text-emerald-800">{{ tip }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  LightBulbIcon,
  CheckCircleIcon,
  PlayIcon,
  ArrowRightIcon,
  ArrowPathIcon,
  ClockIcon,
  StarIcon,
  DocumentIcon,
  DocumentTextIcon,
  ChartBarIcon,
} from '@heroicons/vue/24/solid'
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'nuxt/app'
import { useInterviewStore } from '@/stores/interview'
import type { InterviewType } from '~/types/interview'
import { Line as LineChart, Doughnut as DoughnutChart } from 'vue-chartjs'
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement } from 'chart.js'

const router = useRouter()
const interviewStore = useInterviewStore()
const { recentInterviews, stats, isLoading, error, fetchDashboardData } = useDashboardData()
const { checkCreditsAndExecute } = useCreditsGuard()

const userInfo = ref({
  interviewCount: 24,
  bestScore: 92,
  practiceHours: 16,
})

// 岗位数据
const positions = [
  { id: 'frontend', name: '前端开发' },
  { id: 'backend', name: '后端开发' },
  { id: 'fullstack', name: '全栈开发' },
  { id: 'algorithm', name: '算法开发' },
]

// 时长选项
const durations = [
  { value: 15, label: '15分钟' },
  { value: 30, label: '30分钟' },
]

// 响应式状态
const selectedPosition = ref('frontend')
const selectedDuration = ref(15)

const lastInterview = ref({
  positionId: 'dev',
  progress: 60,
})

const improvementTips = ['建议加强项目经验的表述，多使用数据支撑', '可以准备更多关于团队协作的具体案例', '技术面试中需要注意算法思维的训练']

const interviewTips = ['STAR法则：用具体的情境、任务、行动、结果来组织回答', '准备充分的问题来展示对公司的了解和兴趣', '注意非语言沟通，保持积极的肢体语言']

const hotSkills = ['React', 'Vue.js', 'Node.js', 'System Design', 'TypeScript', 'Microservices']

// 在组件挂载时获取数据
onMounted(() => {
  fetchDashboardData()
})

// 工具函数
const getPositionName = (id: string) => {
  return positions.find(p => p.id === id)?.name || id
}

const formatDate = (date: string | Date | number) => {
  let dateObj: Date
  if (typeof date === 'number') {
    // 如果是时间戳（秒），转换为毫秒
    dateObj = new Date(date * 1000)
  } else if (typeof date === 'string') {
    dateObj = new Date(date)
  } else {
    dateObj = date
  }

  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(dateObj)
}

const startInterview = async () => {
  if (!selectedPosition.value) return

  await checkCreditsAndExecute(async () => {
    const interviewType = selectedPosition.value as InterviewType
    interviewStore.setType(interviewType)
    interviewStore.startInterview()
    router.push('/prepare-interview')
  })
}

const continueInterview = () => {
  // 实现继续面试逻辑
  router.push('/interview?continue=true')
}

const getPositionIcon = (type: string) => {
  switch (type) {
    case 'frontend':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />`
    case 'backend':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />`
    case 'fullstack':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />`
    case 'algorithm':
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />`
    default:
      return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />`
  }
}

// 获取图标组件
const getPositionIconComponent = (type: string) => {
  switch (type) {
    case 'frontend':
      return DocumentTextIcon
    case 'backend':
      return ChartBarIcon
    case 'fullstack':
      return StarIcon
    case 'algorithm':
      return LightBulbIcon
    default:
      return DocumentIcon
  }
}

const getPositionIconColor = (type: string) => {
  switch (type) {
    case 'frontend':
      return 'text-rose-500 bg-rose-50'
    case 'backend':
      return 'text-emerald-500 bg-emerald-50'
    case 'fullstack':
      return 'text-violet-500 bg-violet-50'
    case 'algorithm':
      return 'text-amber-500 bg-amber-50'
    default:
      return 'text-indigo-500 bg-indigo-50'
  }
}

// 注册 ChartJS 组件
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, ArcElement)

// 面试得分趋势数据
const scoreChartData = computed(() => ({
  labels: stats.value.scoresTrend.labels,
  datasets: [
    {
      label: '面试得分',
      data: stats.value.scoresTrend.data,
      borderColor: 'rgb(99, 102, 241)',
      backgroundColor: 'rgba(99, 102, 241, 0.1)',
      tension: 0.4,
      fill: true,
    },
  ],
}))

const scoreChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      max: 100,
    },
  },
}

// 岗位分布数据
const positionDistribution = computed(() => stats.value.positionDistribution)

const positionChartData = computed(() => ({
  labels: positionDistribution.value.map(item => item.name),
  datasets: [
    {
      data: positionDistribution.value.map(item => item.percentage),
      backgroundColor: positionDistribution.value.map(item => {
        const colorMap = {
          'bg-indigo-500': '#6366f1',
          'bg-emerald-500': '#10b981',
          'bg-violet-500': '#8b5cf6',
          'bg-amber-500': '#f59e0b',
        }
        return colorMap[item.color as keyof typeof colorMap] || '#94a3b8'
      }),
      borderWidth: 0,
    },
  ],
}))

const positionChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
}
</script>
