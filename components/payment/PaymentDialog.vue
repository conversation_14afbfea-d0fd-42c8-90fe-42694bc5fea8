<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" class="relative z-50" @close="handleClose">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
              <!-- 顶部装饰条 -->
              <div class="absolute inset-x-0 top-0 h-1.5 rounded-t-2xl bg-gradient-to-r from-purple-600/20 via-purple-600/40 to-pink-500/20"></div>

              <!-- 关闭按钮 -->
              <button class="absolute right-4 top-4 z-10 text-gray-400 hover:text-gray-600" @click="handleClose">
                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  />
                </svg>
              </button>

              <div class="p-8">
                <!-- 扫码支付阶段 -->
                <div v-if="currentStage === 'pending'" class="text-center">
                  <h3 class="text-3xl font-semibold text-purple-700">微信扫码支付</h3>
                  <p class="mt-3 text-gray-600">解锁 AI 助手全部功能，加速你的职业发展</p>

                  <div class="mt-10 flex flex-col items-center">
                    <!-- QR码容器 -->
                    <div class="relative">
                      <!-- 虚线框和星星装饰 -->
                      <div class="absolute -inset-3">
                        <div class="relative h-full w-full">
                          <!-- 虚线边框 -->
                          <div class="absolute inset-0 rounded-lg border-[1.5px] border-dashed border-purple-200"></div>
                          <!-- 左上星星 -->
                          <div class="absolute -left-1 -top-1 -translate-x-full -translate-y-full">
                            <svg class="h-6 w-6 text-purple-400" viewBox="0 0 24 24" fill="none">
                              <path d="M12 2L14.4 7.2L20 8.4L16 12.4L17.2 18L12 15.2L6.8 18L8 12.4L4 8.4L9.6 7.2L12 2Z" fill="currentColor" />
                            </svg>
                          </div>
                          <!-- 右下星星 -->
                          <div class="absolute -bottom-1 -right-1 translate-x-full translate-y-full">
                            <svg class="h-6 w-6 text-purple-400" viewBox="0 0 24 24" fill="none">
                              <path d="M12 2L14.4 7.2L20 8.4L16 12.4L17.2 18L12 15.2L6.8 18L8 12.4L4 8.4L9.6 7.2L12 2Z" fill="currentColor" />
                            </svg>
                          </div>
                        </div>
                      </div>

                      <!-- QR码或占位图 -->
                      <div class="relative h-48 w-48 overflow-hidden rounded-lg bg-gray-50">
                        <template v-if="isLoading">
                          <!-- QR码占位图 -->
                          <div class="flex h-full w-full flex-wrap items-center justify-center gap-2 p-8">
                            <div
                              v-for="i in 9"
                              :key="i"
                              class="h-4 w-4 rounded-sm bg-purple-600 transition-all duration-700"
                              :class="{
                                'opacity-20': [2, 4, 5, 7].includes(i),
                                'animate-pulse-delayed': i % 2 === 0,
                                'animate-pulse': i % 2 !== 0,
                              }"
                              :style="{ animationDelay: `${i * 0.1}s` }"
                            ></div>
                          </div>
                        </template>
                        <template v-else>
                          <img :src="qrCode" alt="微信支付二维码" class="h-full w-full object-cover" />
                        </template>
                      </div>
                    </div>

                    <!-- 添加加载状态或倒计时显示 -->
                    <div class="mt-6 flex items-center space-x-2 text-sm text-gray-500">
                      <svg class="h-4 w-4 animate-spin text-purple-500" viewBox="0 0 24 24" fill="none">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path
                          class="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      <template v-if="isLoading">
                        <span class="font-medium text-gray-400">正在加载支付二维码...</span>
                      </template>
                      <template v-else>
                        <span class="font-medium text-gray-400">支付倒计时:</span>
                        <span class="font-medium text-purple-600">{{ formatTime(remainingTime) }}</span>
                      </template>
                    </div>
                    <p class="mt-3 text-sm text-gray-500">{{ props.plan.name }} - 限时优惠 ¥{{ props.plan.price }}</p>
                    <p class="mt-2 text-xs text-purple-500">获得 {{ props.plan.credits }} AI Credits</p>
                  </div>
                </div>

                <!-- 支付处理中阶段 -->
                <div v-else-if="currentStage === 'processing'" class="relative py-12 text-center">
                  <!-- 装饰性背景元素 -->
                  <div class="absolute inset-0 overflow-hidden">
                    <!-- 渐变背景圆圈 -->
                    <div class="absolute -left-10 -top-10 h-40 w-40 rounded-full bg-gradient-to-br from-purple-100/40 to-pink-100/40 blur-[32px]"></div>
                    <div class="absolute -bottom-10 -right-10 h-40 w-40 rounded-full bg-gradient-to-br from-purple-100/40 to-pink-100/40 blur-[32px]"></div>

                    <!-- 动态装饰元素 -->
                    <div class="animate-float absolute left-4 top-4">
                      <svg class="h-6 w-6 text-purple-300" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2L14.4 7.2L20 8.4L16 12.4L17.2 18L12 15.2L6.8 18L8 12.4L4 8.4L9.6 7.2L12 2Z" />
                      </svg>
                    </div>
                    <div class="animate-float-delayed absolute bottom-4 right-4">
                      <svg class="h-6 w-6 text-pink-300" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2L14.4 7.2L20 8.4L16 12.4L17.2 18L12 15.2L6.8 18L8 12.4L4 8.4L9.6 7.2L12 2Z" />
                      </svg>
                    </div>
                  </div>

                  <!-- 主要内容区域 -->
                  <div class="relative">
                    <!-- 动画加载图标 -->
                    <div class="mx-auto flex justify-center">
                      <div class="relative">
                        <!-- 外圈脉冲动画 -->
                        <div class="absolute -inset-x-8 -inset-y-4">
                          <div class="h-full w-full animate-pulse rounded-2xl bg-purple-100/60"></div>
                        </div>
                        <!-- 信封动画容器 -->
                        <div class="relative flex h-32 w-32 items-center justify-center">
                          <!-- 信封主体 -->
                          <div class="envelope-container">
                            <!-- 信封盖子 -->
                            <div class="envelope-flap"></div>
                            <!-- 信封正面 -->
                            <div class="envelope-front">
                              <!-- 信的内容 -->
                              <div class="letter">
                                <svg class="h-8 w-8 text-purple-600" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 文字内容区域 -->
                    <div class="mt-8 space-y-3">
                      <h3 class="text-2xl font-semibold text-purple-700">支付成功！</h3>
                      <div class="flex justify-center space-x-1">
                        <div v-for="i in 5" :key="i" class="text-yellow-400">
                          <svg class="h-5 w-5 animate-bounce" :style="{ animationDelay: `${i * 0.1}s` }" viewBox="0 0 20 20" fill="currentColor">
                            <path
                              d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                            />
                          </svg>
                        </div>
                      </div>
                      <p class="mt-2 text-sm font-medium text-purple-500">每一次支持，都是创意旅程的新起点</p>
                    </div>
                  </div>
                </div>

                <!-- 支付成功阶段 -->
                <div v-else-if="currentStage === 'success'" class="relative py-8 text-center">
                  <div class="mt-4 flex justify-center">
                    <div class="relative">
                      <div class="flex h-24 w-24 items-center justify-center rounded-full bg-gradient-to-br from-purple-50 to-purple-100">
                        <svg class="h-12 w-12 text-purple-600" viewBox="0 0 24 24" fill="none">
                          <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <h3 class="mt-6 text-2xl font-semibold text-purple-700">充值成功</h3>
                  <div class="mt-2 flex justify-center">
                    <div class="flex space-x-1">
                      <div v-for="i in 5" :key="i" class="text-yellow-400">
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path
                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <p class="mt-4 text-gray-600">已获得 {{ props.plan.credits }} AI Credits</p>
                  <p class="mt-2 text-sm text-purple-500">让我们开始探索 Hi-Offer 助手的所有功能</p>

                  <div class="mt-8">
                    <button
                      class="w-full rounded-full bg-purple-600 px-8 py-3 text-sm font-medium text-white shadow-md transition-all hover:bg-purple-700 hover:shadow-lg hover:shadow-purple-600/20"
                      @click="handlePaymentSuccess"
                    >
                      开始使用
                    </button>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, onUnmounted, onMounted, watch } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import type { Ref } from 'vue'
import { useFireworks } from '~/composables/useFirework'
import type { ApiResponse } from '~/types/api/common'
import { useToast } from 'vue-toast-notification'

type PlanKey = 'basic-credit' | 'pro-credit' | 'max-credit'
type PaymentStage = 'pending' | 'processing' | 'success'

interface Plan {
  name: string
  key: PlanKey
  price: number
  credits: number
}

interface Props {
  isOpen: boolean
  plan: Plan
}

interface CreateOrderResponse {
  img: string
  trade_no: string
  o_id: string
}

interface QueryOrderResponse {
  status: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'processing'): void
  (e: 'success'): void
  (e: 'timeout'): void
}>()

const POLLING_INTERVAL = 3000 // 轮询间隔：3秒
const MAX_POLLING_TIME = 15 * 60 * 1000 // 最大轮询时间：15分钟
const POLLING_COUNT_BEFORE_INCREASE = 10 // 多少次轮询后增加间隔
const MAX_POLLING_INTERVAL = 10000 // 最大轮询间隔：10秒

const qrCode: Ref<string> = ref('')
const orderId: Ref<string> = ref('')
const isLoading: Ref<boolean> = ref(true)
const error: Ref<string | null> = ref(null)
const remainingTime: Ref<number> = ref(MAX_POLLING_TIME / 1000) // 剩余时间（秒）
const currentPollingInterval: Ref<number> = ref(POLLING_INTERVAL)
const currentStage: Ref<PaymentStage> = ref('pending')

let pollingTimer: ReturnType<typeof setInterval> | null = null
let timeoutTimer: ReturnType<typeof setTimeout> | null = null
let countdownTimer: ReturnType<typeof setInterval> | null = null
let pollingCount = 0

const toast = useToast()

const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const createOrder = async () => {
  try {
    isLoading.value = true
    error.value = null
    pollingCount = 0
    currentPollingInterval.value = POLLING_INTERVAL
    remainingTime.value = MAX_POLLING_TIME / 1000

    const { data, error: fetchError } = await useFetch<ApiResponse<CreateOrderResponse>>('/api/payment/zpay/create', {
      method: 'POST',
      body: {
        planKey: props.plan.key,
      },
      onResponseError({ response }) {
        throw new Error(response._data?.message || '创建订单失败')
      },
    })

    if (fetchError.value) {
      throw fetchError.value
    }

    if (!data.value) {
      throw new Error('创建订单失败：未获取到响应数据')
    }

    // 检查响应格式
    if (!data.value.success || data.value.error) {
      throw new Error(data.value.errorMessage || '创建订单失败')
    }

    const responseData = data.value.data
    if (!responseData?.img || !responseData?.trade_no) {
      throw new Error('创建订单失败：响应数据格式错误')
    }

    qrCode.value = responseData.img
    orderId.value = responseData.trade_no
    startPolling()
    startTimeout()
  } catch (e) {
    toast.error('创建订单失败，请重试')
  } finally {
    isLoading.value = false
  }
}

const queryOrder = async () => {
  const { data } = await useFetch<ApiResponse<QueryOrderResponse>>('/api/payment/zpay/query', {
    method: 'POST',
    body: {
      outTradeNo: orderId.value,
    },
  })
  // 如果查询成功，立即停止所有定时器
  if (data.value?.success) {
    stopAll()
    toProcessStage()
    return
  }
  // 如果查询失败，继续轮询并增加间隔
  pollingCount++
  if (pollingCount > POLLING_COUNT_BEFORE_INCREASE) {
    currentPollingInterval.value = Math.min(currentPollingInterval.value * 1.5, MAX_POLLING_INTERVAL)
  }
}

const startPolling = () => {
  if (pollingTimer) return
  // 立即执行一次查询
  queryOrder()
  pollingTimer = setInterval(() => {
    queryOrder()
  }, currentPollingInterval.value)
}

const startTimeout = () => {
  if (timeoutTimer) return
  // 设置倒计时
  countdownTimer = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--
    } else {
      handleTimeout()
    }
  }, 1000)
  // 设置总超时
  timeoutTimer = setTimeout(() => {
    handleTimeout()
  }, MAX_POLLING_TIME)
}

const handleTimeout = () => {
  stopAll()
  toast.error('支付超时，请重新尝试')
  emit('timeout')
}

const stopAll = () => {
  // 停止轮询
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
  // 停止超时计时器
  if (timeoutTimer) {
    clearTimeout(timeoutTimer)
    timeoutTimer = null
  }
  // 停止倒计时
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

const handleClose = () => {
  stopAll()
  emit('close')
}

const handlePaymentSuccess = () => {
  stopAll()
  emit('success')
}

const toProcessStage = () => {
  currentStage.value = 'processing'
  setTimeout(() => {
    currentStage.value = 'success'
  }, 3000)
}

//打开支付对话框时，创建订单
onMounted(() => {
  if (props.isOpen) {
    createOrder()
  }
})

// 支付成功阶段，触发烟花效果
watch(currentStage, newStage => {
  if (newStage === 'success') {
    const { launchFireworks } = useFireworks()
    launchFireworks()
  }
})

onUnmounted(() => {
  stopAll()
})
</script>
<style scoped>
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 3s ease-in-out infinite;
  animation-delay: 1.5s;
}

.animate-spin-slow {
  animation: spin 3s linear infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 优化过渡动画 */
.dialog-enter-active,
.dialog-leave-active {
  transition: all 0.3s ease-out;
}

.dialog-enter-from,
.dialog-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.animate-pulse {
  animation: pulse 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-pulse-delayed {
  animation: pulse 2.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation-delay: 0.5s;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.95);
  }
}

/* 信封动画相关样式 */
.envelope-container {
  position: relative;
  width: 100px;
  height: 70px;
  background: linear-gradient(45deg, #f3e7ff 30%, #e9d5ff 90%);
  border-radius: 4px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  transform-style: preserve-3d;
  perspective: 1000px;
}

.envelope-flap {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #f3e7ff 30%, #e9d5ff 90%);
  clip-path: polygon(0 0, 50% 50%, 100% 0);
  transform-origin: top;
  animation: openFlap 1.5s ease-in-out forwards;
  z-index: 2;
}

.envelope-front {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #f3e7ff 30%, #e9d5ff 90%);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.letter {
  width: 80%;
  height: 80%;
  background: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: riseLetter 1.5s ease-in-out forwards;
  animation-delay: 0.75s;
  transform: translateY(100%);
  box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1);
}

@keyframes openFlap {
  0% {
    transform: rotateX(0deg);
  }
  100% {
    transform: rotateX(180deg);
  }
}

@keyframes riseLetter {
  0% {
    transform: translateY(100%);
  }
  60% {
    transform: translateY(-10%);
  }
  80% {
    transform: translateY(5%);
  }
  100% {
    transform: translateY(0);
  }
}

/* 添加装饰性闪光效果 */
.envelope-front::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, transparent 40%, rgba(255, 255, 255, 0.2) 45%, transparent 50%, transparent);
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  20%,
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}
</style>
