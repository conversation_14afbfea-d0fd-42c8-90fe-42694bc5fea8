<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useGlobalAudio } from '../composables/useGlobalAudio'

const globalAudio = useGlobalAudio()

interface Props {
  audioUrl?: string
  text: string
  role: 'assistant' | 'user'
  isTemp?: boolean
  audioDuration?: number
}

const props = defineProps<Props>()
const isTextVisible = ref(false)
const isPlaying = ref(false)
const audio = ref<HTMLAudioElement | null>(null)

// 生成随机UUID
const generateUUID = () => {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID()
  }
  return Math.random().toString(36).substring(2) + Date.now().toString(36) + Math.random().toString(36).substring(2)
}

const audioId = ref(generateUUID())

onMounted(() => {
  if (props.audioUrl) {
    audio.value = new Audio(props.audioUrl)
    audio.value.addEventListener('ended', () => {
      isPlaying.value = false
      if (globalAudio.currentPlayingAudio.value?.id === audioId.value) {
        globalAudio.currentPlayingAudio.value = null
      }
    })
  }
})

onUnmounted(() => {
  if (audio.value) {
    audio.value.pause()
    audio.value.removeEventListener('ended', () => {})
    if (globalAudio.currentPlayingAudio.value?.id === audioId.value) {
      globalAudio.currentPlayingAudio.value = null
    }
  }
})

const togglePlay = () => {
  if (!audio.value) return

  if (isPlaying.value) {
    audio.value.pause()
    audio.value.currentTime = 0
    isPlaying.value = false
    if (globalAudio.currentPlayingAudio.value?.id === audioId.value) {
      globalAudio.currentPlayingAudio.value = null
    }
  } else {
    audio.value.currentTime = 0
    audio.value.play()
    isPlaying.value = true
    globalAudio.setCurrentAudio(audio.value, audioId.value)
  }
}

// 监听全局音频状态变化
watch(
  globalAudio.currentPlayingAudio,
  newVal => {
    if (newVal?.id !== audioId.value && isPlaying.value) {
      isPlaying.value = false
      if (audio.value) {
        audio.value.pause()
      }
    }
  },
  { deep: true },
)

const toggleText = () => {
  isTextVisible.value = !isTextVisible.value
}
</script>

<template>
  <div class="relative max-w-[85%]" :class="[role === 'assistant' ? 'items-start' : 'items-end']">
    <!-- 语音消息气泡 -->
    <div
      class="group flex cursor-pointer flex-col rounded-2xl"
      :class="[
        isTemp
          ? role === 'assistant'
            ? 'bg-gray-50/80 text-gray-600 backdrop-blur'
            : 'bg-indigo-500/90 text-white backdrop-blur'
          : role === 'assistant'
            ? 'bg-white text-gray-900 shadow-sm ring-1 ring-gray-200/50'
            : 'bg-indigo-600 text-white',
        role === 'assistant' ? 'rounded-tl-md' : 'rounded-tr-md',
        isTemp ? 'animate-pulse' : 'animate-message-in',
      ]"
    >
      <!-- 语音部分 -->
      <div class="flex items-center gap-2 px-4 py-3" @click="togglePlay">
        <!-- 语音图标 -->
        <div class="relative flex h-5 w-5 items-center justify-center">
          <div v-if="isPlaying" class="absolute inset-0">
            <div class="voice-wave">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          <div v-else class="i-carbon-volume-up text-lg" :class="{ 'opacity-60': !isPlaying }"></div>
        </div>

        <!-- 语音时长 -->
        <span class="text-sm">{{ audioDuration ?? 0 }}"</span>
      </div>

      <!-- 文字内容 -->
      <div
        v-if="text && isTextVisible"
        class="border-t px-4 py-2 text-sm"
        :class="[role === 'assistant' ? 'border-gray-100 text-gray-600' : 'border-indigo-500 text-white/90']"
      >
        {{ text }}
      </div>
    </div>

    <!-- 翻译按钮 -->
    <div v-if="text" class="mt-1 flex items-center gap-1" :class="[role === 'assistant' ? 'justify-start' : 'justify-end']">
      <button
        @click="toggleText"
        class="flex items-center gap-1 rounded-md px-2 py-1 text-xs text-gray-500 transition-colors hover:bg-gray-100/10 hover:text-gray-700"
      >
        <div class="i-carbon-translate text-sm"></div>
        <span>{{ isTextVisible ? '收起译文' : '查看译文' }}</span>
      </button>
    </div>
  </div>
</template>

<style scoped>
.voice-wave {
  display: flex;
  align-items: center;
  gap: 1px;
  height: 100%;
}

.voice-wave span {
  display: inline-block;
  width: 2px;
  height: 100%;
  background-color: currentColor;
  animation: wave 1.2s ease-in-out infinite;
}

.voice-wave span:nth-child(2) {
  animation-delay: 0.2s;
}

.voice-wave span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes wave {
  0%,
  100% {
    height: 30%;
  }
  50% {
    height: 100%;
  }
}

@keyframes message-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-message-in {
  animation: message-in 0.2s ease-out;
}
</style>
