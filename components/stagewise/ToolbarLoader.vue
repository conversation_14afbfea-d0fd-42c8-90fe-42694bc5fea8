<script setup lang="ts">
import type { ToolbarConfig } from '@stagewise/toolbar'
import ToolbarWrapper from './ToolbarWrapper.vue'
const config = useRuntimeConfig()
const isToolbarEnabled = Boolean(config.public.stagewiseToolbarEnabled)
const stagewiseConfig: ToolbarConfig = {
  plugins: [],
}
</script>

<template>
  <ClientOnly>
    <ToolbarWrapper v-if="isToolbarEnabled" :config="stagewiseConfig" />
  </ClientOnly>
</template>
