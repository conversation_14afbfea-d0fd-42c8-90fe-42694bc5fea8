<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
    <div class="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
      <div class="mb-4 flex items-center justify-between">
        <h2 class="text-xl font-semibold text-gray-800">结束面试</h2>
        <UiButton @click="$emit('close')" variant="ghost" size="xs" :icon="XMarkIcon" class="!p-1" />
      </div>

      <div class="mb-6">
        <p class="text-gray-600">
          确定要结束当前面试吗？面试时长：<span class="font-medium">{{ timerValue }}</span>
        </p>
        <p class="mt-2 text-sm text-gray-500">结束后将保存面试记录，并返回到面试列表页面。</p>
      </div>

      <div class="flex gap-3">
        <UiButton @click="$emit('close')" variant="outline" size="sm" class="flex-1"> 取消 </UiButton>
        <UiButton @click="$emit('confirm')" variant="danger" size="sm" class="flex-1"> 确认结束 </UiButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { XMarkIcon } from '@heroicons/vue/24/outline'

defineEmits(['close', 'confirm'])

defineProps<{
  timerValue: string
}>()
</script>
