<script setup lang="ts">
import { computed } from 'vue'

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info' | 'outline' | 'ghost'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  loading?: boolean
  icon?: any
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
}

const props = withDefaults(defineProps<ButtonProps>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  iconPosition: 'left',
  fullWidth: false,
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => {
  const baseClasses = [
    'group relative overflow-hidden font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none',
  ]

  // Size classes
  const sizeClasses = {
    xs: 'px-2 py-1 text-xs rounded-md',
    sm: 'px-3 py-1.5 text-sm rounded-lg',
    md: 'px-4 py-2 text-sm rounded-lg',
    lg: 'px-6 py-3 text-base rounded-xl',
    xl: 'px-8 py-4 text-lg rounded-xl',
  }

  // Variant classes
  const variantClasses = {
    primary: [
      'bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-500 text-white shadow-lg',
      'hover:scale-[1.02] hover:shadow-xl active:scale-[0.98]',
      'focus:ring-indigo-500',
    ],
    secondary: [
      'bg-gradient-to-r from-slate-600 to-slate-700 text-white shadow-lg',
      'hover:from-slate-700 hover:to-slate-800 hover:shadow-xl',
      'focus:ring-slate-500',
    ],
    success: [
      'bg-gradient-to-r from-emerald-500 to-green-600 text-white shadow-lg',
      'hover:from-emerald-600 hover:to-green-700 hover:shadow-xl',
      'focus:ring-emerald-500',
    ],
    warning: [
      'bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg',
      'hover:from-amber-600 hover:to-orange-600 hover:shadow-xl',
      'focus:ring-amber-500',
    ],
    danger: ['bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg', 'hover:from-red-600 hover:to-pink-600 hover:shadow-xl', 'focus:ring-red-500'],
    info: ['bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg', 'hover:from-blue-600 hover:to-cyan-600 hover:shadow-xl', 'focus:ring-blue-500'],
    outline: ['bg-transparent border-2 border-indigo-500 text-indigo-600', 'hover:bg-indigo-50 hover:border-indigo-600', 'focus:ring-indigo-500'],
    ghost: ['bg-transparent text-indigo-600', 'hover:bg-indigo-50', 'focus:ring-indigo-500'],
  }

  // Width classes
  const widthClasses = props.fullWidth ? 'w-full' : ''

  return [...baseClasses, sizeClasses[props.size], ...variantClasses[props.variant], widthClasses].filter(Boolean).join(' ')
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<template>
  <button :class="buttonClasses" :disabled="disabled || loading" @click="handleClick">
    <span class="relative z-10 flex items-center justify-center gap-2">
      <!-- Loading spinner -->
      <div v-if="loading" class="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>

      <!-- Left icon -->
      <component v-if="icon && iconPosition === 'left' && !loading" :is="icon" class="h-5 w-5" />

      <!-- Slot content -->
      <slot />

      <!-- Right icon -->
      <component v-if="icon && iconPosition === 'right' && !loading" :is="icon" class="h-5 w-5" />
    </span>

    <!-- Hover light effect (only for gradient variants) -->
    <div
      v-if="['primary', 'secondary', 'success', 'warning', 'danger', 'info'].includes(variant)"
      class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"
    ></div>

    <!-- Shimmer effect (only for gradient variants) -->
    <div
      v-if="['primary', 'secondary', 'success', 'warning', 'danger', 'info'].includes(variant)"
      class="absolute inset-0 translate-x-[-100%] bg-gradient-to-r from-transparent via-white/20 to-transparent transition-transform duration-700 group-hover:translate-x-[100%]"
    ></div>
  </button>
</template>
