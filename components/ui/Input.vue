<script setup lang="ts">
import { computed, useAttrs } from 'vue'

export interface InputProps {
  modelValue?: string | number
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  maxlength?: number
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  label?: string
  description?: string
  error?: string
  showCount?: boolean
  icon?: any
  iconPosition?: 'left' | 'right'
  required?: boolean
}

const props = withDefaults(defineProps<InputProps>(), {
  type: 'text',
  variant: 'default',
  size: 'md',
  iconPosition: 'left',
  showCount: false,
  required: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  input: [event: Event]
}>()

const attrs = useAttrs()

const inputValue = computed({
  get: () => props.modelValue || '',
  set: value => emit('update:modelValue', value),
})

const inputClasses = computed(() => {
  const baseClasses = [
    'block w-full rounded-2xl border-0 bg-white transition-all duration-300',
    'placeholder:text-gray-400 disabled:cursor-not-allowed disabled:opacity-50',
    'focus:outline-none focus:ring-2',
  ]

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-sm sm:px-5 sm:py-4 sm:text-base',
    lg: 'px-5 py-4 text-base sm:px-6 sm:py-5 sm:text-lg',
  }

  // Variant classes with proper focus colors
  const variantClasses = {
    default: [
      'text-gray-900 shadow-lg ring-2 ring-gray-200/50',
      'hover:shadow-xl hover:ring-gray-300/50',
      'focus:shadow-xl focus:shadow-gray-500/20 focus:ring-gray-500',
    ],
    primary: [
      'text-gray-900 shadow-lg ring-2 ring-gray-200/50',
      'hover:shadow-xl hover:ring-indigo-300/50',
      'focus:shadow-xl focus:shadow-indigo-500/20 focus:ring-indigo-500',
    ],
    secondary: [
      'text-gray-900 shadow-lg ring-2 ring-gray-200/50',
      'hover:shadow-xl hover:ring-purple-300/50',
      'focus:shadow-xl focus:shadow-purple-500/20 focus:ring-purple-500',
    ],
    success: [
      'text-gray-900 shadow-lg ring-2 ring-gray-200/50',
      'hover:shadow-xl hover:ring-green-300/50',
      'focus:shadow-xl focus:shadow-green-500/20 focus:ring-green-500',
    ],
    warning: [
      'text-gray-900 shadow-lg ring-2 ring-gray-200/50',
      'hover:shadow-xl hover:ring-amber-300/50',
      'focus:shadow-xl focus:shadow-amber-500/20 focus:ring-amber-500',
    ],
    danger: [
      'text-gray-900 shadow-lg ring-2 ring-red-200/50',
      'hover:shadow-xl hover:ring-red-300/50',
      'focus:shadow-xl focus:shadow-red-500/20 focus:ring-red-500',
    ],
  }

  // Error state overrides
  if (props.error) {
    return [
      ...baseClasses,
      sizeClasses[props.size],
      'text-gray-900 shadow-lg ring-2 ring-red-200/50',
      'hover:shadow-xl hover:ring-red-300/50',
      'focus:shadow-xl focus:shadow-red-500/20 focus:ring-red-500',
    ].join(' ')
  }

  return [...baseClasses, sizeClasses[props.size], ...variantClasses[props.variant]].join(' ')
})

const labelClasses = computed(() => {
  const baseClasses = 'flex items-center gap-2 text-base font-semibold'

  if (props.error) {
    return `${baseClasses} text-red-700`
  }

  return `${baseClasses} text-gray-800`
})

const dotColor = computed(() => {
  const colors = {
    default: 'from-gray-500 to-gray-600',
    primary: 'from-indigo-500 to-purple-500',
    secondary: 'from-purple-500 to-pink-500',
    success: 'from-green-500 to-emerald-500',
    warning: 'from-amber-500 to-orange-500',
    danger: 'from-red-500 to-pink-500',
  }

  return props.error ? 'from-red-500 to-pink-500' : colors[props.variant]
})

const currentLength = computed(() => {
  return String(inputValue.value).length
})

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.value)
  emit('input', event)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}
</script>

<template>
  <div class="group space-y-3">
    <!-- Label -->
    <label v-if="label" :for="attrs.id as string" :class="labelClasses">
      <div class="h-2 w-2 rounded-full bg-gradient-to-r" :class="dotColor"></div>
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <!-- Input Container -->
    <div class="relative">
      <!-- Left Icon -->
      <div v-if="icon && iconPosition === 'left'" class="absolute left-3 top-1/2 -translate-y-1/2 transform">
        <component :is="icon" class="h-5 w-5 text-gray-400" />
      </div>

      <!-- Input Field -->
      <input
        :value="inputValue"
        :type="type"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :class="[
          inputClasses,
          icon && iconPosition === 'left' ? 'pl-10' : '',
          icon && iconPosition === 'right' ? 'pr-10' : '',
          showCount && maxlength ? 'pr-16' : '',
        ]"
        v-bind="attrs"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      />

      <!-- Right Icon -->
      <div v-if="icon && iconPosition === 'right'" class="absolute right-3 top-1/2 -translate-y-1/2 transform">
        <component :is="icon" class="h-5 w-5 text-gray-400" />
      </div>

      <!-- Character Count -->
      <div v-if="showCount && maxlength" class="absolute right-3 top-1/2 -translate-y-1/2 transform text-xs text-gray-400 sm:right-4">
        {{ currentLength }}/{{ maxlength }}
      </div>
    </div>

    <!-- Description -->
    <p v-if="description && !error" class="text-sm text-gray-600">
      {{ description }}
    </p>

    <!-- Error Message -->
    <p v-if="error" class="text-sm text-red-600">
      {{ error }}
    </p>
  </div>
</template>
