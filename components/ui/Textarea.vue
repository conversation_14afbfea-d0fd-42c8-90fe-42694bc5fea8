<script setup lang="ts">
import { computed, useAttrs } from 'vue'

export interface TextareaProps {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  maxlength?: number
  rows?: number
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  label?: string
  description?: string
  error?: string
  showCount?: boolean
  showProgress?: boolean
  required?: boolean
  resize?: boolean
}

const props = withDefaults(defineProps<TextareaProps>(), {
  variant: 'default',
  size: 'md',
  rows: 4,
  showCount: false,
  showProgress: false,
  required: false,
  resize: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  input: [event: Event]
}>()

const attrs = useAttrs()

const textareaValue = computed({
  get: () => props.modelValue || '',
  set: value => emit('update:modelValue', value),
})

const textareaClasses = computed(() => {
  const baseClasses = [
    'block w-full rounded-2xl border-0 bg-white transition-all duration-300',
    'placeholder:text-gray-400 disabled:cursor-not-allowed disabled:opacity-50',
    'focus:outline-none focus:ring-2',
    props.resize ? 'resize-y' : 'resize-none',
  ]

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-sm sm:px-5 sm:py-4 sm:text-base',
    lg: 'px-5 py-4 text-base sm:px-6 sm:py-5 sm:text-lg',
  }

  // Variant classes with proper focus colors
  const variantClasses = {
    default: [
      'text-gray-900 shadow-lg ring-2 ring-gray-200/50',
      'hover:shadow-xl hover:ring-gray-300/50',
      'focus:shadow-xl focus:shadow-gray-500/20 focus:ring-gray-500',
    ],
    primary: [
      'text-gray-900 shadow-lg ring-2 ring-gray-200/50',
      'hover:shadow-xl hover:ring-indigo-300/50',
      'focus:shadow-xl focus:shadow-indigo-500/20 focus:ring-indigo-500',
    ],
    secondary: [
      'text-gray-900 shadow-lg ring-2 ring-gray-200/50',
      'hover:shadow-xl hover:ring-purple-300/50',
      'focus:shadow-xl focus:shadow-purple-500/20 focus:ring-purple-500',
    ],
    success: [
      'text-gray-900 shadow-lg ring-2 ring-gray-200/50',
      'hover:shadow-xl hover:ring-green-300/50',
      'focus:shadow-xl focus:shadow-green-500/20 focus:ring-green-500',
    ],
    warning: [
      'text-gray-900 shadow-lg ring-2 ring-gray-200/50',
      'hover:shadow-xl hover:ring-amber-300/50',
      'focus:shadow-xl focus:shadow-amber-500/20 focus:ring-amber-500',
    ],
    danger: [
      'text-gray-900 shadow-lg ring-2 ring-red-200/50',
      'hover:shadow-xl hover:ring-red-300/50',
      'focus:shadow-xl focus:shadow-red-500/20 focus:ring-red-500',
    ],
  }

  // Error state overrides
  if (props.error) {
    return [
      ...baseClasses,
      sizeClasses[props.size],
      'text-gray-900 shadow-lg ring-2 ring-red-200/50',
      'hover:shadow-xl hover:ring-red-300/50',
      'focus:shadow-xl focus:shadow-red-500/20 focus:ring-red-500',
    ].join(' ')
  }

  return [...baseClasses, sizeClasses[props.size], ...variantClasses[props.variant]].join(' ')
})

const labelClasses = computed(() => {
  const baseClasses = 'flex items-center gap-2 text-base font-semibold'

  if (props.error) {
    return `${baseClasses} text-red-700`
  }

  return `${baseClasses} text-gray-800`
})

const dotColor = computed(() => {
  const colors = {
    default: 'from-gray-500 to-gray-600',
    primary: 'from-indigo-500 to-purple-500',
    secondary: 'from-purple-500 to-pink-500',
    success: 'from-green-500 to-emerald-500',
    warning: 'from-amber-500 to-orange-500',
    danger: 'from-red-500 to-pink-500',
  }

  return props.error ? 'from-red-500 to-pink-500' : colors[props.variant]
})

const currentLength = computed(() => {
  return String(textareaValue.value).length
})

const progressPercentage = computed(() => {
  if (!props.maxlength) return 0
  return currentLength.value / props.maxlength
})

const progressColor = computed(() => {
  if (progressPercentage.value > 0.8) return '#ef4444' // red
  if (progressPercentage.value > 0.6) return '#f59e0b' // amber
  return '#10b981' // green
})

const countTextColor = computed(() => {
  if (currentLength.value > (props.maxlength || 0) * 0.9) return 'text-red-500'
  if (currentLength.value > (props.maxlength || 0) * 0.7) return 'text-amber-500'
  return 'text-gray-500'
})

const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  emit('update:modelValue', target.value)
  emit('input', event)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}
</script>

<template>
  <div class="group space-y-3">
    <!-- Label -->
    <label v-if="label" :for="attrs.id as string" :class="labelClasses">
      <div class="h-2 w-2 rounded-full bg-gradient-to-r" :class="dotColor"></div>
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <!-- Textarea Container -->
    <div class="relative">
      <!-- Textarea Field -->
      <textarea
        :value="textareaValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :maxlength="maxlength"
        :rows="rows"
        :class="textareaClasses"
        v-bind="attrs"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      />

      <!-- Character Count with Progress Indicator -->
      <div v-if="(showCount || showProgress) && maxlength" class="absolute bottom-3 right-3 flex items-center gap-2 sm:bottom-4 sm:right-4">
        <!-- Progress Circle -->
        <div v-if="showProgress" class="flex h-5 w-5 items-center justify-center rounded-full bg-gray-100 sm:h-6 sm:w-6">
          <div
            class="relative h-3 w-3 rounded-full border-2 border-gray-300 sm:h-4 sm:w-4"
            :style="{
              background: `conic-gradient(from 0deg, ${progressColor} ${progressPercentage * 360}deg, #e5e7eb 0deg)`,
            }"
          >
            <div class="absolute inset-1 rounded-full bg-white"></div>
          </div>
        </div>

        <!-- Count Text -->
        <span v-if="showCount" class="text-xs font-medium" :class="countTextColor"> {{ currentLength }}/{{ maxlength }} </span>
      </div>
    </div>

    <!-- Description -->
    <p v-if="description && !error" class="text-sm text-gray-600">
      {{ description }}
    </p>

    <!-- Error Message -->
    <p v-if="error" class="text-sm text-red-600">
      {{ error }}
    </p>
  </div>
</template>
