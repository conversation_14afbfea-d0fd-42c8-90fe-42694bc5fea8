<script setup lang="ts">
import { Listbox, ListboxButton, ListboxOptions, ListboxOption } from '@headlessui/vue'
import { ChevronUpDownIcon } from '@heroicons/vue/16/solid'
import { CheckIcon } from '@heroicons/vue/20/solid'

const props = defineProps<{
  modelValue: string | number
  options: Array<{ value: string | number; label: string }>
  icon?: string
  label: string
  description?: string
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
}>()

const selectedOption = computed(() => props.options.find(option => option.value === props.modelValue) || props.options[0])

// 更新值的方法
const updateValue = (option: { value: string | number; label: string }) => {
  emit('update:modelValue', option.value)
}
</script>

<template>
  <div class="group flex items-center justify-between rounded-lg px-2 py-2 transition-colors hover:bg-blue-50/30">
    <label class="flex items-center font-medium text-gray-700">
      <div v-if="icon" :class="[icon, 'mr-2 text-lg text-indigo-500 transition-transform group-hover:rotate-12']"></div>
      <div>
        <div class="text-sm">{{ label }}</div>
        <div v-if="description" class="text-xs text-gray-500">{{ description }}</div>
      </div>
    </label>

    <div class="flex items-center gap-2">
      <Listbox as="div" :modelValue="selectedOption" @update:modelValue="updateValue" class="w-44">
        <div class="relative">
          <ListboxButton
            class="grid w-full cursor-default grid-cols-1 rounded-lg border-gray-300 bg-white py-1.5 pl-3 pr-2 text-left text-sm text-gray-900 shadow-sm outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600"
          >
            <span class="col-start-1 row-start-1 truncate pr-6">{{ selectedOption.label }}</span>
            <ChevronUpDownIcon class="col-start-1 row-start-1 size-5 self-center justify-self-end text-gray-500 sm:size-4" aria-hidden="true" />
          </ListboxButton>

          <transition leave-active-class="transition ease-in duration-100" leave-from-class="opacity-100" leave-to-class="opacity-0">
            <ListboxOptions
              class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-sm shadow-lg ring-1 ring-black/5 focus:outline-none"
            >
              <ListboxOption as="template" v-for="option in options" :key="option.value" :value="option" v-slot="{ active, selected }">
                <li :class="[active ? 'bg-indigo-600 text-white outline-none' : 'text-gray-900', 'relative cursor-default select-none py-2 pl-8 pr-4']">
                  <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">{{ option.label }}</span>

                  <span v-if="selected" :class="[active ? 'text-white' : 'text-indigo-600', 'absolute inset-y-0 left-0 flex items-center pl-1.5']">
                    <CheckIcon class="size-5" aria-hidden="true" />
                  </span>
                </li>
              </ListboxOption>
            </ListboxOptions>
          </transition>
        </div>
      </Listbox>
      <slot name="append"></slot>
    </div>
  </div>
</template>
