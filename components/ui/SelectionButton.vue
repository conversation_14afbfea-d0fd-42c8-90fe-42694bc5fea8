<script setup lang="ts">
import { computed } from 'vue'

export interface SelectionButtonProps {
  selected?: boolean
  disabled?: boolean
  size?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  variant?: 'default' | 'card'
}

const props = withDefaults(defineProps<SelectionButtonProps>(), {
  selected: false,
  disabled: false,
  size: 'md',
  fullWidth: false,
  variant: 'default',
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => {
  const baseClasses = [
    'relative overflow-hidden font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500',
    'disabled:opacity-50 disabled:cursor-not-allowed',
  ]

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm rounded-lg',
    md: 'px-4 py-3 text-base rounded-lg',
    lg: 'px-6 py-4 text-lg rounded-xl',
  }

  // Variant classes
  const variantClasses = {
    default: [
      'border-2 text-center',
      props.selected
        ? 'border-indigo-500 bg-indigo-50 text-indigo-700 shadow-lg shadow-indigo-500/20'
        : 'border-gray-200 bg-white text-gray-700 hover:border-indigo-300 hover:bg-indigo-50/50',
    ],
    card: [
      'border border-gray-200 text-left',
      props.selected
        ? 'border-purple-200 bg-purple-50/80 text-purple-600 shadow-sm ring-2 ring-purple-200/60'
        : 'bg-gray-100/60 text-gray-600 hover:border-purple-200 hover:bg-purple-50/80 hover:text-purple-600 hover:shadow-sm hover:ring-2 hover:ring-purple-200/60',
    ],
  }

  // Width classes
  const widthClasses = props.fullWidth ? 'w-full' : ''

  return [...baseClasses, sizeClasses[props.size], ...variantClasses[props.variant], widthClasses].filter(Boolean).join(' ')
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled) {
    emit('click', event)
  }
}
</script>

<template>
  <button :class="buttonClasses" :disabled="disabled" @click="handleClick">
    <slot />

    <!-- Selection gradient overlay (only for default variant when selected) -->
    <div v-if="variant === 'default' && selected" class="pointer-events-none absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10" />
  </button>
</template>
