<script setup lang="ts">
import { Switch } from '@headlessui/vue'

defineProps<{
  modelValue: boolean
  icon?: string
  label: string
  description?: string
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()
</script>

<template>
  <div class="group flex items-center justify-between rounded-lg px-2 py-2 transition-colors hover:bg-blue-50/30">
    <label class="flex items-center font-medium text-gray-700">
      <div v-if="icon" :class="[icon, 'mr-2 text-lg text-indigo-500']"></div>
      <div>
        <div class="text-sm">{{ label }}</div>
        <div v-if="description" class="text-xs text-gray-500">{{ description }}</div>
      </div>
    </label>
    <div class="flex h-6 items-center">
      <Switch
        :model-value="modelValue"
        @update:model-value="emit('update:modelValue', $event)"
        :class="[
          modelValue ? 'bg-gradient-to-r from-indigo-600 to-primary-500' : 'bg-gray-200',
          'relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2',
        ]"
      >
        <span class="sr-only">{{ label }}</span>
        <span
          aria-hidden="true"
          :class="[
            modelValue ? 'translate-x-5' : 'translate-x-0',
            'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
          ]"
        ></span>
      </Switch>
    </div>
  </div>
</template>
