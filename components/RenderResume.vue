<!-- components/MarkdownRenderer.vue -->
<template>
  <div class="markdown-container custom-scrollbar max-h-[850px] overflow-y-auto">
    <div class="markdown" v-html="renderedMarkdown"></div>
  </div>
</template>

<script setup lang="ts">
import MarkdownIt from 'markdown-it'

const props = defineProps<{
  markdownText: string
}>()

const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  xhtmlOut: true,
  breaks: true,
  langPrefix: 'language-',
})

const renderedMarkdown = computed(() => md.render(props.markdownText))
</script>

<style>
.markdown-container {
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    sans-serif;
  line-height: 1.6;
  color: #374151; /* text-gray-700 */
  padding-right: 4px; /* 为滚动条预留空间，从6px减小到4px */
}

/* 自定义滚动条样式 - 更细更明显的渐变 */
.custom-scrollbar::-webkit-scrollbar {
  width: 2px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f3f4f6; /* 浅灰色轨道 */
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #a855f7, #4f46e5); /* 更鲜明的紫色到靛蓝色渐变 */
  border-radius: 10px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #9333ea, #3730a3); /* 悬停时更深的颜色 */
}

/* Firefox 滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin; /* Firefox的thin比默认的更细 */
  scrollbar-color: #8b5cf6 #f3f4f6;
}

.markdown {
  /* 标题样式 */
  h1 {
    font-size: 1.875rem; /* text-3xl */
    font-weight: 700; /* font-bold */
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    color: #4338ca; /* text-indigo-700 */
    border-bottom: 1px solid #e5e7eb; /* border-gray-200 */
    padding-bottom: 0.5rem;
  }

  h2 {
    font-size: 1.5rem; /* text-2xl */
    font-weight: 600; /* font-semibold */
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: #6d28d9; /* text-purple-700 */
  }

  h3 {
    font-size: 1.25rem; /* text-xl */
    font-weight: 600; /* font-semibold */
    margin-top: 1.25rem;
    margin-bottom: 0.75rem;
    color: #7c3aed; /* text-violet-600 */
  }

  h4,
  h5,
  h6 {
    font-size: 1.125rem; /* text-lg */
    font-weight: 500; /* font-medium */
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    color: #8b5cf6; /* text-violet-500 */
  }

  /* 段落样式 */
  p {
    margin-bottom: 1rem;
    font-size: 1rem; /* text-base */
  }

  /* 列表样式 */
  ul {
    list-style-type: disc;
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  ol {
    list-style-type: decimal;
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  li {
    margin-bottom: 0.25rem;
  }

  /* 链接样式 */
  a {
    color: #6366f1; /* text-indigo-500 */
    text-decoration: none;
    border-bottom: 1px dashed #a5b4fc; /* border-indigo-300 */
    transition: all 0.2s ease;
  }

  a:hover {
    color: #4f46e5; /* text-indigo-600 */
    border-bottom: 1px solid #818cf8; /* border-indigo-400 */
  }

  /* 表格样式 */
  table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.1),
      0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  thead {
    background-color: #f3f4f6; /* bg-gray-100 */
  }

  th {
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 500;
    color: #4b5563; /* text-gray-600 */
    border-bottom: 1px solid #e5e7eb; /* border-gray-200 */
  }

  td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb; /* border-gray-200 */
  }

  tr:last-child td {
    border-bottom: none;
  }

  /* 引用样式 */
  blockquote {
    border-left: 4px solid #c4b5fd; /* border-violet-300 */
    padding-left: 1rem;
    margin-left: 0;
    margin-right: 0;
    margin-top: 1rem;
    margin-bottom: 1rem;
    font-style: italic;
    color: #6b7280; /* text-gray-500 */
    background-color: #f5f3ff; /* bg-violet-50 */
    padding: 1rem;
    border-radius: 0.375rem; /* rounded-md */
  }

  /* 代码样式 */
  code {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
    font-size: 0.875rem; /* text-sm */
    padding: 0.125rem 0.25rem;
    background-color: #f3f4f6; /* bg-gray-100 */
    border-radius: 0.25rem; /* rounded */
    color: #ef4444; /* text-red-500 */
  }

  pre {
    background-color: #f9fafb; /* bg-gray-50 */
    border-radius: 0.5rem; /* rounded-lg */
    padding: 1rem;
    overflow-x: auto;
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e5e7eb; /* border-gray-200 */
  }

  pre code {
    background-color: transparent;
    padding: 0;
    color: #374151; /* text-gray-700 */
  }

  /* 水平线样式 */
  hr {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
    border: 0;
    height: 1px;
    background: linear-gradient(to right, rgba(99, 102, 241, 0.1), rgba(99, 102, 241, 0.5), rgba(99, 102, 241, 0.1));
  }

  /* 强调样式 */
  strong {
    font-weight: 600;
    color: #111827; /* text-gray-900 */
  }

  em {
    font-style: italic;
  }
}
</style>
