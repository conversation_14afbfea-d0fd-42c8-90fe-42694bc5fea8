<script setup lang="ts">
import { ref, reactive } from 'vue'
import InterviewSettingsForm from '~/components/interview-prep/InterviewSettingsForm.vue'

const interviewSettings = reactive({
  duration: 15,
  interviewerStyle: '客观冷静',
  coverage: '全面',
  language: '中文',
  responseLength: '适中',
  autoTranscribe: true,
  audioSource: 'default',
})

const isAudioTested = ref(false)
const isAudioTestPassed = ref(false)

const handleAudioTestComplete = (success: boolean) => {
  isAudioTested.value = true
  isAudioTestPassed.value = success

  if (!success) {
    eventBus.emit('showToast', {
      message: '麦克风测试未通过，建议检查设置以提升面试体验。',
      type: 'warning',
      duration: 3000,
    })
  }
}

const handleUpdateSettings = (settings: any) => {
  Object.assign(interviewSettings, settings)
}

const emit = defineEmits<{
  (e: 'startInterview', settings: typeof interviewSettings): void
  (e: 'goBack'): void
}>()
</script>

<template>
  <div class="mx-auto max-w-7xl px-4 py-6 sm:px-6 sm:py-8">
    <!-- 主内容区 -->
    <div class="flex flex-col gap-8 lg:flex-row lg:gap-12">
      <!-- 左侧内容区 (介绍和引导) -->
      <div class="lg:w-2/5">
        <div class="sticky top-8">
          <div class="rounded-xl bg-white p-6 shadow-md">
            <div class="mb-6 flex items-center gap-3">
              <div class="i-carbon-information-square flex-shrink-0 text-2xl text-purple-600"></div>
              <h1 class="bg-gradient-to-r from-purple-600 to-purple-500 bg-clip-text text-2xl font-bold text-transparent">模拟面试</h1>
            </div>

            <div class="space-y-5 text-gray-600">
              <div class="group flex items-start gap-3 rounded-lg bg-purple-50 p-4 transition-all hover:bg-purple-100">
                <div class="i-carbon-checkmark mt-0.5 flex-shrink-0 text-lg text-purple-600"></div>
                <div>
                  <h3 class="mb-1.5 font-medium text-purple-800">面试配置</h3>
                  <p class="text-sm leading-relaxed text-purple-700/80">
                    根据您的需求自定义面试体验，包括时长、面试官风格、问题覆盖范围、语言和更多选项。建议完成麦克风测试，但即使测试未通过也可以开始面试。
                  </p>
                </div>
              </div>
            </div>

            <div class="mt-6 rounded-lg bg-purple-50 p-4 shadow-sm transition-all hover:bg-purple-100">
              <div class="flex items-start gap-3">
                <div class="i-carbon-light-filled mt-0.5 flex-shrink-0 text-xl text-purple-600"></div>
                <div>
                  <h3 class="mb-1.5 font-medium text-purple-800">科学引导，助您提升</h3>
                  <p class="text-sm leading-relaxed text-purple-700/80">
                    模拟面试将通过 AI 技术实时分析您的回答，提供专业的面试反馈和改进建议，帮助您更好地准备真实面试场景。
                  </p>
                </div>
              </div>
            </div>

            <!-- 面试提示 -->
            <div class="mt-6 rounded-lg bg-amber-50 p-4 shadow-sm transition-all hover:bg-amber-100">
              <div class="flex items-start gap-3">
                <div class="i-carbon-warning-alt mt-0.5 flex-shrink-0 text-xl text-amber-500"></div>
                <div>
                  <h4 class="mb-2 font-medium text-amber-800">面试注意事项</h4>
                  <ul class="space-y-2 text-sm text-amber-700/80">
                    <li class="flex items-start gap-2">
                      <div class="i-carbon-dot-mark mt-1 flex-shrink-0 text-amber-500"></div>
                      <span>找一个安静无干扰的环境</span>
                    </li>
                    <li class="flex items-start gap-2">
                      <div class="i-carbon-dot-mark mt-1 flex-shrink-0 text-amber-500"></div>
                      <span>确保网络连接稳定</span>
                    </li>
                    <li class="flex items-start gap-2">
                      <div class="i-carbon-dot-mark mt-1 flex-shrink-0 text-amber-500"></div>
                      <span>面试过程中请勿刷新页面</span>
                    </li>
                    <li class="flex items-start gap-2">
                      <div class="i-carbon-dot-mark mt-1 flex-shrink-0 text-amber-500"></div>
                      <span>语音清晰，语速适中</span>
                    </li>
                    <li class="flex items-start gap-2">
                      <div class="i-carbon-dot-mark mt-1 flex-shrink-0 text-amber-500"></div>
                      <span>可以随时暂停或结束面试</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧配置面板 -->
      <div class="lg:w-1/2">
        <!-- 设置表单 -->
        <InterviewSettingsForm @update:settings="handleUpdateSettings" @audio-test-complete="handleAudioTestComplete" />

        <!-- 操作按钮区域 -->
        <div class="mt-6 flex justify-center gap-4">
          <UiButton @click="emit('goBack')" variant="outline" size="md"> 返回上一步 </UiButton>
          <UiButton @click="emit('startInterview', interviewSettings)" variant="primary" size="md"> 开始模拟面试 </UiButton>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition:
    opacity 0.5s ease,
    transform 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>
