<script setup lang="ts">
import SettingsSelect from '~/components/ui/Select.vue'
import SettingsToggle from '~/components/ui/Toggle.vue'
import type { InterviewSettings, OptionItem, InterviewerStyle, InterviewCoverage, InterviewLanguage, AudioSource } from '~/types/interview-settings'
const { isRecording, testCompleted, audioLevel, testSuccess, startRecording } = useMicrophoneTest()

const styleOptions: OptionItem[] = [
  { value: 'objective', label: '客观冷静' },
  { value: 'friendly', label: '友好鼓励' },
  { value: 'challenging', label: '严格挑战' },
  { value: 'technical', label: '技术深入' },
  { value: 'patient', label: '耐心指导' },
  { value: 'humorous', label: '幽默风趣' },
  { value: 'creative', label: '思维发散' },
  { value: 'pressure', label: '压力施加' },
]

const coverageOptions: OptionItem[] = [
  { value: 'comprehensive', label: '全面（技能、经验、项目）' },
  { value: 'technical_skills', label: '专业技能' },
  { value: 'behavioral', label: '行为问题' },
  { value: 'project_experience', label: '项目经验' },
  { value: 'algorithms', label: '算法与数据结构' },
  { value: 'system_design', label: '系统设计' },
  { value: 'teamwork', label: '团队协作能力' },
  { value: 'leadership', label: '领导力与管理' },
  { value: 'career_planning', label: '职业规划与发展' },
  { value: 'salary_negotiation', label: '薪资与福利谈判' },
]

const languageOptions: OptionItem[] = [
  { value: '中文', label: '中文' },
  { value: 'English', label: 'English' },
]

const audioSourceOptions = ref<OptionItem[]>([{ value: 'default', label: '默认麦克风' }])

const interviewSettings = reactive<InterviewSettings>({
  duration: 15,
  interviewerStyle: 'objective' as InterviewerStyle,
  coverage: 'comprehensive' as InterviewCoverage,
  language: '中文' as InterviewLanguage,
  autoTranscribe: true,
  audioSource: 'default' as AudioSource,
})

const emit = defineEmits<{
  'update:settings': [settings: InterviewSettings]
  'audio-test-complete': [success: boolean]
}>()

const durationOptions = [
  { value: 5, label: '5 分钟' },
  { value: 10, label: '10 分钟' },
  { value: 15, label: '15 分钟' },
  { value: 20, label: '20 分钟' },
  { value: 30, label: '30 分钟' },
  { value: 45, label: '45 分钟' },
  { value: 60, label: '60 分钟' },
]

const updateSettings = () => {
  emit('update:settings', { ...interviewSettings })
}

const loadAudioDevices = async () => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices()
    const audioInputs = devices.filter(device => device.kind === 'audioinput')

    const uniqueDevices = new Map()

    audioInputs.forEach(device => {
      if (device.deviceId !== 'default' && !uniqueDevices.has(device.deviceId) && device.label) {
        uniqueDevices.set(device.deviceId, {
          value: device.deviceId,
          label: device.label || `麦克风 ${device.deviceId.substring(0, 5)}...`,
        })
      }
    })

    audioSourceOptions.value = [{ value: 'default', label: '默认麦克风' }, ...Array.from(uniqueDevices.values())]
  } catch (err) {
    console.error('获取音频设备失败:', err)
  }
}

watch(testCompleted, value => {
  if (value) {
    emit('audio-test-complete', testSuccess.value)
  }
})

const handleStartTest = () => {
  startRecording(interviewSettings.audioSource)
}

onMounted(() => {
  loadAudioDevices()
})
</script>

<template>
  <div class="transition-all duration-300">
    <div class="space-y-4">
      <!-- 基础设置组 -->
      <div class="rounded-lg bg-white p-4 shadow-sm">
        <!-- 面试时长 -->
        <SettingsSelect
          v-model="interviewSettings.duration"
          :options="durationOptions"
          icon="i-carbon-timer"
          label="面试时长"
          @update:model-value="updateSettings"
        />

        <!-- 面试官性格 -->
        <SettingsSelect
          v-model="interviewSettings.interviewerStyle"
          :options="styleOptions"
          icon="i-carbon-face"
          label="面试官性格"
          @update:model-value="updateSettings"
          class="mt-3"
        />

        <!-- 问题偏好 -->
        <SettingsSelect
          v-model="interviewSettings.coverage"
          :options="coverageOptions"
          icon="i-carbon-list-checked"
          label="问题偏好"
          @update:model-value="updateSettings"
          class="mt-3"
        />
      </div>

      <!-- 语言和回答设置组 -->
      <div class="rounded-lg bg-white p-4 shadow-sm">
        <div class="flex flex-col md:flex-row md:gap-4">
          <!-- 面试语言 -->
          <div class="flex-1">
            <SettingsSelect
              v-model="interviewSettings.language"
              :options="languageOptions"
              icon="i-carbon-translate"
              label="面试语言"
              @update:model-value="updateSettings"
            />
          </div>
        </div>
      </div>

      <!-- 音频设置组 -->
      <div class="rounded-lg bg-white p-4 shadow-sm">
        <!-- 自动播放语音 -->
        <div class="mb-3">
          <SettingsToggle v-model="interviewSettings.autoTranscribe" icon="i-carbon-volume-up" label="自动播放语音" @update:model-value="updateSettings" />
        </div>

        <!-- 你的音源 -->
        <div>
          <SettingsSelect
            v-model="interviewSettings.audioSource"
            :options="audioSourceOptions"
            icon="i-carbon-microphone"
            label="你的音源"
            @update:model-value="updateSettings"
          />
        </div>

        <!-- 麦克风测试区 -->
        <div class="mt-4 border-t border-gray-200 pt-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="i-carbon-microphone-filled mr-2 text-indigo-500"></div>
              <h4 class="text-sm font-medium text-gray-800">麦克风测试</h4>
            </div>

            <div class="flex items-center">
              <div v-if="testCompleted" class="mr-2 flex h-5 w-5 items-center justify-center rounded-full" :class="testSuccess ? 'bg-green-100' : 'bg-red-100'">
                <div v-if="testSuccess" class="i-carbon-checkmark text-sm text-green-600"></div>
                <div v-else class="i-carbon-close text-sm text-red-600"></div>
              </div>
              <span
                class="text-xs font-medium"
                :class="{ 'text-indigo-600': isRecording, 'text-green-600': testCompleted && testSuccess, 'text-red-600': testCompleted && !testSuccess }"
              >
                {{ isRecording ? '正在测试...' : testCompleted ? (testSuccess ? '测试通过' : '测试失败') : '未测试' }}
              </span>
            </div>
          </div>

          <div class="mt-3">
            <!-- 音量指示器 -->
            <div v-if="isRecording || testCompleted" class="mb-3 mt-2">
              <div class="flex h-6 w-full items-center rounded-full bg-gray-100 p-1 shadow-inner">
                <div
                  class="h-4 rounded-full transition-all duration-300 ease-out"
                  :class="{
                    'bg-gradient-to-r from-green-400 to-teal-500': audioLevel > 70,
                    'bg-gradient-to-r from-yellow-300 to-green-400': audioLevel <= 70 && audioLevel > 40,
                    'bg-gradient-to-r from-red-400 to-yellow-300': audioLevel <= 40,
                  }"
                  :style="{ width: `${audioLevel}%` }"
                ></div>
              </div>
            </div>

            <!-- 按钮区 -->
            <div class="flex items-center justify-between">
              <span v-if="testCompleted" class="text-xs text-gray-600">
                {{ testSuccess ? '麦克风工作正常' : '声音信号太弱，请调整麦克风' }}
              </span>
              <span v-else-if="isRecording" class="text-xs text-indigo-600">请对着麦克风说话...</span>
              <span v-else class="text-xs text-gray-600">点击按钮测试麦克风</span>

              <div>
                <button
                  v-if="!isRecording"
                  @click="handleStartTest"
                  class="flex items-center rounded-lg border border-indigo-200 bg-indigo-50 px-3 py-1.5 text-xs text-indigo-700 transition-all hover:bg-indigo-100"
                >
                  <div class="i-carbon-microphone-filled mr-1.5 text-indigo-500"></div>
                  {{ testCompleted ? '重新测试' : '开始测试' }}
                </button>

                <button v-else class="flex animate-pulse items-center rounded-lg bg-indigo-100 px-3 py-1.5 text-xs text-indigo-700" disabled>
                  <div class="i-carbon-recording-filled mr-1.5"></div>
                  测试中...
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
