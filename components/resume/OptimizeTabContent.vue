<template>
  <div>
    <div class="mb-6">
      <div class="mb-6 grid grid-cols-2 gap-4">
        <button
          @click="optimizationStore.switchInputType(InputType.TEXT)"
          class="flex items-center justify-center rounded-lg border-2 px-4 py-3 transition-colors"
          :class="
            optimizationStore.uiState.inputType === 'text' ? 'border-purple-500 bg-purple-50 text-purple-800' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          "
        >
          <span class="text-sm font-medium">模块内容优化</span>
        </button>
        <button
          @click="optimizationStore.switchInputType(InputType.FILE)"
          class="flex items-center justify-center rounded-lg border-2 px-4 py-3 transition-colors"
          :class="
            optimizationStore.uiState.inputType === InputType.FILE
              ? 'border-purple-500 bg-purple-50 text-purple-800'
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          "
        >
          <span class="text-sm font-medium">整体优化</span>
        </button>
      </div>

      <!-- 文本输入区域 -->
      <div v-if="optimizationStore.uiState.inputType === InputType.TEXT" class="mb-6">
        <div
          class="relative rounded-lg border border-gray-300 shadow-sm transition-all duration-300 focus-within:border-2 focus-within:border-dashed focus-within:border-purple-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-purple-200"
        >
          <textarea
            v-model="optimizationStore.resumeInput.content"
            class="block h-80 w-full resize-none rounded-t-lg border-0 p-4 text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-0"
            placeholder="请输入需要优化的具体模块内容，如工作经历或教育经历等"
          ></textarea>
          <!-- 文本工具栏 -->
          <div class="flex items-center rounded-b-lg border-t border-gray-200 bg-white px-5 py-3">
            <div class="flex w-full items-center justify-start space-x-6">
              <!-- 增强模式选择 -->
              <div class="relative">
                <div
                  @click.stop="toggleDropdown('enhancement')"
                  class="dropdown-trigger group relative flex cursor-pointer items-center text-sm text-gray-700 hover:text-gray-900"
                  aria-label="优化模式"
                >
                  <EnhancementIcon class="h-5 w-5 text-gray-600" />
                  <span class="ml-2">{{ getEnhancementName(optimizationStore.attachment.enhancementMode) }}</span>
                  <DropdownArrowIcon class="ml-1.5 h-4 w-4 text-gray-500" />
                  <div class="absolute left-0 top-0 -mt-8 hidden w-24 rounded bg-purple-500 px-2 py-1 text-xs text-white group-hover:block">优化模式</div>
                </div>

                <!-- 下拉选项 -->
                <div
                  v-if="dropdownState.enhancement"
                  class="dropdown-options absolute left-0 top-full z-10 mt-2 w-48 rounded-lg border border-gray-200 bg-white p-1 shadow-lg focus:outline-none"
                >
                  <div
                    v-for="option in ENHANCEMENT_OPTIONS"
                    :key="option.value"
                    @click="selectEnhancement(option.value as EnhancementMode)"
                    class="relative my-1 flex cursor-pointer items-center rounded-md px-3 py-2 text-sm hover:bg-purple-50"
                    :class="optimizationStore.attachment.enhancementMode === option.value ? 'bg-purple-50 text-purple-700' : 'text-gray-700'"
                  >
                    <span v-if="optimizationStore.attachment.enhancementMode === option.value" class="text-purple-600"> </span>
                    <span class="block" :class="optimizationStore.attachment.enhancementMode === option.value ? 'font-medium' : 'font-normal'">
                      {{ option.label }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 语言选择 -->
              <div class="relative">
                <div
                  @click.stop="toggleDropdown('language')"
                  class="dropdown-trigger group flex cursor-pointer items-center text-sm text-gray-700 hover:text-gray-900"
                  aria-label="目标语言"
                >
                  <TargetLangIcon class="h-5 w-5 text-gray-600" />
                  <span class="ml-2">{{ getLanguageName(optimizationStore.attachment.targetLanguage) }}</span>
                  <DropdownArrowIcon class="ml-1.5 h-4 w-4 text-gray-500" />
                  <div class="absolute left-0 top-0 -mt-8 hidden w-24 rounded bg-purple-500 px-2 py-1 text-xs text-white group-hover:block">输出语言</div>
                </div>
                <!-- 下拉选项 -->
                <div
                  v-if="dropdownState.language"
                  class="dropdown-options absolute left-0 top-full z-10 mt-2 w-36 rounded-lg border border-gray-200 bg-white p-1 shadow-lg focus:outline-none"
                >
                  <div
                    v-for="option in LANGUAGE_OPTIONS"
                    :key="option.value"
                    @click="selectLanguage(option.value as Language)"
                    class="relative my-1 flex cursor-pointer items-center rounded-md px-3 py-2 text-sm hover:bg-purple-50"
                    :class="optimizationStore.attachment.targetLanguage === option.value ? 'bg-purple-50 text-purple-700' : 'text-gray-700'"
                  >
                    <span v-if="optimizationStore.attachment.targetLanguage === option.value" class="text-purple-600"> </span>
                    <span class="block" :class="optimizationStore.attachment.targetLanguage === option.value ? 'font-medium' : 'font-normal'">
                      {{ option.label }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 职位类型选择 -->
              <div class="relative">
                <div
                  @click.stop="toggleDropdown('jobType')"
                  class="dropdown-trigger group flex cursor-pointer items-center text-sm text-gray-700 hover:text-gray-900"
                  aria-label="职位类型"
                >
                  <JobTypeIcon class="h-5 w-5 text-gray-600" />
                  <span class="ml-2">{{ optimizationStore.attachment.jobType ? getJobTypeName(optimizationStore.attachment.jobType) : '选择职位类型' }}</span>
                  <DropdownArrowIcon class="ml-1.5 h-4 w-4 text-gray-500" />
                  <div class="bg-gray-350 absolute left-0 top-0 -mt-8 hidden w-24 rounded bg-purple-500 px-2 py-1 text-xs text-white group-hover:block">
                    职位类型
                  </div>
                </div>

                <!-- 下拉选项 -->
                <div
                  v-if="dropdownState.jobType"
                  class="dropdown-options absolute left-0 top-full z-10 mt-2 max-h-60 w-48 overflow-y-auto rounded-lg border border-gray-200 bg-white p-1 shadow-lg focus:outline-none"
                >
                  <div
                    v-for="option in JOB_TYPE_OPTIONS"
                    :key="option.value"
                    @click="selectJobType(option.value as JobType)"
                    class="relative my-1 flex cursor-pointer items-center rounded-md px-3 py-2 text-sm hover:bg-purple-50"
                    :class="optimizationStore.attachment.jobType === option.value ? 'bg-purple-50 text-purple-700' : 'text-gray-700'"
                  >
                    <span v-if="optimizationStore.attachment.jobType === option.value" class="text-purple-600"> </span>
                    <span class="block" :class="optimizationStore.attachment.jobType === option.value ? 'font-medium' : 'font-normal'">
                      {{ option.label }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件上传区域 - 优化版 -->
      <div
        v-if="optimizationStore.uiState.inputType === InputType.FILE"
        class="relative cursor-pointer rounded-xl transition-all duration-300"
        :class="[
          optimizationStore.resumeInput.file
            ? 'border border-indigo-200 bg-white shadow-sm'
            : 'border-2 border-dashed border-indigo-100/80 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 hover:translate-y-[-2px] hover:border-purple-300 hover:shadow-md hover:shadow-purple-100/40',
        ]"
        @dragover="handleDragOver"
        @drop="handleDrop"
        @click="!optimizationStore.resumeInput.file && optimizationStore.uploadFileStatus !== Status.PROCESSING && fileInput!.click()"
      >
        <input type="file" ref="fileInput" @change="handleFileChange" accept="application/pdf, .doc" class="hidden" />

        <!-- 加载状态 -->
        <div v-if="optimizationStore.uploadFileStatus === Status.PROCESSING" class="absolute inset-0 z-10 bg-white/95">
          <LoadingIndicator message="正在解析文件..." :showProgress="true" />
        </div>

        <!-- 无文件状态 -->
        <div v-if="!optimizationStore.resumeInput.file" class="p-8 text-center">
          <div class="flex flex-col items-center justify-center">
            <div
              class="mb-5 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100/80 p-4 text-purple-500 shadow-sm shadow-purple-100/50 transition-all duration-300 hover:scale-105"
            >
              <FileUploadIcon class="h-9 w-9" />
            </div>
            <h2 class="text-base font-medium text-gray-700">拖拽文件到此处或点击上传</h2>
            <p class="mt-1 text-sm text-gray-500">支持PDF格式</p>
            <button
              class="mt-5 flex items-center rounded-lg bg-gradient-to-r from-purple-500 to-purple-600 px-5 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200 hover:translate-y-[-1px] hover:shadow-md hover:shadow-purple-200"
              @click.stop="fileInput!.click()"
              :disabled="optimizationStore.uploadFileStatus === Status.PROCESSING"
            >
              选择文件
            </button>
          </div>
        </div>

        <!-- 有文件状态 - 带工具栏 -->
        <div v-else>
          <div class="rounded-t-xl p-6">
            <div class="flex flex-col items-center">
              <!-- 文件图标和信息 -->
              <div class="mb-4 flex h-14 w-14 flex-shrink-0 items-center justify-center rounded-full bg-purple-100 transition-all duration-300 hover:scale-105">
                <DocumentIcon class="h-8 w-8 text-purple-600" />
              </div>

              <!-- 文件名和大小 -->
              <div class="mb-2 text-center">
                <h3 class="text-base font-medium text-gray-800">{{ optimizationStore.resumeInput.file.name }}</h3>
                <p class="mt-0.5 text-sm text-gray-500">{{ formatFileSize(optimizationStore.resumeInput.file.size) }}</p>
              </div>

              <!-- 操作按钮 -->
              <div class="mt-4 flex space-x-3">
                <button
                  @click.stop="fileInput!.click()"
                  class="flex items-center rounded-lg border border-purple-200 bg-purple-50 px-4 py-2 text-sm font-medium text-purple-600 transition-all duration-200 hover:bg-purple-100 hover:shadow-sm"
                  :disabled="optimizationStore.uploadFileStatus === Status.PROCESSING"
                >
                  <UploadButtonIcon class="mr-1.5 h-4 w-4" />
                  更换文件
                </button>
                <button
                  @click.stop="removeFile"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-600 transition-all duration-200 hover:border-red-200 hover:bg-red-50 hover:text-pink-400"
                  :disabled="optimizationStore.uploadFileStatus === Status.PROCESSING"
                >
                  <RemoveFileIcon class="mr-1.5 h-4 w-4" />
                  移除文件
                </button>
              </div>
            </div>
          </div>

          <!-- 文件工具栏 -->
          <div class="flex items-center rounded-b-xl border-t border-gray-200 bg-white px-5 py-3">
            <div class="flex w-full items-center justify-start space-x-6">
              <!-- 增强模式选择 -->
              <div class="relative z-30">
                <div
                  @click.stop="toggleDropdown('enhancement')"
                  class="dropdown-trigger group relative flex cursor-pointer items-center text-sm text-gray-700 hover:text-gray-900"
                  aria-label="增强模式"
                >
                  <EnhancementIcon class="h-5 w-5 text-gray-600" />
                  <span class="ml-2">{{ getEnhancementName(optimizationStore.attachment.enhancementMode) }}</span>
                  <DropdownArrowIcon class="ml-1.5 h-4 w-4 text-gray-500" />
                  <div class="absolute left-0 top-0 -mt-8 hidden w-24 rounded bg-purple-500 px-2 py-1 text-xs text-white group-hover:block">优化模式</div>
                </div>

                <!-- 下拉选项 - 只修改z-index -->
                <div
                  v-if="dropdownState.enhancement"
                  class="dropdown-options absolute left-0 top-full z-50 mt-2 w-48 rounded-lg border border-gray-200 bg-white p-1 shadow-lg focus:outline-none"
                >
                  <div
                    v-for="option in ENHANCEMENT_OPTIONS"
                    :key="option.value"
                    @click="selectEnhancement(option.value as EnhancementMode)"
                    class="relative my-1 flex cursor-pointer items-center rounded-md px-3 py-2 text-sm hover:bg-purple-50"
                    :class="optimizationStore.attachment.enhancementMode === option.value ? 'bg-purple-50 text-purple-700' : 'text-gray-700'"
                  >
                    <span v-if="optimizationStore.attachment.enhancementMode === option.value" class="text-purple-600"> </span>
                    <span class="block" :class="optimizationStore.attachment.enhancementMode === option.value ? 'font-medium' : 'font-normal'">
                      {{ option.label }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 语言选择 -->
              <div class="relative z-30">
                <div
                  @click.stop="toggleDropdown('language')"
                  class="dropdown-trigger group flex cursor-pointer items-center text-sm text-gray-700 hover:text-gray-900"
                  aria-label="目标语言"
                >
                  <!-- <svg class="h-5 w-5 text-gray-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M3 5H15M9 3V5M10.048 14.5C8.5081 12.9059 7.26768 11.0413 6.39999 9M12.5 18H19.5M11 21L16 11L21 21M12.5 5C11.9168 7.17428 11 9.58947 9.99999 11.2632C8.95363 13.014 7.84639 14 6.39999 14"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg> -->
                  <TargetLangIcon class="h-5 w-5 text-gray-600" />
                  <span class="ml-2">{{ getLanguageName(optimizationStore.attachment.targetLanguage) }}</span>
                  <DropdownArrowIcon class="ml-1.5 h-4 w-4 text-gray-500" />
                  <div class="absolute left-0 top-0 -mt-8 hidden w-24 rounded bg-purple-500 px-2 py-1 text-xs text-white group-hover:block">输出语言</div>
                </div>
                <!-- 下拉选项 - 只修改z-index -->
                <div
                  v-if="dropdownState.language"
                  class="dropdown-options absolute left-0 top-full z-50 mt-2 w-36 rounded-lg border border-gray-200 bg-white p-1 shadow-lg focus:outline-none"
                >
                  <div
                    v-for="option in LANGUAGE_OPTIONS"
                    :key="option.value"
                    @click="selectLanguage(option.value as Language)"
                    class="relative my-1 flex cursor-pointer items-center rounded-md px-3 py-2 text-sm hover:bg-purple-50"
                    :class="optimizationStore.attachment.targetLanguage === option.value ? 'bg-purple-50 text-purple-700' : 'text-gray-700'"
                  >
                    <span v-if="optimizationStore.attachment.targetLanguage === option.value" class="text-purple-600"> </span>
                    <span class="block" :class="optimizationStore.attachment.targetLanguage === option.value ? 'font-medium' : 'font-normal'">
                      {{ option.label }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 职位类型选择 -->
              <div class="relative z-30">
                <div
                  @click.stop="toggleDropdown('jobType')"
                  class="dropdown-trigger group flex cursor-pointer items-center text-sm text-gray-700 hover:text-gray-900"
                  aria-label="职位类型"
                >
                  <JobTypeIcon class="h-5 w-5 text-gray-600" />
                  <span class="ml-2">{{ optimizationStore.attachment.jobType ? getJobTypeName(optimizationStore.attachment.jobType) : '选择职位类型' }}</span>
                  <DropdownArrowIcon class="ml-1.5 h-4 w-4 text-gray-500" />
                  <div class="bg-gray-350 absolute left-0 top-0 -mt-8 hidden w-24 rounded bg-purple-500 px-2 py-1 text-xs text-white group-hover:block">
                    职位类型
                  </div>
                </div>

                <!-- 下拉选项 - 只修改z-index -->
                <div
                  v-if="dropdownState.jobType"
                  class="dropdown-options absolute left-0 top-full z-50 mt-2 max-h-60 w-48 overflow-y-auto rounded-lg border border-gray-200 bg-white p-1 shadow-lg focus:outline-none"
                >
                  <div
                    v-for="option in JOB_TYPE_OPTIONS"
                    :key="option.value"
                    @click="selectJobType(option.value as JobType)"
                    class="relative my-1 flex cursor-pointer items-center rounded-md px-3 py-2 text-sm hover:bg-purple-50"
                    :class="optimizationStore.attachment.jobType === option.value ? 'bg-purple-50 text-purple-700' : 'text-gray-700'"
                  >
                    <span v-if="optimizationStore.attachment.jobType === option.value" class="text-purple-600"> </span>
                    <span class="block" :class="optimizationStore.attachment.jobType === option.value ? 'font-medium' : 'font-normal'">
                      {{ option.label }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 优化选项 -->
      <div class="mb-6" v-if="optimizationStore.uiState.inputType === InputType.TEXT">
        <h3 class="mb-3 text-sm font-medium text-gray-700">选择优化模块</h3>
        <div class="grid grid-cols-2 gap-4">
          <div v-for="option in RESUME_MODULE_OPTIONS" :key="option.value" class="flex items-center">
            <input
              type="radio"
              :id="option.value"
              name="optimizeOption"
              class="h-4 w-4 border-gray-300 text-purple-600 focus:ring-purple-500"
              :value="option.value"
              v-model="optimizationStore.attachment.module"
            />
            <label :for="option.value" class="ml-2 text-sm text-gray-700">{{ option.label }}</label>
          </div>
        </div>
      </div>

      <div class="my-4" v-if="optimizationStore.uiState.inputType === InputType.FILE">
        <h3 class="mb-3 text-sm font-medium text-gray-700">选择你的简历服务</h3>
        <div class="grid grid-cols-2 gap-4">
          <div v-for="option in Full_RESUME_STRATEGY_OPTIONS" :key="option.value" class="flex items-center">
            <input
              type="radio"
              :id="option.value"
              name="fullResumeStrategy"
              class="h-4 w-4 border-gray-300 text-purple-600 focus:ring-purple-500"
              :value="option.value"
              v-model="optimizationStore.attachment.fullResumeStrategy"
            />
            <label :for="option.value" class="ml-2 text-sm text-gray-700">{{ option.label }}</label>
          </div>
        </div>
      </div>
      <!-- JD信息 -->
      <div class="mb-6">
        <div class="mb-3 flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium text-gray-700">岗位信息</h3>
            <p class="mt-2 text-[10px] text-gray-400">提供岗位职责、任职要求等岗位信息，效果会更佳哟</p>
          </div>
          <div class="relative inline-block w-12 select-none align-middle">
            <input type="checkbox" id="toggle-jd" class="peer sr-only" v-model="optimizationStore.uiState.showJdInput" />
            <label
              for="toggle-jd"
              class="block h-6 w-11 cursor-pointer rounded-full bg-gray-300 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-purple-500 peer-checked:after:translate-x-full peer-focus:outline-none"
            ></label>
          </div>
        </div>
        <textarea
          v-if="optimizationStore.uiState.showJdInput"
          v-model="optimizationStore.attachment.jobInfo"
          class="mt-1 block h-32 w-full resize-none rounded-md border border-gray-300 p-4 placeholder-gray-400 shadow-sm placeholder:text-xs focus:border-purple-500 focus:ring-purple-500 sm:text-sm"
          :placeholder="PLACEHOLDERS.jobInfo"
        ></textarea>
      </div>

      <!-- 优化按钮 -->
      <button
        @click="handleSubmit"
        class="relative flex w-full items-center justify-center overflow-hidden rounded-xl bg-gradient-to-r from-purple-600 to-pink-500 px-4 py-3 text-white shadow-md shadow-purple-500/20 transition-all duration-300 hover:translate-y-[-2px] hover:from-purple-700 hover:to-pink-600 hover:shadow-lg hover:shadow-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
        :disabled="!optimizationStore.isFormValid"
        :class="{ 'cursor-not-allowed opacity-70': !optimizationStore.isFormValid }"
      >
        <OptimizeButtonIcon class="mr-2 h-5 w-5" />
        开始优化
        <span class="button-shine"></span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  useOptimizationStore,
  dropdownState,
  LANGUAGE_OPTIONS,
  ENHANCEMENT_OPTIONS,
  JOB_TYPE_OPTIONS,
  PLACEHOLDERS,
  InputType,
  Module,
  Language,
  EnhancementMode,
  JobType,
  FullResumeStrategy,
  Full_RESUME_STRATEGY_OPTIONS,
  Status,
} from '@/stores/resume'
import LoadingIndicator from '@/components/resume/LoadingIndicator.vue'
import TargetLangIcon from '@/assets/icons/target-lang.svg'
import EnhancementIcon from '@/assets/icons/enhancement-icon.svg'
import JobTypeIcon from '@/assets/icons/job-type-icon.svg'
import FileUploadIcon from '@/assets/icons/file-upload-icon.svg'
import DocumentIcon from '@/assets/icons/document-icon.svg'
import UploadButtonIcon from '@/assets/icons/upload-button-icon.svg'
import RemoveFileIcon from '@/assets/icons/remove-file-icon.svg'
import DropdownArrowIcon from '@/assets/icons/dropdown-arrow-icon.svg'
import OptimizeButtonIcon from '@/assets/icons/optimize-button-icon.svg'
import { formatFileSize } from '@/utils/file'
import { useToast } from 'vue-toast-notification'

const toast = useToast()
const optimizationStore = useOptimizationStore()
const { checkCreditsAndExecute } = useCreditsGuard()

const fileInput = ref<HTMLInputElement | null>(null)

// UI操作函数
const toggleDropdown = (dropdownName: keyof typeof dropdownState) => {
  Object.keys(dropdownState).forEach(key => {
    dropdownState[key as keyof typeof dropdownState] = key === dropdownName ? !dropdownState[key as keyof typeof dropdownState] : false
  })
}

const closeAllDropdowns = () => {
  Object.keys(dropdownState).forEach(key => {
    dropdownState[key as keyof typeof dropdownState] = false
  })
}

// 绑定全局点击事件关闭下拉菜单
onMounted(() => {
  document.addEventListener('click', (event: MouseEvent) => {
    const target = event.target as HTMLElement
    if (!target.closest('.dropdown-trigger') && !target.closest('.dropdown-options')) {
      closeAllDropdowns()
    }
  })
})

onUnmounted(() => {
  document.removeEventListener('click', closeAllDropdowns)
})

// 业务函数 - 委托给store处理数据
const selectLanguage = (value: Language) => {
  optimizationStore.updateAttachment('targetLanguage', value)
  dropdownState.language = false
}

const selectEnhancement = (value: EnhancementMode) => {
  optimizationStore.updateAttachment('enhancementMode', value)
  dropdownState.enhancement = false
}

const selectJobType = (value: JobType) => {
  optimizationStore.updateAttachment('jobType', value)
  dropdownState.jobType = false
}

// 文件处理
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
}

const handleDrop = async (e: DragEvent) => {
  e.preventDefault()
  if (e.dataTransfer?.files.length) {
    const file = e.dataTransfer.files[0]
    if (file.type === 'application/pdf') {
      await processPdfFile(file)
    }
  }
}

const handleFileChange = async (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    const file = target.files[0]
    await processPdfFile(file)
  }
}

// 定义响应类型接口
interface ParseResponse {
  success: boolean
  data: {
    markdown?: string
    message?: string
  }
}
// 处理 PDF 文件并提取内容
const processPdfFile = async (file: File) => {
  try {
    // 显示加载状态
    optimizationStore.uploadFileStatus = Status.PROCESSING

    const formData = new FormData()
    formData.append('file', file)

    // 文件类型只支持PDF
    if (file.type !== 'application/pdf') {
      toast.error('文件类型错误，请上传PDF文件', {
        duration: 3000,
        position: 'top-right',
      })
      return
    }

    const response = await $fetch<ParseResponse>('/api/file/file-to-md', {
      method: 'POST',
      body: formData,
    })
    if (!response.success || !response.data.markdown) {
      throw new Error(`file parse error !`)
    }

    optimizationStore.resumeInput.fileToMarkdownContent = response.data.markdown
    optimizationStore.resumeInput.file = file
  } catch (error: any) {
    toast.error('文件解析失败，请稍后重试', {
      duration: 3000,
      position: 'top-right',
    })
  } finally {
    optimizationStore.uploadFileStatus = Status.PENDING
  }
}

// 处理翻译请求
const translationRequest = async (resumeContent: string) => {
  const apiParams = {
    text: resumeContent,
    targetLang: optimizationStore.attachment.targetLanguage,
    model: optimizationStore.attachment.enhancementMode,
    jobInfo: optimizationStore.attachment.jobInfo || undefined,
    countNumber: 500, // 默认字数限制
  }

  return (await $fetch('/api/resume/i18n', {
    method: 'POST',
    body: apiParams,
    responseType: 'stream',
  })) as ReadableStream
}

// 处理优化请求
const optimizationRequest = async (resumeContent: string) => {
  const apiParams = {
    text: resumeContent,
    model: optimizationStore.attachment.enhancementMode,
    resumeModule: optimizationStore.attachment.module,
    language: optimizationStore.attachment.targetLanguage,
    jobType: optimizationStore.attachment.jobType,
    jobInfo: optimizationStore.attachment.jobInfo || undefined,
  }

  return (await $fetch('/api/resume/optimize', {
    method: 'POST',
    body: apiParams,
    responseType: 'stream',
  })) as ReadableStream
}

const handleSubmit = async () => {
  // 表单验证
  if (optimizationStore.uiState.inputType === InputType.TEXT && !optimizationStore.resumeInput.content) {
    toast.error('请输入简历内容')
    return
  }
  if (optimizationStore.uiState.inputType === InputType.FILE && !optimizationStore.resumeInput.file) {
    toast.error('请上传简历PDF文件')
    return
  }
  if (!optimizationStore.attachment.jobType) {
    toast.error('请选择职位类型')
    return
  }

  await checkCreditsAndExecute(async () => {
    try {
      // 开始处理
      optimizationStore.status = Status.PROCESSING
      optimizationStore.output.content = '' // 清空之前的内容

      const inputType = optimizationStore.uiState.inputType

      const resumeContent = inputType === InputType.TEXT ? optimizationStore.resumeInput.content : optimizationStore.resumeInput.fileToMarkdownContent

      // 根据策略选择处理方法
      const isTranslation = optimizationStore.attachment.fullResumeStrategy === FullResumeStrategy.TRANSLATE
      const response = await (isTranslation ? translationRequest(resumeContent) : optimizationRequest(resumeContent))

      // 处理流式响应
      const reader = response.getReader()
      const decoder = new TextDecoder()
      let result = ''
      optimizationStore.status = Status.COMPLETED

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        result += chunk
        optimizationStore.output.content = result
      }
    } catch (error) {
      optimizationStore.status = Status.ERROR
      toast.error(`${optimizationStore.attachment.fullResumeStrategy === FullResumeStrategy.TRANSLATE ? '翻译' : '优化'}失败，请重试`)
    }
  })
}

const getLanguageName = (value: string): string => {
  const option = LANGUAGE_OPTIONS.find(option => option.value === value)
  return option ? option.label : '中文简体'
}

const getEnhancementName = (value: string): string => {
  const option = ENHANCEMENT_OPTIONS.find(option => option.value === value)
  return option ? option.label : '标准优化'
}

const getJobTypeName = (value: string): string => {
  const option = JOB_TYPE_OPTIONS.find(option => option.value === value)
  return option ? option.label : '通用类'
}

// 移除文件
const removeFile = (e: Event) => {
  e.stopPropagation()
  optimizationStore.resumeInput.file = null
  optimizationStore.updateResumeInputData({
    fileToMarkdownContent: '',
  })
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}
</script>
