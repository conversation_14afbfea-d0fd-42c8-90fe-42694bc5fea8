<template>
  <TransitionRoot appear :show="props.isOpen" as="template">
    <Dialog as="div" class="relative z-50" @close="handleClose">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="relative h-[90vh] w-full max-w-6xl transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all">
              <!-- 顶部装饰条 -->
              <div class="absolute inset-x-0 top-0 h-1.5 rounded-t-2xl bg-gradient-to-r from-purple-500/20 via-purple-600/40 to-pink-500/20"></div>

              <!-- 关闭按钮 -->
              <button class="absolute right-4 top-4 z-10 p-1.5 text-gray-400 transition-colors" @click="handleClose">
                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  />
                </svg>
              </button>

              <div class="flex h-full flex-col overflow-y-auto">
                <!-- 固定头部 -->
                <div class="flex-none p-6 pb-0">
                  <DialogTitle as="h3" class="bg-gradient-to-r from-purple-600 to-pink-500 bg-clip-text text-center text-2xl font-semibold text-transparent">
                    {{ textOptions.title }}
                  </DialogTitle>
                  <p class="mt-2 text-center text-sm text-gray-500">{{ textOptions.description }}</p>
                </div>

                <!-- 可滚动的内容区域 -->
                <div class="flex-1 p-6">
                  <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <!-- 原始内容 -->
                    <div class="flex h-full flex-col rounded-xl border border-gray-100 bg-white p-4 shadow-sm transition-all hover:shadow-md">
                      <div class="mb-3 flex items-center justify-between">
                        <h4 class="text-lg font-medium text-purple-500">{{ textOptions.preText }}</h4>
                        <span class="select-none rounded-[6px] bg-purple-100 px-3 py-1 text-xs font-medium text-purple-600">{{ textOptions.preTag }}</span>
                      </div>
                      <div class="custom-scrollbar h-[60vh] overflow-y-auto rounded-lg bg-gray-50/50 p-4 shadow-inner">
                        <pre class="whitespace-pre-wrap font-mono text-sm text-gray-600">{{ originalContent }}</pre>
                      </div>
                    </div>

                    <!-- 优化后内容 -->
                    <div class="flex h-full flex-col rounded-xl border border-purple-100 bg-white p-4 shadow-sm transition-all hover:shadow-md">
                      <div class="mb-3 flex items-center justify-between">
                        <h4 class="text-lg font-medium text-purple-500">{{ textOptions.postText }}</h4>
                        <span class="select-none rounded-[6px] bg-purple-50 px-3 py-1 text-xs font-medium text-purple-600">{{ textOptions.postTag }}</span>
                      </div>
                      <div class="custom-scrollbar h-[60vh] overflow-y-auto rounded-lg bg-purple-50/30 p-4 shadow-inner">
                        <pre class="whitespace-pre-wrap font-mono text-sm text-gray-600">{{ optimizedContent }}</pre>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { useResumeStore } from '@/stores/resume'

const resumeStore = useResumeStore()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const handleClose = () => {
  emit('close')
}

const props = defineProps<{
  isOpen: boolean
  originalContent: string
  optimizedContent: string
}>()

const OPTIMIZE_TEXT_OPTIONS = {
  title: '简历内容对比',
  preText: '原始内容',
  postText: '优化内容',
  preTag: '优化前',
  postTag: '优化后',
  description: '对比优化前后的简历内容，查看 AI 助手的优化效果',
}

const WRITE_TEXT_OPTIONS = {
  title: '简历内容对比',
  preText: '原始内容',
  postText: '扩写内容',
  preTag: '扩写前',
  postTag: '扩写后',
  description: '对比扩写前后的简历内容，查看 AI 助手的扩写效果',
}

const DEFAULT_TEXT_OPTIONS = {
  title: '内容对比',
  preText: '原始内容',
  postText: '处理后内容',
  preTag: '处理前',
  postTag: '处理后',
  description: '对比前后效果，查看 AI 助手的修改效果',
}

const textOptions = computed(() => {
  if (resumeStore.activeTab === 'optimize') return OPTIMIZE_TEXT_OPTIONS
  if (resumeStore.activeTab === 'write') return WRITE_TEXT_OPTIONS
  return DEFAULT_TEXT_OPTIONS
})
</script>

<style scoped>
/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 2px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #9333ea, #ec4899);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #7e22ce, #db2777);
}

/* Firefox 滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #9333ea #f3f4f6;
}
</style>
