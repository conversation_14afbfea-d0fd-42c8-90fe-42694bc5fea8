<template>
  <div class="resume-result-container">
    <div class="mb-4 flex items-center justify-between">
      <div class="flex gap-2">
        <button
          @click="copyToClipboard"
          class="flex items-center gap-1 rounded-md bg-indigo-50 px-3 py-1.5 text-xs font-medium text-indigo-600 transition-colors hover:cursor-pointer hover:bg-indigo-100"
          :disabled="copyButtonDisabled"
        >
          <ClipboardIcon class="h-4 w-4" />
          复制
        </button>
        <button
          v-show="showComparisonButton"
          @click="openComparisonDialog"
          class="flex items-center gap-1 rounded-md bg-purple-50 px-3 py-1.5 text-xs font-medium text-purple-600 transition-colors hover:cursor-pointer hover:bg-purple-100"
        >
          <ComparisonIcon class="h-4 w-4" />
          对比效果
        </button>
        <button
          @click="downloadAsText"
          class="flex items-center gap-1 rounded-md bg-purple-50 px-3 py-1.5 text-xs font-medium text-purple-600 transition-colors hover:cursor-pointer hover:bg-purple-100"
          :disabled="downloadButtonDisabled"
        >
          <DownloadIcon class="h-4 w-4" />
          下载
        </button>

        <!-- 查看/预览切换按钮 -->
        <div class="flex overflow-hidden rounded-md border border-indigo-100">
          <button
            @click="viewMode = 'preview'"
            class="flex items-center gap-1 px-3 py-1.5 text-xs font-medium transition-colors"
            :class="viewMode === 'preview' ? 'bg-indigo-500 text-white' : 'bg-white text-gray-600 hover:bg-indigo-50'"
          >
            <PreviewIcon class="h-4 w-4" />
            预览
          </button>
          <button
            @click="viewMode = 'source'"
            class="flex items-center gap-1 px-3 py-1.5 text-xs font-medium transition-colors"
            :class="viewMode === 'source' ? 'bg-indigo-500 text-white' : 'bg-white text-gray-600 hover:bg-indigo-50'"
          >
            <SourceCodeIcon class="h-4 w-4" />
            源码
          </button>
        </div>
      </div>
    </div>

    <div class="overflow-hidden rounded-xl border border-indigo-100/70 bg-white p-6 shadow-sm transition duration-300 hover:shadow-md">
      <!-- 加载动效 -->
      <LoadingIndicator v-if="optimizationStore.status == 'processing'" message="正在帮您优化简历内容..." :showProgress="true" />

      <!-- 错误状态 -->
      <!-- <ErrorState v-else-if="optimizationStore.status == 'error'" :showRetry="true" @retry="handleRetry" /> -->

      <!-- 结果内容 -->
      <template v-else>
        <!-- Markdown 预览模式 -->
        <RenderResume v-if="viewMode === 'preview'" :markdownText="markdownText" />

        <!-- Markdown 源码模式 -->
        <div v-else class="markdown-source custom-scrollbar">
          <pre><code>{{ markdownText }}</code></pre>
        </div>
      </template>
    </div>

    <!-- 对比对话框 -->
    <ResumeComparisonDialog
      :is-open="isComparisonDialogOpen"
      :original-content="originalContent"
      :optimized-content="markdownText"
      @close="isComparisonDialogOpen = false"
    />
  </div>
</template>

<script setup lang="ts">
import RenderResume from '@/components/RenderResume.vue'
import LoadingIndicator from '@/components/resume/LoadingIndicator.vue'
import ErrorState from '@/components/resume/ErrorState.vue'
import ResumeComparisonDialog from '@/components/resume/ResumeComparisonDialog.vue'
import ClipboardIcon from '@/assets/icons/clipboard-icon.svg'
import ComparisonIcon from '@/assets/icons/comparison-icon.svg'
import DownloadIcon from '@/assets/icons/download-icon.svg'
import PreviewIcon from '@/assets/icons/preview-icon.svg'
import SourceCodeIcon from '@/assets/icons/source-code-icon.svg'
import { useResumeStore, useOptimizationStore, useIAMInterviewerStore } from '@/stores/resume'
import { useToast } from 'vue-toast-notification'
import 'vue-toast-notification/dist/theme-default.css'

const resumeStore = useResumeStore()
const optimizationStore = useOptimizationStore()
const IAMInterviewerStore = useIAMInterviewerStore()
const toast = useToast()

const props = defineProps<{
  originalContent: string
  markdownText: string
}>()

// 视图模式：预览或源码
const viewMode = ref('preview')
// 对比对话框状态
const isComparisonDialogOpen = ref(false)

// 复制到剪贴板功能
const copyToClipboard = () => {
  navigator.clipboard
    .writeText(props.markdownText)
    .then(() => {
      toast.success('复制文本成功!', {
        position: 'top',
        duration: 2000,
      })
    })
    .catch(() => {
      toast.error('复制文本失败!', {
        position: 'top',
        duration: 2000,
      })
    })
}

// 下载为文本文件
const downloadAsText = () => {
  const blob = new Blob([props.markdownText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = '简历内容.md'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// // 重试处理
// const handleRetry = () => {
//   optimizationStore.submitOptimization()
// }
// 打开对比对话框
const openComparisonDialog = () => {
  if (optimizationStore.status !== 'completed') return
  isComparisonDialogOpen.value = true
}

const showComparisonButton = computed(() => {
  if (optimizationStore.status !== 'completed') return false
  return resumeStore.activeTab === 'optimize' || resumeStore.activeTab === 'write'
})

const llmStreamEnded = () => {
  return IAMInterviewerStore.status === Status.COMPLETED || optimizationStore.status === Status.COMPLETED
}

const downloadButtonDisabled = computed(() => {
  return !llmStreamEnded()
})
const copyButtonDisabled = computed(() => {
  return !llmStreamEnded()
})
</script>

<style scoped>
/* 可选：添加一些额外的动画样式 */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 自定义滚动条样式 - 更细更明显的渐变 */
.custom-scrollbar::-webkit-scrollbar {
  width: 2px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f3f4f6; /* 浅灰色轨道 */
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #a855f7, #9692e9); /* 更鲜明的紫色到靛蓝色渐变 */
  border-radius: 10px;
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #9333ea, #3730a3); /* 悬停时更深的颜色 */
}

/* Firefox 滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin; /* Firefox的thin比默认的更细 */
  scrollbar-color: #8b5cf6 #f3f4f6;
}

/* Markdown 源码样式 */
.markdown-source {
  max-height: 850px;
  overflow-y: auto;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.7;
  padding-right: 4px; /* 为滚动条预留空间，从6px减小到4px */
}

.markdown-source pre {
  margin: 0;
  padding: 0;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1rem;
}

.markdown-source code {
  white-space: pre-wrap;
  word-break: break-word;
  color: #686c72;
}
</style>
