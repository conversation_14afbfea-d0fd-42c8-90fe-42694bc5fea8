<template>
  <div class="mx-auto mb-6 flex max-w-4xl justify-center overflow-hidden rounded-lg bg-white shadow-sm">
    <button
      v-for="tab in tabs"
      :key="tab.value"
      class="tab-button relative flex-1 px-6 py-3 text-center text-sm font-medium transition-all duration-500 ease-in-out"
      :class="resumeStore.activeTab === tab.value ? 'bg-purple-100 text-purple-800' : 'bg-white text-gray-600 hover:bg-gray-50'"
      @click="resumeStore.switchTab(tab.value as Tab)"
    >
      <div class="flex items-center justify-center">
        <component :is="tab.icon" class="mr-2 h-4 w-4 transition-colors duration-500 ease-in-out" />
        {{ tab.label }}
      </div>
      <div
        class="absolute bottom-0 left-1/2 h-0.5 -translate-x-1/2 transform transition-all duration-500 ease-in-out"
        :class="resumeStore.activeTab === tab.value ? 'w-20 bg-purple-600' : 'w-0 bg-transparent'"
      ></div>
    </button>
  </div>
</template>

<script setup lang="ts">
import { useResumeStore } from '@/stores/resume'
const resumeStore = useResumeStore()
const tabs = [
  {
    value: 'write',
    label: '简历扩写',
    icon: defineComponent({
      render() {
        return h(
          'svg',
          {
            xmlns: 'http://www.w3.org/2000/svg',
            class: 'h-4 w-4',
            viewBox: '0 0 20 20',
            fill: 'currentColor',
          },
          [
            h('path', {
              d: 'M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z',
            }),
          ],
        )
      },
    }),
  },
  {
    value: 'optimize',
    label: '简历优化',
    icon: defineComponent({
      render() {
        return h(
          'svg',
          {
            xmlns: 'http://www.w3.org/2000/svg',
            class: 'h-4 w-4',
            viewBox: '0 0 20 20',
            fill: 'currentColor',
          },
          [
            h('path', {
              d: 'M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z',
            }),
          ],
        )
      },
    }),
  },

  {
    value: 'interviewer',
    label: '面试题预测',
    icon: defineComponent({
      render() {
        return h(
          'svg',
          {
            xmlns: 'http://www.w3.org/2000/svg',
            class: 'h-4 w-4',
            viewBox: '0 0 20 20',
            fill: 'currentColor',
          },
          [
            h('path', {
              d: 'M7.75 2.75a.75.75 0 00-1.5 0v1.258a32.987 32.987 0 00-3.599.278.75.75 0 10.198 1.487A31.545 31.545 0 018.7 5.545 19.381 19.381 0 017 9.56a19.418 19.418 0 01-1.002-2.05.75.75 0 00-1.384.577 20.935 20.935 0 001.492 2.91 19.613 19.613 0 01-3.828 4.154.75.75 0 10.945 1.164A21.116 21.116 0 007 12.331c.095.132.192.262.29.391a.75.75 0 001.194-.91c-.204-.266-.4-.538-.59-.815a20.888 20.888 0 002.333-5.332c.31.031.618.068.926.086a.75.75 0 00.15-1.492 32.832 32.832 0 01-3.599-.278V2.75z',
            }),
            h('path', {
              'fill-rule': 'evenodd',
              d: 'M13 8a.75.75 0 01.671.415l4.25 8.5a.75.75 0 11-1.342.67L15.787 16h-5.573l-.793 1.585a.75.75 0 11-1.342-.67l4.25-8.5A.75.75 0 0113 8zm2.037 6.5L13 10.427 10.964 14.5h4.073z',
              'clip-rule': 'evenodd',
            }),
          ],
        )
      },
    }),
  },
]
</script>
