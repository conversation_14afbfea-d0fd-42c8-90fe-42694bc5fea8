<template>
  <div class="mx-auto max-w-2xl">
    <div class="flex items-center justify-between">
      <!-- 阶段1: 诊断分析 -->
      <div class="flex items-center">
        <div 
          class="flex h-10 w-10 items-center justify-center rounded-full border-2 transition-all duration-300"
          :class="[
            currentStage === 'stage-one' || stageOneCompleted 
              ? 'border-purple-500 bg-purple-500 text-white' 
              : 'border-gray-300 bg-white text-gray-500'
          ]"
        >
          <svg v-if="stageOneCompleted" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <span v-else class="text-sm font-medium">1</span>
        </div>
        <div class="ml-3">
          <div class="text-sm font-medium" :class="currentStage === 'stage-one' || stageOneCompleted ? 'text-purple-600' : 'text-gray-500'">
            诊断分析
          </div>
          <div class="text-xs text-gray-500">分析简历与职位匹配度</div>
        </div>
      </div>

      <!-- 连接线 -->
      <div class="flex-1 mx-4">
        <div 
          class="h-0.5 transition-all duration-500"
          :class="stageOneCompleted ? 'bg-purple-500' : 'bg-gray-300'"
        ></div>
      </div>

      <!-- 阶段2: 简历优化 -->
      <div class="flex items-center">
        <div 
          class="flex h-10 w-10 items-center justify-center rounded-full border-2 transition-all duration-300"
          :class="[
            currentStage === 'stage-two' || stageTwoCompleted 
              ? 'border-purple-500 bg-purple-500 text-white' 
              : 'border-gray-300 bg-white text-gray-500'
          ]"
        >
          <svg v-if="stageTwoCompleted" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
          <span v-else class="text-sm font-medium">2</span>
        </div>
        <div class="ml-3">
          <div class="text-sm font-medium" :class="currentStage === 'stage-two' || stageTwoCompleted ? 'text-purple-600' : 'text-gray-500'">
            简历优化
          </div>
          <div class="text-xs text-gray-500">生成优化后的简历</div>
        </div>
      </div>
    </div>

    <!-- 当前阶段状态指示 -->
    <div class="mt-4 text-center">
      <div v-if="currentStage === 'idle'" class="text-sm text-gray-500">
        请输入简历内容开始诊断
      </div>
      <div v-else-if="currentStage === 'stage-one'" class="flex items-center justify-center text-sm text-purple-600">
        <div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-purple-200 border-t-purple-600"></div>
        正在分析简历内容...
      </div>
      <div v-else-if="currentStage === 'stage-two'" class="flex items-center justify-center text-sm text-purple-600">
        <div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-purple-200 border-t-purple-600"></div>
        正在生成优化简历...
      </div>
      <div v-else-if="stageOneCompleted && !stageTwoCompleted" class="text-sm text-green-600">
        ✓ 诊断完成，点击"生成优化简历"继续
      </div>
      <div v-else-if="stageTwoCompleted" class="text-sm text-green-600">
        ✓ 简历优化完成
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  currentStage: 'idle' | 'stage-one' | 'stage-two'
  stageOneCompleted: boolean
  stageTwoCompleted: boolean
}

defineProps<Props>()
</script> 