<template>
  <div>
    <div class="mb-6">
      <!-- 文本输入区域 -->
      <div class="mb-6">
        <div
          class="relative rounded-lg border border-gray-300 shadow-sm transition-all duration-300 focus-within:border-2 focus-within:border-dashed focus-within:border-purple-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-purple-200"
        >
          <textarea
            v-model="resumeEditStore.resumeInput.content"
            class="block h-80 w-full resize-none rounded-t-lg border-0 p-4 text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-0"
            placeholder="请输入帮您扩写的简历内容(可以是工作内容、个人技能，个人优势等)"
          ></textarea>
          <!-- 文本工具栏 -->
          <div class="flex items-center rounded-b-lg border-t border-gray-200 bg-white px-5 py-3">
            <div class="flex w-full items-center justify-start space-x-6">
              <!-- 增强模式选择 -->
              <div class="relative">
                <div
                  @click.stop="toggleDropdown('enhancement')"
                  class="dropdown-trigger group relative flex cursor-pointer items-center text-sm text-gray-700 hover:text-gray-900"
                  aria-label="优化模式"
                >
                  <EnhancementIcon class="h-5 w-5 text-gray-600" />
                  <span class="ml-2">{{ getEnhancementName(resumeEditStore.attachment.enhancementMode) }}</span>
                  <DropdownArrowIcon class="ml-1.5 h-4 w-4 text-gray-500" />
                  <div class="absolute left-0 top-0 -mt-8 hidden w-24 rounded bg-purple-500 px-2 py-1 text-xs text-white group-hover:block">优化模式</div>
                </div>

                <!-- 下拉选项 -->
                <div
                  v-if="dropdownState.enhancement"
                  class="dropdown-options absolute left-0 top-full z-10 mt-2 w-48 rounded-lg border border-gray-200 bg-white p-1 shadow-lg focus:outline-none"
                >
                  <div
                    v-for="option in ENHANCEMENT_OPTIONS"
                    :key="option.value"
                    @click="selectEnhancement(option.value as EnhancementMode)"
                    class="relative my-1 flex cursor-pointer items-center rounded-md px-3 py-2 text-sm hover:bg-purple-50"
                    :class="resumeEditStore.attachment.enhancementMode === option.value ? 'bg-purple-50 text-purple-700' : 'text-gray-700'"
                  >
                    <span v-if="resumeEditStore.attachment.enhancementMode === option.value" class="text-purple-600"> </span>
                    <span class="block" :class="resumeEditStore.attachment.enhancementMode === option.value ? 'font-medium' : 'font-normal'">
                      {{ option.label }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 语言选择 -->
              <div class="relative">
                <div
                  @click.stop="toggleDropdown('language')"
                  class="dropdown-trigger group flex cursor-pointer items-center text-sm text-gray-700 hover:text-gray-900"
                  aria-label="目标语言"
                >
                  <TargetLangIcon class="h-5 w-5 text-gray-600" />
                  <span class="ml-2">{{ getLanguageName(resumeEditStore.attachment.targetLanguage) }}</span>
                  <DropdownArrowIcon class="ml-1.5 h-4 w-4 text-gray-500" />
                  <div class="absolute left-0 top-0 -mt-8 hidden w-24 rounded bg-purple-500 px-2 py-1 text-xs text-white group-hover:block">输出语言</div>
                </div>
                <!-- 下拉选项 -->
                <div
                  v-if="dropdownState.language"
                  class="dropdown-options absolute left-0 top-full z-10 mt-2 w-36 rounded-lg border border-gray-200 bg-white p-1 shadow-lg focus:outline-none"
                >
                  <div
                    v-for="option in LANGUAGE_OPTIONS"
                    :key="option.value"
                    @click="selectLanguage(option.value as Language)"
                    class="relative my-1 flex cursor-pointer items-center rounded-md px-3 py-2 text-sm hover:bg-purple-50"
                    :class="resumeEditStore.attachment.targetLanguage === option.value ? 'bg-purple-50 text-purple-700' : 'text-gray-700'"
                  >
                    <span v-if="resumeEditStore.attachment.targetLanguage === option.value" class="text-purple-600"> </span>
                    <span class="block" :class="resumeEditStore.attachment.targetLanguage === option.value ? 'font-medium' : 'font-normal'">
                      {{ option.label }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 职位类型选择 -->
              <div class="relative">
                <div
                  @click.stop="toggleDropdown('jobType')"
                  class="dropdown-trigger group flex cursor-pointer items-center text-sm text-gray-700 hover:text-gray-900"
                  aria-label="职位类型"
                >
                  <JobTypeIcon class="h-5 w-5 text-gray-600" />
                  <span class="ml-2">{{ resumeEditStore.attachment.jobType ? getJobTypeName(resumeEditStore.attachment.jobType) : '选择职位类型' }}</span>
                  <DropdownArrowIcon class="ml-1.5 h-4 w-4 text-gray-500" />
                  <div class="bg-gray-350 absolute left-0 top-0 -mt-8 hidden w-24 rounded bg-purple-500 px-2 py-1 text-xs text-white group-hover:block">
                    职位类型
                  </div>
                </div>

                <!-- 下拉选项 -->
                <div
                  v-if="dropdownState.jobType"
                  class="dropdown-options absolute left-0 top-full z-10 mt-2 max-h-60 w-48 overflow-y-auto rounded-lg border border-gray-200 bg-white p-1 shadow-lg focus:outline-none"
                >
                  <div
                    v-for="option in JOB_TYPE_OPTIONS"
                    :key="option.value"
                    @click="selectJobType(option.value as JobType)"
                    class="relative my-1 flex cursor-pointer items-center rounded-md px-3 py-2 text-sm hover:bg-purple-50"
                    :class="resumeEditStore.attachment.jobType === option.value ? 'bg-purple-50 text-purple-700' : 'text-gray-700'"
                  >
                    <span v-if="resumeEditStore.attachment.jobType === option.value" class="text-purple-600"> </span>
                    <span class="block" :class="resumeEditStore.attachment.jobType === option.value ? 'font-medium' : 'font-normal'">
                      {{ option.label }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 选择编写模块 -->
      <div class="mb-6">
        <h3 class="mb-3 text-sm font-medium text-gray-700">选择编写的模块</h3>
        <div class="grid grid-cols-2 gap-4">
          <div v-for="option in RESUME_EDIT_MODULE_OPTIONS" :key="option.value" class="flex items-center">
            <input
              type="radio"
              :id="option.value"
              name="editOption"
              class="h-4 w-4 border-gray-300 text-purple-600 focus:ring-purple-500"
              :value="option.value"
              v-model="resumeEditStore.attachment.module"
            />
            <label :for="option.value" class="ml-2 text-sm text-gray-700">{{ option.label }}</label>
          </div>
        </div>
      </div>

      <!-- JD信息 -->
      <div class="mb-6">
        <div class="mb-3 flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium text-gray-700">岗位信息</h3>
            <p class="mt-2 text-[10px] text-gray-400">提供岗位职责、任职要求等岗位信息，效果会更佳哟</p>
          </div>
          <div class="relative inline-block w-12 select-none align-middle">
            <input type="checkbox" id="toggle-jd" class="peer sr-only" v-model="resumeEditStore.uiState.showJdInput" />
            <label
              for="toggle-jd"
              class="block h-6 w-11 cursor-pointer rounded-full bg-gray-300 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-purple-500 peer-checked:after:translate-x-full peer-focus:outline-none"
            ></label>
          </div>
        </div>
        <textarea
          v-if="resumeEditStore.uiState.showJdInput"
          v-model="resumeEditStore.attachment.jobInfo"
          class="mt-1 block h-32 w-full resize-none rounded-md border p-4 placeholder-gray-400 shadow-sm placeholder:text-xs sm:text-sm"
          :placeholder="PLACEHOLDERS.jobInfo"
        ></textarea>
      </div>

      <!-- 编写按钮 -->
      <button
        @click="handleSubmit"
        class="relative flex w-full items-center justify-center overflow-hidden rounded-xl bg-gradient-to-r from-purple-600 to-pink-500 px-4 py-3 text-white shadow-md shadow-purple-500/20 transition-all duration-300 hover:translate-y-[-2px] hover:from-purple-700 hover:to-pink-600 hover:shadow-lg hover:shadow-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
        :disabled="!resumeEditStore.isFormValid"
        :class="{ 'cursor-not-allowed opacity-70': !resumeEditStore.isFormValid }"
      >
        <EditIcon class="mr-2 h-5 w-5" />
        开始编写
        <span class="button-shine"></span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  useResumeEditStore,
  dropdownState,
  LANGUAGE_OPTIONS,
  ENHANCEMENT_OPTIONS,
  JOB_TYPE_OPTIONS,
  Language,
  EnhancementMode,
  JobType,
  Status,
  RESUME_EDIT_MODULE_OPTIONS,
  PLACEHOLDERS,
} from '@/stores/resume'
import EnhancementIcon from '@/assets/icons/enhancement-icon.svg'
import TargetLangIcon from '@/assets/icons/target-lang.svg'
import JobTypeIcon from '@/assets/icons/job-type-icon.svg'
import DropdownArrowIcon from '@/assets/icons/dropdown-arrow-icon.svg'
import EditIcon from '@/assets/icons/edit-icon.svg'
import { useToast } from 'vue-toast-notification'

const resumeEditStore = useResumeEditStore()
const toast = useToast()
const { checkCreditsAndExecute } = useCreditsGuard()

// UI操作函数
const toggleDropdown = (dropdownName: keyof typeof dropdownState) => {
  Object.keys(dropdownState).forEach(key => {
    dropdownState[key as keyof typeof dropdownState] = key === dropdownName ? !dropdownState[key as keyof typeof dropdownState] : false
  })
}

const closeAllDropdowns = () => {
  Object.keys(dropdownState).forEach(key => {
    dropdownState[key as keyof typeof dropdownState] = false
  })
}

// 绑定全局点击事件关闭下拉菜单
onMounted(() => {
  document.addEventListener('click', (event: MouseEvent) => {
    const target = event.target as HTMLElement
    if (!target.closest('.dropdown-trigger') && !target.closest('.dropdown-options')) {
      closeAllDropdowns()
    }
  })
})

onUnmounted(() => {
  document.removeEventListener('click', closeAllDropdowns)
})

// 业务函数 - 委托给store处理数据
const selectLanguage = (value: Language) => {
  resumeEditStore.updateAttachment('targetLanguage', value)
  dropdownState.language = false
}

const selectEnhancement = (value: EnhancementMode) => {
  resumeEditStore.updateAttachment('enhancementMode', value)
  dropdownState.enhancement = false
}

const selectJobType = (value: JobType) => {
  resumeEditStore.updateAttachment('jobType', value)
  dropdownState.jobType = false
}

// 处理帮写请求
const editRequest = async (resumeContent: string) => {
  const apiParams = {
    text: resumeContent,
    model: resumeEditStore.attachment.enhancementMode,
    resumeModule: resumeEditStore.attachment.module,
    language: resumeEditStore.attachment.targetLanguage,
    jobType: resumeEditStore.attachment.jobType,
    jobInfo: resumeEditStore.attachment.jobInfo || undefined,
  }

  return (await $fetch('/api/resume/help-edit', {
    method: 'POST',
    body: apiParams,
    responseType: 'stream',
  })) as ReadableStream
}

const handleSubmit = async () => {
  // 表单验证
  if (!resumeEditStore.resumeInput.content) {
    toast.error('请输入简历内容')
    return
  }
  if (!resumeEditStore.attachment.jobType) {
    toast.error('请选择职位类型')
    return
  }

  await checkCreditsAndExecute(async () => {
    try {
      // 开始处理
      resumeEditStore.status = Status.PROCESSING
      resumeEditStore.output.content = '' // 清空之前的内容

      // 获取简历内容
      const resumeContent = resumeEditStore.resumeInput.content

      // 处理请求
      const response = await editRequest(resumeContent)

      // 处理流式响应
      const reader = response.getReader()
      const decoder = new TextDecoder()
      let result = ''
      resumeEditStore.status = Status.COMPLETED

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        result += chunk
        resumeEditStore.output.content = result
      }
    } catch (error) {
      resumeEditStore.status = Status.ERROR
      toast.error('编写失败，请重试')
    }
  })
}

const getLanguageName = (value: string): string => {
  const option = LANGUAGE_OPTIONS.find(option => option.value === value)
  return option ? option.label : '中文简体'
}

const getEnhancementName = (value: string): string => {
  const option = ENHANCEMENT_OPTIONS.find(option => option.value === value)
  return option ? option.label : '标准优化'
}

const getJobTypeName = (value: string): string => {
  const option = JOB_TYPE_OPTIONS.find(option => option.value === value)
  return option ? option.label : '通用类'
}
</script>
