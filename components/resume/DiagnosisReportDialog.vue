<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" class="relative z-50" @close="handleClose">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-50" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
              <!-- 头部 -->
              <div class="relative bg-gradient-to-r from-purple-600 to-pink-500 px-6 py-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="flex h-10 w-10 items-center justify-center rounded-full bg-white bg-opacity-20">
                      <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div class="ml-3">
                      <h3 class="text-lg font-semibold text-white">简历诊断报告</h3>
                      <p class="text-sm text-purple-100">AI 智能分析完成</p>
                    </div>
                  </div>
                  <button @click="handleClose" class="rounded-full p-2 text-white hover:bg-white hover:bg-opacity-20 transition-colors">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- 内容区域 -->
              <div class="max-h-[70vh] overflow-y-auto p-6">
                <div class="space-y-6">
                  <!-- 技能匹配分析 -->
                  <div v-if="diagnosisResult.analyzer_sop" class="rounded-lg border border-blue-200 bg-blue-50 p-4">
                    <h4 class="mb-3 flex items-center text-lg font-semibold text-blue-800">
                      <svg class="mr-2 h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                      </svg>
                      技能匹配分析
                    </h4>
                    
                    <div v-if="diagnosisResult.analyzer_sop.skill_matching" class="mb-4">
                      <h5 class="mb-2 text-sm font-medium text-blue-700">技能匹配情况</h5>
                      <div class="space-y-2">
                        <div v-for="(skill, index) in diagnosisResult.analyzer_sop.skill_matching" :key="index" class="rounded-md bg-white p-3 text-sm text-gray-700 shadow-sm">
                          {{ skill }}
                        </div>
                      </div>
                    </div>

                    <div v-if="diagnosisResult.analyzer_sop.strength_skills" class="mb-4">
                      <h5 class="mb-2 text-sm font-medium text-blue-700">优势技能</h5>
                      <div class="flex flex-wrap gap-2">
                        <span v-for="(skill, index) in diagnosisResult.analyzer_sop.strength_skills" :key="index" class="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800">
                          {{ skill }}
                        </span>
                      </div>
                    </div>

                    <div v-if="diagnosisResult.analyzer_sop.matching_experiences" class="mb-4">
                      <h5 class="mb-2 text-sm font-medium text-blue-700">匹配经验</h5>
                      <div class="space-y-2">
                        <div v-for="(exp, index) in diagnosisResult.analyzer_sop.matching_experiences" :key="index" class="rounded-md bg-white p-3 text-sm text-gray-700 shadow-sm">
                          {{ exp }}
                        </div>
                      </div>
                    </div>

                    <!-- 处理原始响应情况 -->
                    <div v-if="diagnosisResult.analyzer_sop.raw_response" class="rounded-md bg-white p-3 text-sm text-gray-700 shadow-sm whitespace-pre-wrap">
                      {{ diagnosisResult.analyzer_sop.raw_response }}
                    </div>
                  </div>

                  <!-- 问题诊断 -->
                  <div v-if="diagnosisResult.diagnosis_sop" class="rounded-lg border border-orange-200 bg-orange-50 p-4">
                    <h4 class="mb-3 flex items-center text-lg font-semibold text-orange-800">
                      <svg class="mr-2 h-5 w-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.728-.833-2.498 0L4.316 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      问题诊断
                    </h4>
                    
                    <div v-if="diagnosisResult.diagnosis_sop.improvement_points" class="space-y-3">
                      <div v-for="(point, index) in diagnosisResult.diagnosis_sop.improvement_points" :key="index" class="rounded-md bg-white p-3 text-sm text-gray-700 shadow-sm">
                        {{ point }}
                      </div>
                    </div>

                    <div v-if="diagnosisResult.diagnosis_sop.raw_response" class="rounded-md bg-white p-3 text-sm text-gray-700 shadow-sm whitespace-pre-wrap">
                      {{ diagnosisResult.diagnosis_sop.raw_response }}
                    </div>
                  </div>

                  <!-- 优化建议 -->
                  <div v-if="diagnosisResult.suggestions_sop" class="rounded-lg border border-purple-200 bg-purple-50 p-4">
                    <h4 class="mb-3 flex items-center text-lg font-semibold text-purple-800">
                      <svg class="mr-2 h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                      优化建议
                    </h4>
                    
                    <div v-if="diagnosisResult.suggestions_sop.suggestions" class="space-y-4">
                      <div v-for="(suggestion, index) in diagnosisResult.suggestions_sop.suggestions" :key="index" class="rounded-md border-l-4 border-purple-400 bg-white p-4 shadow-sm">
                        <h5 class="font-semibold text-purple-800">{{ suggestion.title }}</h5>
                        <p class="mt-2 text-sm text-purple-700 whitespace-pre-wrap">{{ suggestion.content }}</p>
                      </div>
                    </div>

                    <div v-if="diagnosisResult.suggestions_sop.raw_response" class="rounded-md bg-white p-3 text-sm text-gray-700 shadow-sm whitespace-pre-wrap">
                      {{ diagnosisResult.suggestions_sop.raw_response }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 底部操作按钮 -->
              <div class="bg-gray-50 px-6 py-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center text-sm text-gray-600">
                    <svg class="mr-2 h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    诊断完成，您可以选择生成优化后的简历
                  </div>
                  <div class="flex space-x-3">
                    <button
                      @click="handleClose"
                      class="rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                    >
                      稍后处理
                    </button>
                    <button
                      @click="handleGenerateResume"
                      class="flex items-center rounded-lg bg-gradient-to-r from-green-600 to-teal-500 px-4 py-2 text-sm font-medium text-white shadow-sm hover:from-green-700 hover:to-teal-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                    >
                      <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      生成优化简历
                    </button>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, TransitionChild, TransitionRoot } from '@headlessui/vue'

interface Props {
  isOpen: boolean
  diagnosisResult: {
    analyzer_sop: any
    diagnosis_sop: any
    suggestions_sop: any
  }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  generateResume: []
}>()

const handleClose = () => {
  emit('close')
}

const handleGenerateResume = () => {
  emit('generateResume')
  handleClose()
}
</script> 