<template>
  <div class="flex flex-col items-center justify-center py-16">
    <div class="relative h-16 w-16">
      <!-- 脉冲动画 -->
      <div class="absolute inset-0 animate-ping rounded-full bg-purple-400 opacity-30"></div>
      <!-- 渐变圆环 -->
      <div class="absolute inset-0 rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 opacity-75 blur-sm"></div>
      <!-- 旋转动画 -->
      <div class="absolute inset-0 animate-spin rounded-full border-4 border-transparent border-t-white opacity-90"></div>
      <!-- 中心点 -->
      <div class="absolute inset-0 flex items-center justify-center">
        <div class="h-2 w-2 rounded-full bg-white"></div>
      </div>
    </div>
    <p class="mt-4 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-lg font-semibold text-transparent">
      {{ message || '正在处理中...' }}
    </p>

    <!-- 进度指示器 -->
    <div v-if="showProgress" class="mt-8 w-64">
      <div class="h-2 w-full overflow-hidden rounded-full bg-gray-200">
        <div class="h-full animate-pulse rounded-full bg-gradient-to-r from-indigo-500 to-purple-500"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  message?: string
  showProgress?: boolean
}>()
</script>
