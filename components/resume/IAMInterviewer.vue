<template>
  <div>
    <div class="mb-6">
      <!-- 文件上传区域 -->
      <div
        class="relative cursor-pointer rounded-xl transition-all duration-300"
        :class="[
          resumeFile
            ? 'border border-indigo-200 bg-white shadow-sm'
            : 'border-2 border-dashed border-indigo-100/80 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 hover:translate-y-[-2px] hover:border-purple-300 hover:shadow-md hover:shadow-purple-100/40',
        ]"
        @dragover="handleDragOver"
        @drop="handleDrop"
        @click="!resumeFile && IAMInterviewerStore.uploadFileStatus !== Status.PROCESSING && fileInput!.click()"
      >
        <input type="file" ref="fileInput" @change="handleFileChange" accept="application/pdf,.doc" class="hidden" />

        <!-- 加载状态 -->
        <div v-if="IAMInterviewerStore.uploadFileStatus === Status.PROCESSING" class="absolute inset-0 z-10 bg-white/95">
          <ResumeLoadingIndicator message="正在解析文件..." :showProgress="true" />
        </div>

        <!-- 无文件状态 -->
        <div v-if="!resumeFile" class="p-8 text-center">
          <div class="flex flex-col items-center justify-center">
            <div
              class="mb-5 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100/80 p-4 text-purple-500 shadow-sm shadow-purple-100/50 transition-all duration-300 hover:scale-105"
            >
              <svg class="h-9 w-9" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M472 269.822l-60.268 78.53c-13.45 17.526-38.56 20.83-56.085 7.38s-20.829-38.56-7.38-56.085l132-172c16.012-20.863 47.454-20.863 63.465 0l132 172c13.45 17.526 10.146 42.636-7.38 56.085-17.525 13.45-42.635 10.146-56.084-7.38L552 269.823V704c0 22.091-17.909 40-40 40s-40-17.909-40-40V269.822zM832 512c0-22.091 17.909-40 40-40s40 17.909 40 40v288c0 61.856-50.144 112-112 112H224c-61.856 0-112-50.144-112-112V512c0-22.091 17.909-40 40-40s40 17.909 40 40v288c0 17.673 14.327 32 32 32h576c17.673 0 32-14.327 32-32V512z"
                  fill="currentColor"
                />
              </svg>
            </div>
            <h2 class="text-base font-medium text-gray-700">拖拽简历文件到此处或点击上传</h2>
            <p class="mt-1 text-sm text-gray-500">支持PDF/doc格式</p>
            <button
              class="mt-5 flex items-center rounded-lg bg-gradient-to-r from-purple-500 to-purple-600 px-5 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200 hover:translate-y-[-1px] hover:shadow-md hover:shadow-purple-200"
              @click.stop="fileInput!.click()"
              :disabled="IAMInterviewerStore.uploadFileStatus === Status.PROCESSING"
            >
              选择文件
            </button>
          </div>
        </div>

        <!-- 有文件状态 -->
        <div v-else>
          <div class="rounded-t-xl p-6">
            <div class="flex flex-col items-center">
              <div class="mb-4 flex h-14 w-14 flex-shrink-0 items-center justify-center rounded-full bg-purple-100 transition-all duration-300 hover:scale-105">
                <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>

              <!-- 文件名和大小 -->
              <div class="mb-2 text-center">
                <h3 class="text-base font-medium text-gray-800">{{ resumeFile.name }}</h3>
                <p class="mt-0.5 text-sm text-gray-500">{{ formatFileSize(resumeFile.size) }}</p>
              </div>

              <!-- 操作按钮 -->
              <div class="mt-4 flex space-x-3">
                <button
                  @click.stop="fileInput!.click()"
                  class="flex items-center rounded-lg border border-purple-200 bg-purple-50 px-4 py-2 text-sm font-medium text-purple-600 transition-all duration-200 hover:bg-purple-100 hover:shadow-sm"
                  :disabled="IAMInterviewerStore.status === Status.PROCESSING"
                >
                  <svg class="mr-1.5 h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M17 8l-5-5-5 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M12 3v12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                  更换文件
                </button>
                <button
                  @click.stop="removeFile"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-600 transition-all duration-200 hover:border-red-200 hover:bg-red-50 hover:text-pink-400"
                  :disabled="IAMInterviewerStore.status === Status.PROCESSING"
                >
                  <svg class="mr-1.5 h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 6h18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  移除文件
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- JD信息 -->
      <div class="mt-6">
        <div class="mb-3">
          <h3 class="text-sm font-medium text-gray-700">岗位信息</h3>
          <p class="mt-2 text-[10px] text-gray-400">提供岗位职责、任职要求等岗位信息，效果会更佳哟</p>
        </div>
        <div class="relative">
          <textarea
            v-model="jobInfo"
            class="mt-1 block h-32 w-full resize-none rounded-md border border-gray-300 p-4 placeholder-gray-400 shadow-sm placeholder:text-xs focus:border-purple-500 focus:outline-none focus:ring-purple-500 focus:ring-opacity-50 sm:text-sm"
            :placeholder="PLACEHOLDERS.jobInfo"
          ></textarea>
          <div class="absolute bottom-2 right-2 text-xs text-gray-400">{{ jobInfo.length }}/2000</div>
        </div>
      </div>

      <!-- 面试题预测数量 -->
      <div class="mt-6" v-if="interviewerRole === 'profession'">
        <h3 class="mb-3 text-sm font-medium text-gray-700">选择面试题数量</h3>
        <div class="grid grid-cols-3 gap-4">
          <div class="flex items-center">
            <input
              type="radio"
              id="count-large"
              name="questionCount"
              class="h-4 w-4 border-gray-300 text-purple-600 focus:ring-purple-500"
              value="large"
              v-model="questionCount"
            />
            <label for="count-large" class="ml-2 text-sm text-gray-700">{{ QUESTION_COUNT_CONFIG.large.label }}</label>
          </div>
          <div class="flex items-center">
            <input
              type="radio"
              id="count-medium"
              name="questionCount"
              class="h-4 w-4 border-gray-300 text-purple-600 focus:ring-purple-500"
              value="medium"
              v-model="questionCount"
            />
            <label for="count-medium" class="ml-2 text-sm text-gray-700">{{ QUESTION_COUNT_CONFIG.medium.label }}</label>
          </div>
          <div class="flex items-center">
            <input
              type="radio"
              id="count-small"
              name="questionCount"
              class="h-4 w-4 border-gray-300 text-purple-600 focus:ring-purple-500"
              value="small"
              v-model="questionCount"
            />
            <label for="count-small" class="ml-2 text-sm text-gray-700">{{ QUESTION_COUNT_CONFIG.small.label }}</label>
          </div>
        </div>
      </div>

      <!-- 面试官类型选择 -->
      <div class="mt-6">
        <h3 class="mb-3 text-sm font-medium text-gray-700">选择预测方向</h3>
        <div class="grid grid-cols-2 gap-4">
          <div
            @click="interviewerRole = 'profession'"
            class="flex cursor-pointer items-center justify-center rounded-lg border-2 px-4 py-3 transition-colors"
            :class="interviewerRole === 'profession' ? 'border-purple-500 bg-purple-50 text-purple-800' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
          >
            <span class="text-sm font-medium">专业面</span>
          </div>
          <div
            @click="interviewerRole = 'hr'"
            class="flex cursor-pointer items-center justify-center rounded-lg border-2 px-4 py-3 transition-colors"
            :class="interviewerRole === 'hr' ? 'border-purple-500 bg-purple-50 text-purple-800' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
          >
            <span class="text-sm font-medium">HR面</span>
          </div>
        </div>
      </div>

      <!-- 提交按钮 -->
      <button
        @click="handleSubmit"
        class="relative mt-6 flex w-full items-center justify-center overflow-hidden rounded-xl bg-gradient-to-r from-purple-600 to-pink-500 px-4 py-3 text-white shadow-md shadow-purple-500/20 transition-all duration-300 hover:translate-y-[-2px] hover:from-purple-700 hover:to-pink-600 hover:shadow-lg hover:shadow-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
        :disabled="!isFormValid"
        :class="{ 'cursor-not-allowed opacity-70': !isFormValid }"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path
            fill-rule="evenodd"
            d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
            clip-rule="evenodd"
          />
        </svg>
        开始提问
        <span class="button-shine"></span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useToast } from 'vue-toast-notification'
import { useIAMInterviewerStore } from '@/stores/resume'
import { formatFileSize } from '@/utils/file'
import { PLACEHOLDERS } from '@/stores/resume'

const toast = useToast()
const IAMInterviewerStore = useIAMInterviewerStore()
const { checkCreditsAndExecute } = useCreditsGuard()

const fileInput = ref<HTMLInputElement | null>(null)
const resumeFile = ref<File | null>(null)
const resumeContent = ref('')

const interviewerRole = ref<'profession' | 'hr'>('profession')
const jobInfo = ref('')

// 面试题数量配置
const QUESTION_COUNT_CONFIG = {
  small: { label: '3题(每题3-4小问)', groupCount: '3', perGroup: '4' },
  medium: { label: '5题(每题3-4小问)', groupCount: '5', perGroup: '4' },
  large: { label: '10题(每题3-4小问)', groupCount: '10', perGroup: '4' },
} as const

const questionCount = ref<keyof typeof QUESTION_COUNT_CONFIG>('medium')

const isFormValid = computed(() => {
  return resumeFile.value !== null && jobInfo.value.trim() !== ''
})

const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
}

const handleDrop = async (e: DragEvent) => {
  e.preventDefault()
  if (e.dataTransfer?.files.length) {
    const file = e.dataTransfer.files[0]
    if (file.type === 'application/pdf' || file.name.endsWith('.doc')) {
      await processPdfFile(file)
    }
  }
}

const handleFileChange = async (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    const file = target.files[0]
    await processPdfFile(file)
  }
}

interface ParseResponse {
  success: boolean
  data: {
    markdown?: string
    message?: string
  }
}
const processPdfFile = async (file: File) => {
  try {
    IAMInterviewerStore.uploadFileStatus = Status.PROCESSING
    const formData = new FormData()
    formData.append('file', file)
    // 文件类型只支持PDF
    if (file.type !== 'application/pdf') {
      toast.error('文件类型错误，请上传PDF文件', {
        duration: 3000,
        position: 'top-right',
      })
      return
    }

    // 文件大小限制
    const response = await $fetch<ParseResponse>('/api/file/file-to-md', {
      method: 'POST',
      body: formData,
    })
    if (!response.success || !response.data.markdown) {
      throw new Error(`file parse error !`)
    }
    resumeContent.value = response.data.markdown
    resumeFile.value = file
  } catch (error: any) {
    toast.error('文件解析失败，请稍后重试！', {
      duration: 3000,
      position: 'top-right',
    })
  } finally {
    IAMInterviewerStore.uploadFileStatus = Status.PENDING
  }
}
const removeFile = (e: Event) => {
  e.stopPropagation()
  resumeFile.value = null
  resumeContent.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  await checkCreditsAndExecute(async () => {
    try {
      IAMInterviewerStore.status = Status.PROCESSING
      IAMInterviewerStore.output.content = '' // 清空之前内容

      // 解析questionCount参数
      const questionCountObj = {
        questionGroupCount: '',
        questionPerGroup: '',
      }

      if (questionCount.value) {
        const config = QUESTION_COUNT_CONFIG[questionCount.value]
        questionCountObj.questionGroupCount = config.groupCount
        questionCountObj.questionPerGroup = config.perGroup
      }

      const apiParams: any = {
        text: resumeContent.value,
        role: interviewerRole.value,
        model: 'standard',
        jobInfo: jobInfo.value,
        questionCountObj: questionCountObj, // 目前hr视角暂不处理
      }

      const response = (await $fetch('/api/resume/generate-questions', {
        method: 'POST',
        body: apiParams,
        responseType: 'stream',
      })) as ReadableStream

      if (!response) {
        throw new Error('模型调用失败')
      }

      const reader = response.getReader()
      const decoder = new TextDecoder()
      let result = ''
      IAMInterviewerStore.status = Status.COMPLETED
      while (true) {
        const { done, value } = await reader.read()
        if (done) break
        const chunk = decoder.decode(value, { stream: true })
        result += chunk
        IAMInterviewerStore.output.content = result // 实时更新内容
      }
    } catch (error) {
      IAMInterviewerStore.status = Status.ERROR
      toast.error('提交失败，请重试')
    }
  })
}
</script>
