<template>
  <div class="flex flex-col items-center justify-center py-16">
    <div class="mb-6">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-indigo-300" viewBox="0 0 20 20" fill="currentColor">
        <path
          fill-rule="evenodd"
          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
          clip-rule="evenodd"
        />
      </svg>
    </div>
    <p class="mb-2 text-lg font-medium text-gray-700">{{ title || '请求处理失败' }}</p>
    <p class="mb-6 text-sm text-gray-500">{{ message || '网络问题或服务器繁忙，请稍后再试' }}</p>

    <button
      v-if="showRetry"
      @click="$emit('retry')"
      class="flex items-center gap-2 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 px-6 py-2.5 text-sm font-medium text-white shadow-sm transition-all duration-300 hover:shadow-md"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
        />
      </svg>
      {{ retryText || '重新尝试' }}
    </button>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  title?: string
  message?: string
  retryText?: string
  showRetry?: boolean
}>()

defineEmits<{
  (e: 'retry'): void
}>()
</script>
