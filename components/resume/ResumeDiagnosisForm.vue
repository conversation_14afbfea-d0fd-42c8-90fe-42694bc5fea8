<template>
  <div>
    <div class="mb-6">
      <h2 class="mb-4 text-lg font-semibold text-gray-800">简历诊断</h2>
      
      <!-- 简历内容输入 -->
      <div class="mb-6">
        <label class="mb-2 block text-sm font-medium text-gray-700">简历内容</label>
        <div class="relative rounded-lg border border-gray-300 shadow-sm transition-all duration-300 focus-within:border-2 focus-within:border-dashed focus-within:border-purple-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-purple-200">
          <textarea
            v-model="formData.resumeText"
            class="block h-64 w-full resize-none rounded-t-lg border-0 p-4 text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-0"
            placeholder="请输入您的简历内容..."
            :disabled="isProcessing"
          ></textarea>
        </div>
      </div>

      <!-- 职位描述输入 -->
      <div class="mb-6">
        <label class="mb-2 block text-sm font-medium text-gray-700">目标职位描述</label>
        <textarea
          v-model="formData.jobDescription"
          class="block h-32 w-full resize-none rounded-lg border border-gray-300 p-4 text-sm text-gray-700 placeholder-gray-400 shadow-sm focus:border-purple-500 focus:ring-purple-500"
          placeholder="请输入目标职位的职位描述、任职要求等信息..."
          :disabled="isProcessing"
        ></textarea>
      </div>

      <!-- 模型选择 -->
      <div class="mb-6">
        <label class="mb-2 block text-sm font-medium text-gray-700">诊断模式</label>
        <div class="grid grid-cols-2 gap-4">
          <label class="flex items-center">
            <input
              type="radio"
              v-model="formData.model"
              value="standard"
              class="h-4 w-4 border-gray-300 text-purple-600 focus:ring-purple-500"
              :disabled="isProcessing"
            />
            <span class="ml-2 text-sm text-gray-700">标准模式</span>
          </label>
          <label class="flex items-center">
            <input
              type="radio"
              v-model="formData.model"
              value="professional"
              class="h-4 w-4 border-gray-300 text-purple-600 focus:ring-purple-500"
              :disabled="isProcessing"
            />
            <span class="ml-2 text-sm text-gray-700">专业模式</span>
          </label>
        </div>
      </div>

      <!-- 开始诊断按钮 -->
      <button
        @click="handleSubmit"
        :disabled="!isFormValid || isProcessing"
        class="relative mb-4 flex w-full items-center justify-center overflow-hidden rounded-xl bg-gradient-to-r from-purple-600 to-pink-500 px-4 py-3 text-white shadow-md shadow-purple-500/20 transition-all duration-300 hover:translate-y-[-2px] hover:from-purple-700 hover:to-pink-600 hover:shadow-lg hover:shadow-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
      >
        <div v-if="isProcessing" class="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
        <span>{{ isProcessing ? '正在诊断中...' : '开始诊断' }}</span>
      </button>

      <!-- 生成优化简历按钮 -->
      <button
        v-if="stageOneCompleted"
        @click="handleGenerateResume"
        :disabled="!isFormValid || isProcessing"
        class="relative flex w-full items-center justify-center overflow-hidden rounded-xl bg-gradient-to-r from-green-600 to-teal-500 px-4 py-3 text-white shadow-md shadow-green-500/20 transition-all duration-300 hover:translate-y-[-2px] hover:from-green-700 hover:to-teal-600 hover:shadow-lg hover:shadow-green-500/30 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
      >
        <div v-if="isProcessing" class="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
        <svg v-else class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
        <span>{{ isProcessing ? '正在生成中...' : '生成优化简历' }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  isProcessing: boolean
  stageOneCompleted: boolean
}

interface FormData {
  resumeText: string
  jobDescription: string
  model: 'standard' | 'professional'
}

const props = defineProps<Props>()

const emit = defineEmits<{
  submit: [formData: FormData]
  generateResume: [formData: FormData]
}>()

// 表单数据
const formData = ref<FormData>({
  resumeText: '',
  jobDescription: '',
  model: 'standard'
})

// 表单验证
const isFormValid = computed(() => {
  return formData.value.resumeText.trim().length > 0 && 
         formData.value.jobDescription.trim().length > 0
})

// 处理提交
const handleSubmit = () => {
  if (isFormValid.value) {
    emit('submit', { ...formData.value })
  }
}

// 处理生成简历
const handleGenerateResume = () => {
  if (isFormValid.value) {
    emit('generateResume', { ...formData.value })
  }
}
</script> 