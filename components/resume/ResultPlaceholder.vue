<template>
  <div
    class="flex h-[400px] flex-col items-center justify-center rounded-xl border-2 border-dashed border-indigo-100/80 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 p-6 text-center"
  >
    <div class="mb-6">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 text-purple-200" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
        <path d="M12 5L10 10L5 12L10 14L12 19L14 14L19 12L14 10L12 5Z" />
      </svg>
    </div>
    <p class="mb-2 text-lg font-medium text-gray-700">{{ title }}</p>
    <p class="text-sm text-gray-500">{{ description }}</p>
  </div>
</template>

<script setup lang="ts">
withDefaults(
  defineProps<{
    title?: string
    description?: string
  }>(),
  {
    title: '优化结果将显示在这里',
    description: '完成左侧表单并点击"开始优化"按钮',
  },
)
</script>
