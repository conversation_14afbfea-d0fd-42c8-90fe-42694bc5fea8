<template>
  <div class="h-full">
    <!-- 错误状态 -->
    <div v-if="errorMessage" class="flex flex-col items-center justify-center py-12">
      <div class="mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-red-100 to-pink-100 shadow-lg">
        <svg class="h-10 w-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <h3 class="mb-3 text-xl font-semibold text-gray-800">处理失败</h3>
      <p class="mb-6 max-w-md text-center text-sm text-gray-600">{{ errorMessage }}</p>
      <button
        @click="$emit('retry')"
        class="rounded-lg bg-gradient-to-r from-red-500 to-pink-500 px-6 py-2 text-sm font-medium text-white shadow-md transition-all hover:shadow-lg"
      >
        重新尝试
      </button>
    </div>

    <!-- 初始状态 -->
    <div v-else-if="currentStage === 'idle'" class="flex flex-col items-center justify-center py-16">
      <div class="mb-6 flex h-24 w-24 items-center justify-center rounded-full bg-gradient-to-br from-purple-100 to-blue-100 shadow-lg">
        <svg class="h-12 w-12 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      </div>
      <h3 class="mb-3 text-xl font-semibold text-gray-800">准备开始诊断</h3>
      <p class="mb-6 max-w-md text-center text-gray-600">请在左侧输入您的简历内容和目标职位描述，我们将为您提供专业的分析和优化建议</p>

      <!-- 功能特点 -->
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
        <div class="flex flex-col items-center text-center">
          <div class="mb-2 flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100">
            <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
              />
            </svg>
          </div>
          <div class="text-xs font-medium text-gray-700">智能分析</div>
        </div>
        <div class="flex flex-col items-center text-center">
          <div class="mb-2 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
            <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
          </div>
          <div class="text-xs font-medium text-gray-700">专业建议</div>
        </div>
        <div class="flex flex-col items-center text-center">
          <div class="mb-2 flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
            <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div class="text-xs font-medium text-gray-700">一键优化</div>
        </div>
      </div>
    </div>

    <!-- 处理中状态 -->
    <div v-else-if="isProcessing" class="flex flex-col items-center justify-center py-16">
      <!-- 动画加载器 -->
      <div class="relative mb-8">
        <div class="h-20 w-20 animate-spin rounded-full border-4 border-purple-200 border-t-purple-600"></div>
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="h-12 w-12 rounded-full bg-gradient-to-br from-purple-100 to-blue-100"></div>
        </div>
      </div>

      <!-- 动态状态消息 -->
      <div v-if="currentStatusMessage" class="mb-6 text-center transition-all duration-500 ease-in-out">
        <h3 class="mb-2 text-xl font-semibold text-gray-800">
          {{ currentStatusMessage.title }}
        </h3>
        <p class="max-w-md text-sm text-gray-600">
          {{ currentStatusMessage.subtitle }}
        </p>
      </div>

      <!-- 默认状态消息 -->
      <div v-else class="mb-6 text-center">
        <h3 class="mb-2 text-xl font-semibold text-gray-800">
          {{ currentStage === 'stage-one' ? '正在智能诊断分析...' : '正在生成优化简历...' }}
        </h3>
        <p class="max-w-md text-sm text-gray-600">
          {{ currentStage === 'stage-one' ? '请稍等，AI正在深度分析您的简历与职位匹配度' : '请稍等，AI正在根据诊断结果生成优化后的简历' }}
        </p>
      </div>

      <!-- 进度指示点 -->
      <div class="flex space-x-2">
        <div class="h-3 w-3 animate-bounce rounded-full bg-purple-600" style="animation-delay: 0ms"></div>
        <div class="h-3 w-3 animate-bounce rounded-full bg-purple-600" style="animation-delay: 150ms"></div>
        <div class="h-3 w-3 animate-bounce rounded-full bg-purple-600" style="animation-delay: 300ms"></div>
      </div>
    </div>

    <!-- 阶段一结果：诊断报告 -->
    <div v-else-if="diagnosisResult.analyzer_sop || diagnosisResult.diagnosis_sop || diagnosisResult.suggestions_sop" class="space-y-6">
      <!-- 成功状态指示 -->
      <div class="mb-6 flex items-center justify-center">
        <div class="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-green-100 to-emerald-100 shadow-lg">
          <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
      </div>

      <div class="text-center">
        <h3 class="mb-2 text-xl font-semibold text-gray-800">诊断分析完成</h3>
        <p class="mb-6 text-sm text-gray-600">AI已完成对您简历的深度分析，点击查看详细报告</p>
      </div>

      <!-- 快速摘要 -->
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
        <div class="rounded-xl border border-purple-200 bg-purple-50 p-4 text-center">
          <div class="mb-2 text-2xl font-bold text-purple-600">85%</div>
          <div class="text-xs text-purple-700">匹配度</div>
        </div>
        <div class="rounded-xl border border-blue-200 bg-blue-50 p-4 text-center">
          <div class="mb-2 text-2xl font-bold text-blue-600">12</div>
          <div class="text-xs text-blue-700">优化建议</div>
        </div>
        <div class="rounded-xl border border-green-200 bg-green-50 p-4 text-center">
          <div class="mb-2 text-2xl font-bold text-green-600">A+</div>
          <div class="text-xs text-green-700">综合评级</div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex flex-col gap-3 sm:flex-row">
        <button
          @click="openDiagnosisDialog"
          class="flex flex-1 items-center justify-center rounded-xl bg-gradient-to-r from-purple-600 to-blue-600 px-4 py-3 text-white shadow-lg transition-all hover:scale-[1.02] hover:shadow-xl"
        >
          <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          查看详细报告
        </button>
      </div>

      <!-- 技能匹配分析 -->
      <div v-if="diagnosisResult.analyzer_sop" class="rounded-lg border border-gray-200 bg-gray-50 p-4">
        <h4 class="mb-3 flex items-center text-base font-medium text-gray-800">
          <svg class="mr-2 h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
            />
          </svg>
          技能匹配分析
        </h4>

        <div v-if="diagnosisResult.analyzer_sop.skill_matching" class="mb-4">
          <h5 class="mb-2 text-sm font-medium text-gray-700">技能匹配情况</h5>
          <div class="space-y-2">
            <div v-for="(skill, index) in diagnosisResult.analyzer_sop.skill_matching" :key="index" class="text-sm text-gray-600">
              {{ skill }}
            </div>
          </div>
        </div>

        <div v-if="diagnosisResult.analyzer_sop.strength_skills" class="mb-4">
          <h5 class="mb-2 text-sm font-medium text-gray-700">优势技能</h5>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="(skill, index) in diagnosisResult.analyzer_sop.strength_skills"
              :key="index"
              class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800"
            >
              {{ skill }}
            </span>
          </div>
        </div>

        <div v-if="diagnosisResult.analyzer_sop.matching_experiences" class="mb-4">
          <h5 class="mb-2 text-sm font-medium text-gray-700">匹配经验</h5>
          <div class="space-y-2">
            <div v-for="(exp, index) in diagnosisResult.analyzer_sop.matching_experiences" :key="index" class="text-sm text-gray-600">
              {{ exp }}
            </div>
          </div>
        </div>

        <!-- 处理原始响应情况 -->
        <div v-if="diagnosisResult.analyzer_sop.raw_response" class="whitespace-pre-wrap text-sm text-gray-600">
          {{ diagnosisResult.analyzer_sop.raw_response }}
        </div>
      </div>

      <!-- 问题诊断 -->
      <div v-if="diagnosisResult.diagnosis_sop" class="rounded-lg border border-gray-200 bg-gray-50 p-4">
        <h4 class="mb-3 flex items-center text-base font-medium text-gray-800">
          <svg class="mr-2 h-5 w-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.728-.833-2.498 0L4.316 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          问题诊断
        </h4>

        <div v-if="diagnosisResult.diagnosis_sop.improvement_points" class="space-y-2">
          <div v-for="(point, index) in diagnosisResult.diagnosis_sop.improvement_points" :key="index" class="text-sm text-gray-600">
            {{ point }}
          </div>
        </div>

        <div v-if="diagnosisResult.diagnosis_sop.raw_response" class="whitespace-pre-wrap text-sm text-gray-600">
          {{ diagnosisResult.diagnosis_sop.raw_response }}
        </div>
      </div>

      <!-- 优化建议 -->
      <div v-if="diagnosisResult.suggestions_sop" class="rounded-lg border border-gray-200 bg-gray-50 p-4">
        <h4 class="mb-3 flex items-center text-base font-medium text-gray-800">
          <svg class="mr-2 h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
            />
          </svg>
          优化建议
        </h4>

        <div v-if="diagnosisResult.suggestions_sop.suggestions" class="space-y-3">
          <div v-for="(suggestion, index) in diagnosisResult.suggestions_sop.suggestions" :key="index" class="border-l-4 border-purple-400 bg-purple-50 p-3">
            <h5 class="font-medium text-purple-800">{{ suggestion.title }}</h5>
            <p class="mt-1 text-sm text-purple-700">{{ suggestion.content }}</p>
          </div>
        </div>

        <div v-if="diagnosisResult.suggestions_sop.raw_response" class="whitespace-pre-wrap text-sm text-gray-600">
          {{ diagnosisResult.suggestions_sop.raw_response }}
        </div>
      </div>
    </div>

    <!-- 阶段二结果：优化后的简历 -->
    <div v-else-if="optimizedResume || (currentStage === 'stage-two' && isProcessing)" class="space-y-6">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800">
          {{ isProcessing && !optimizedResume ? '正在生成优化简历...' : '优化后的简历' }}
        </h3>
        <div class="flex space-x-2">
          <button
            v-if="optimizedResume && !isProcessing"
            @click="copyToClipboard"
            class="flex items-center rounded-lg bg-purple-600 px-3 py-1.5 text-sm font-medium text-white transition-colors hover:bg-purple-700"
          >
            <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
              />
            </svg>
            复制
          </button>
        </div>
      </div>

      <!-- 流式输出加载状态 -->
      <div v-if="isProcessing && !optimizedResume" class="flex flex-col items-center justify-center py-12">
        <div class="mb-4 h-16 w-16 animate-spin rounded-full border-4 border-purple-200 border-t-purple-600"></div>
        <h3 class="mb-2 text-lg font-semibold text-gray-800">正在生成优化简历...</h3>
        <p class="text-sm text-gray-600">AI 正在为您优化简历内容</p>
      </div>

      <!-- 简历内容展示 -->
      <div v-else class="rounded-lg border border-gray-200 bg-gray-50 p-4">
        <div class="max-h-96 overflow-y-auto">
          <!-- 流式输出状态指示 -->
          <div v-if="isStreaming" class="mb-2 flex items-center text-xs text-purple-600">
            <div class="mr-2 h-2 w-2 animate-pulse rounded-full bg-purple-600"></div>
            正在实时生成中...
          </div>
          <div class="whitespace-pre-wrap text-sm leading-relaxed text-gray-800">{{ optimizedResume }}</div>
          <!-- 光标效果 -->
          <span v-if="isStreaming" class="animate-pulse text-purple-600">|</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useToast } from 'vue-toast-notification'

interface Props {
  currentStage: 'idle' | 'stage-one' | 'stage-two'
  isProcessing: boolean
  diagnosisResult: {
    analyzer_sop: any
    diagnosis_sop: any
    suggestions_sop: any
  }
  optimizedResume: string
  errorMessage: string
  isStreaming?: boolean
  currentStatusMessage?: {
    title: string
    subtitle: string
  }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  openDiagnosisDialog: []
  retry: []
}>()

const toast = useToast()

const openDiagnosisDialog = () => {
  emit('openDiagnosisDialog')
}

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(props.optimizedResume)
    toast.success('已复制到剪贴板')
  } catch (err) {
    toast.error('复制失败')
  }
}
</script>
