<template>
  <div :class="['mx-auto w-full max-w-4xl', className]">
    <div class="flex items-center justify-between rounded-lg bg-gradient-to-r from-purple-200/30 to-purple-200/50 px-6 py-3 shadow-md">
      <div v-for="(step, index) in steps" :key="step.id" class="flex items-center">
        <!-- Step Name -->
        <span :class="['text-sm font-medium', getStepTextColor(step.id, currentStep)]">
          {{ step.name }}
        </span>
        <!-- Chevron separator (except after last step) -->
        <span v-if="index < steps.length - 1" :class="['mx-2', getStepIconColor(step.id, currentStep)]">
          <ArrowRightIcon class="h-5 w-5" />
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PrepareInterviewStep } from '~/stores/prepare-interview'
import ArrowRightIcon from '~/assets/icons/arrow-right.svg'

interface Step {
  id: number
  name: string
}

defineProps<{
  currentStep: PrepareInterviewStep
  className?: string
}>()

const steps = [
  {
    id: PrepareInterviewStep.POSITION_INFO,
    name: '岗位信息',
  },
  {
    id: PrepareInterviewStep.RESUME_SELECTION,
    name: '简历选择',
  },
  {
    id: PrepareInterviewStep.PREPARATION_COMPLETE,
    name: '准备完成',
  },
]

const getStepStatus = (stepId: number, currentStep: PrepareInterviewStep): 'completed' | 'current' | 'upcoming' => {
  if (stepId < currentStep) {
    return 'completed'
  }
  if (stepId === currentStep) {
    return 'current'
  }
  return 'upcoming'
}

const getStepTextColor = (stepId: number, currentStep: PrepareInterviewStep): string => {
  const status = getStepStatus(stepId, currentStep)
  return status === 'completed' || status === 'current' ? 'text-purple-600' : 'text-gray-500'
}

const getStepIconColor = (stepId: number, currentStep: PrepareInterviewStep): string => {
  const status = getStepStatus(stepId, currentStep)
  return status === 'completed' || status === 'current' ? 'text-purple-400' : 'text-gray-500'
}
</script>
