<template>
  <div class="min-h-screen bg-gradient-to-b from-gray-50 to-white">
    <div class="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
      <!-- 头部区域 -->
      <div class="text-center">
        <div class="relative">
          <h1 class="relative z-10 bg-gradient-to-r from-purple-600 via-primary to-blue-600 bg-clip-text text-5xl font-bold text-transparent sm:text-6xl">
            关于 Hi-Offer
          </h1>
          <div class="absolute -top-8 left-1/2 -z-10 h-24 w-24 -translate-x-1/2 rounded-full bg-purple-100/80 blur-2xl"></div>
          <div class="absolute -top-6 left-1/2 -z-10 h-24 w-24 -translate-x-1/2 rounded-full bg-blue-100/50 blur-2xl"></div>
        </div>
        <p class="mx-auto mt-6 max-w-2xl text-lg font-medium leading-relaxed text-gray-600">
          打造
          <span class="relative inline-block">
            <span class="relative z-10 text-primary">智能化</span>
            <span class="absolute bottom-0 left-0 z-0 h-3 w-full bg-primary/10"></span>
          </span>
          的面试训练平台，让面试更
          <span class="relative inline-block">
            <span class="relative z-10 text-primary">高效</span>
            <span class="absolute bottom-0 left-0 z-0 h-3 w-full bg-primary/10"></span>
          </span>
          、更
          <span class="relative inline-block">
            <span class="relative z-10 text-primary">公平</span>
            <span class="absolute bottom-0 left-0 z-0 h-3 w-full bg-primary/10"></span>
          </span>
        </p>
      </div>

      <!-- 主要内容区 -->
      <div class="mt-16 space-y-16">
        <!-- 技术栈部分 -->
        <section>
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900">核心技术栈</h2>
            <p class="mt-2 text-gray-600">我们采用现代化的技术栈,打造流畅的用户体验</p>
          </div>

          <div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            <div
              class="group relative overflow-hidden rounded-2xl bg-white p-6 shadow-sm ring-1 ring-gray-100 transition-all hover:-translate-y-1 hover:shadow-xl"
            >
              <div class="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-indigo-50 text-indigo-600">
                <svg t="1738150031471" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1479" width="32" height="32">
                  <path
                    d="M94.208 94.208v835.584h835.584V94.208H94.208z m634.92096 405.85216v0.012288c8.011776 0.024576 17.119232 0.436224 23.967744 1.179648 27.891712 3.016704 49.6128 15.050752 68.091904 37.715968 9.201664 11.290624 12.34944 16.2304 11.679744 18.343936-0.432128 1.363968-6.746112 5.885952-26.820608 19.21024-19.720192 13.092864-26.07104 17.014784-27.5456 17.014784-1.497088 0-4.614144-3.207168-9.105408-9.365504-8.6528-11.855872-17.485824-17.266688-31.13984-19.070976-14.68416-1.9456-27.856896 2.68288-34.308096 12.058624-5.515264 8.011776-6.3488 20.901888-1.96608 30.26944 5.07904 10.848256 14.270464 16.846848 49.494016 32.290816 40.624128 17.813504 61.210624 30.005248 76.204032 45.13792 16.146432 16.293888 24.326144 35.106816 26.83904 61.718528 1.226752 12.972032-0.272384 28.34432-3.98336 40.843264-9.10336 30.640128-33.66912 53.075968-69.67296 63.635456-9.95328 2.9184-19.214336 4.661248-28.37504 5.332992-13.985792 1.030144-34.002944 0.462848-46.051328-1.29024-30.482432-4.442112-64.892928-22.17984-82.051072-42.2912-8.423424-9.873408-19.177472-26.12224-19.177472-28.9792 0-1.380352 0.684032-2.164736 3.391488-3.885056 8.032256-5.103616 54.054912-31.412224 54.94784-31.412224 0.540672 0 2.945024 2.832384 5.341184 6.295552 5.429248 7.839744 18.78016 21.313536 25.567232 25.808896 5.543936 3.672064 12.634112 6.619136 21.051392 8.747008 4.820992 1.202176 7.3728 1.417216 17.891328 1.417216 10.747904-0.004096 12.951552-0.18432 17.760256-1.476608 12.71808-3.422208 22.644736-10.50624 26.851328-19.156992 1.8432-3.7376 1.880064-4.204544 1.880064-13.27104v-9.40032l-2.260992-4.48512c-5.474304-10.866688-17.270784-18.323456-54.56896-34.47808-17.13152-7.421952-38.11328-17.885184-46.30528-23.0912-18.696192-11.880448-31.653888-25.462784-40.157184-42.088448-8.45824-16.533504-9.71776-22.687744-9.73824-47.548416-0.02048-19.462144-0.053248-19.222528 3.975168-31.643648 3.65568-11.272192 11.139072-23.863296 19.400704-32.64512 16.4864-17.524736 40.577024-28.788736 66.367488-31.029248 3.29728-0.313344 7.716864-0.434176 12.52352-0.41984z m-221.92128 3.844096h0.008192c49.670144 0.024576 78.143488 0.196608 78.600192 0.483328 0.86016 0.53248 0.968704 4.855808 0.968704 32.444416v31.827968l-49.563648 0.180224-49.563648 0.180224v140.724224c0 77.400064-0.157696 141.185024-0.372736 141.748224-0.350208 0.948224-4.163584 1.019904-36.41344 1.019904h-36.018176l-0.372736-1.45408c-0.239616-0.79872-0.415744-64.587776-0.41984-141.750272l-0.012288-140.296192-49.5616-0.176128-49.565696-0.180224v-31.451136c0-24.94464 0.172032-31.625216 0.837632-32.288768 0.681984-0.702464 25.976832-0.882688 134.967296-0.991232 21.01248-0.02048 39.92576-0.03072 56.48384-0.02048z"
                    fill="#0288D1"
                    p-id="1480"
                  ></path>
                </svg>
              </div>
              <h3 class="mb-2 text-lg font-semibold text-gray-900">Vue 3 + TypeScript</h3>
              <p class="text-gray-600">采用 Vue 3 Composition API 和 TypeScript 开发,提供类型安全和更好的开发体验</p>
            </div>

            <div
              class="group relative overflow-hidden rounded-2xl bg-white p-6 shadow-sm ring-1 ring-gray-100 transition-all hover:-translate-y-1 hover:shadow-xl"
            >
              <div class="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-emerald-50 text-emerald-600">
                <svg t="1738150063658" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3058" width="32" height="32">
                  <path
                    d="M565.512258 809.290323h329.992258c10.570323 0 20.645161-3.716129 29.729032-8.91871a62.513548 62.513548 0 0 0 23.783226-20.810323 59.953548 59.953548 0 0 0 8.91871-29.729032c0-10.405161-3.633548-20.645161-8.91871-29.729032l-222.967742-383.504516a54.255484 54.255484 0 0 0-20.810322-20.810323 71.845161 71.845161 0 0 0-32.701936-8.91871c-10.487742 0-20.645161 3.716129-29.729032 8.91871a54.255484 54.255484 0 0 0-20.810323 20.810323l-56.485161 98.105806L452.541935 244.43871c-5.285161-9.083871-11.726452-18.580645-20.810322-23.783226-9.083871-5.202581-19.15871-5.945806-29.729032-5.945807s-20.645161 0.743226-29.729033 5.945807a70.523871 70.523871 0 0 0-23.783225 23.783226L72.010323 720.103226A57.310968 57.310968 0 0 0 66.064516 749.832258c0 10.405161 0.660645 20.645161 5.945807 29.729032a62.513548 62.513548 0 0 0 23.783225 20.810323A60.449032 60.449032 0 0 0 125.522581 809.290323h208.103225c82.498065 0 142.451613-36.996129 184.32-107.024517L619.024516 526.864516l53.512258-92.16 163.509678 279.452903H619.024516zM330.652903 714.157419H184.980645l217.021936-374.585806L512 526.864516l-72.836129 127.009032c-27.829677 45.419355-59.458065 60.283871-108.510968 60.283871z"
                    fill="#00E676"
                    p-id="3059"
                  ></path>
                </svg>
              </div>
              <h3 class="mb-2 text-lg font-semibold text-gray-900">Nuxt 3</h3>
              <p class="text-gray-600">基于 Nuxt 3 构建,支持 SSR、自动路由、API 接口等现代化特性</p>
            </div>

            <div
              class="group relative overflow-hidden rounded-2xl bg-white p-6 shadow-sm ring-1 ring-gray-100 transition-all hover:-translate-y-1 hover:shadow-xl"
            >
              <div class="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-sky-50 text-sky-600">
                <svg t="1738150126524" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4032" width="32" height="32">
                  <path
                    d="M480 256c-110.912 0-180.288 49.792-208 149.312 41.6-49.728 90.112-68.48 145.6-56 31.616 7.104 54.272 27.712 79.36 50.56C537.6 436.992 584.832 480 688 480c110.912 0 180.288-49.792 208-149.312-41.6 49.728-90.112 68.48-145.6 56-31.68-7.104-54.272-27.712-79.36-50.56C630.4 299.008 583.168 256 480 256zM272 480c-110.912 0-180.288 49.792-208 149.312 41.6-49.728 90.112-68.48 145.6-56 31.616 7.104 54.272 27.712 79.36 50.56C329.6 660.992 376.832 704 480 704c110.912 0 180.288-49.792 208-149.312-41.6 49.728-90.112 68.48-145.6 56-31.616-7.104-54.272-27.712-79.36-50.56C422.4 523.008 375.168 480 272 480z"
                    fill="#89DDFF"
                    p-id="4033"
                  ></path>
                </svg>
              </div>
              <h3 class="mb-2 text-lg font-semibold text-gray-900">Tailwind CSS</h3>
              <p class="text-gray-600">使用 Tailwind CSS 构建现代化 UI,实现响应式设计</p>
            </div>
          </div>
        </section>

        <!-- 核心功能部分 -->
        <section>
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900">核心功能</h2>
            <p class="mt-2 text-gray-600">为用户提供全方位的面试训练体验</p>
          </div>

          <div class="grid gap-8 sm:grid-cols-2">
            <div
              class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-violet-500 via-purple-500 to-indigo-600 p-8 text-white transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl"
            >
              <div class="relative z-10">
                <div class="mb-6 inline-flex rounded-lg bg-white/10 p-3">
                  <svg class="h-6 w-6 text-white" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <h3 class="mb-4 text-xl font-semibold">
                  <span
                    class="inline-block bg-gradient-to-r from-white via-white/90 to-white/80 bg-clip-text text-transparent transition-transform duration-300 group-hover:translate-x-1"
                    >AI 智能面试</span
                  >
                </h3>
                <ul class="space-y-3">
                  <li class="flex items-center gap-2 opacity-90 transition-all duration-300 hover:translate-x-1 hover:opacity-100">
                    <div class="flex h-6 w-6 items-center justify-center rounded-full bg-white/20">
                      <svg class="h-4 w-4" viewBox="0 0 24 24" fill="none">
                        <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </div>
                    <span class="text-white/90">真实场景模拟</span>
                  </li>
                  <li class="flex items-center gap-2 opacity-90 transition-all duration-300 hover:translate-x-1 hover:opacity-100">
                    <div class="flex h-6 w-6 items-center justify-center rounded-full bg-white/20">
                      <svg class="h-4 w-4" viewBox="0 0 24 24" fill="none">
                        <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </div>
                    <span class="text-white/90">智能问题生成</span>
                  </li>
                  <li class="flex items-center gap-2 opacity-90 transition-all duration-300 hover:translate-x-1 hover:opacity-100">
                    <div class="flex h-6 w-6 items-center justify-center rounded-full bg-white/20">
                      <svg class="h-4 w-4" viewBox="0 0 24 24" fill="none">
                        <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </div>
                    <span class="text-white/90">实时语音交互</span>
                  </li>
                </ul>
              </div>
              <div class="absolute right-0 top-0 h-full w-1/2 bg-gradient-to-l from-white/10 to-transparent" />
              <div class="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-purple-400/30 blur-2xl transition-all duration-300 group-hover:scale-150" />
              <div class="absolute -right-4 -top-4 h-32 w-32 rounded-full bg-indigo-400/30 blur-2xl transition-all duration-300 group-hover:scale-150" />
            </div>

            <div
              class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-teal-400 via-emerald-500 to-green-500 p-8 text-white transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl"
            >
              <div class="relative z-10">
                <div class="mb-6 inline-flex rounded-lg bg-white/10 p-3">
                  <svg class="h-6 w-6 text-white" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M9 11.75c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zm6 0c-.69 0-1.25.56-1.25 1.25s.56 1.25 1.25 1.25 1.25-.56 1.25-1.25-.56-1.25-1.25-1.25zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-.29.02-.58.05-.86 2.36-1.05 4.23-2.98 5.21-5.37C11.07 8.33 14.05 10 17.42 10c.78 0 1.53-.09 2.25-.*********** 1.47.33 2.26 0 4.41-3.59 8-8 8z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <h3 class="mb-4 text-xl font-semibold">
                  <span
                    class="inline-block bg-gradient-to-r from-white via-white/90 to-white/80 bg-clip-text text-transparent transition-transform duration-300 group-hover:translate-x-1"
                    >专业评估反馈</span
                  >
                </h3>
                <ul class="space-y-3">
                  <li class="flex items-center gap-2 opacity-90 transition-all duration-300 hover:translate-x-1 hover:opacity-100">
                    <div class="flex h-6 w-6 items-center justify-center rounded-full bg-white/20">
                      <svg class="h-4 w-4" viewBox="0 0 24 24" fill="none">
                        <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </div>
                    <span class="text-white/90">多维度能力评估</span>
                  </li>
                  <li class="flex items-center gap-2 opacity-90 transition-all duration-300 hover:translate-x-1 hover:opacity-100">
                    <div class="flex h-6 w-6 items-center justify-center rounded-full bg-white/20">
                      <svg class="h-4 w-4" viewBox="0 0 24 24" fill="none">
                        <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </div>
                    <span class="text-white/90">详细改进建议</span>
                  </li>
                  <li class="flex items-center gap-2 opacity-90 transition-all duration-300 hover:translate-x-1 hover:opacity-100">
                    <div class="flex h-6 w-6 items-center justify-center rounded-full bg-white/20">
                      <svg class="h-4 w-4" viewBox="0 0 24 24" fill="none">
                        <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </div>
                    <span class="text-white/90">个性化学习计划</span>
                  </li>
                </ul>
              </div>
              <div class="absolute right-0 top-0 h-full w-1/2 bg-gradient-to-l from-white/10 to-transparent" />
              <div class="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-emerald-400/30 blur-2xl transition-all duration-300 group-hover:scale-150" />
              <div class="absolute -right-4 -top-4 h-32 w-32 rounded-full bg-teal-400/30 blur-2xl transition-all duration-300 group-hover:scale-150" />
            </div>
          </div>
        </section>

        <!-- 团队介绍部分 -->
        <section>
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900">我们的团队</h2>
            <p class="mt-2 text-gray-600">由经验丰富的工程师和产品专家组成</p>
          </div>

          <div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            <div
              class="group relative overflow-hidden rounded-2xl bg-white p-8 text-center shadow-md ring-1 ring-gray-200 transition-all hover:-translate-y-2 hover:shadow-xl"
            >
              <div class="absolute -right-4 -top-4 h-32 w-32 rounded-full bg-indigo-100/50 blur-xl"></div>
              <div class="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-emerald-100/50 blur-xl"></div>
              <div class="relative z-10">
                <div class="mx-auto mb-6 h-28 w-28 overflow-hidden rounded-full border-4 border-white shadow-lg">
                  <img src="https://image.hi-offer.net/i/2025/01/29/vtljo0.jpg" alt="团队成员" class="h-full w-full object-cover" />
                </div>
                <h3 class="mb-2 text-xl font-semibold text-gray-900">Jan</h3>
                <p class="mb-4 inline-block rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 px-4 py-1 text-sm font-medium text-white">
                  Code Farmer
                </p>
                <p class="text-gray-600">全干工程师，没有多年开发经验</p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>
