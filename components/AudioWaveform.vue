<template>
  <canvas ref="canvasRef" class="absolute inset-0 h-full w-full"></canvas>
</template>

<script setup lang="ts">
import { ref, onUnmounted, watch } from 'vue'

const props = defineProps<{
  isActive: boolean
  audioData?: Float32Array
}>()

const canvasRef = ref<HTMLCanvasElement | null>(null)
let animationFrameId: number | null = null

const BAR_COUNT = 32 // 柱状条的数量
const MIN_BAR_HEIGHT = 4 // 最小高度
const BAR_GAP = 2 // 柱状条之间的间隔
const SMOOTHING = 0.7 // 平滑系数
const AMPLITUDE_MULTIPLIER = 3 // 振幅倍数

// 音量阈值
const VOLUME_THRESHOLDS = {
  LOW: 0.2, // 音量过低阈值
  HIGH: 0.6, // 音量过高阈值
}

// 颜色配置
const COLORS = {
  LOW: {
    TOP: 'rgba(160, 160, 160, 0.95)',
    MIDDLE: 'rgba(160, 160, 160, 0.5)',
    BOTTOM: 'rgba(160, 160, 160, 0.2)',
  },
  NORMAL: {
    TOP: 'rgba(255, 255, 255, 0.95)',
    MIDDLE: 'rgba(255, 255, 255, 0.5)',
    BOTTOM: 'rgba(255, 255, 255, 0.2)',
  },
  HIGH: {
    TOP: 'rgba(255, 59, 48, 0.95)',
    MIDDLE: 'rgba(255, 59, 48, 0.5)',
    BOTTOM: 'rgba(255, 59, 48, 0.2)',
  },
}

const previousBars = ref<number[]>(Array(BAR_COUNT).fill(MIN_BAR_HEIGHT))

const getVolumeLevel = (amplitude: number) => {
  if (amplitude < VOLUME_THRESHOLDS.LOW) return 'LOW'
  if (amplitude > VOLUME_THRESHOLDS.HIGH) return 'HIGH'
  return 'NORMAL'
}

const drawSpectrum = () => {
  const canvas = canvasRef.value
  if (!canvas || !props.audioData) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 设置 canvas 尺寸以匹配显示尺寸
  const dpr = window.devicePixelRatio || 1
  const rect = canvas.getBoundingClientRect()
  canvas.width = rect.width * dpr
  canvas.height = rect.height * dpr
  ctx.scale(dpr, dpr)

  // 清空画布
  ctx.clearRect(0, 0, rect.width, rect.height)

  // 计算每个柱状条的宽度
  const barWidth = (rect.width - (BAR_COUNT - 1) * BAR_GAP) / BAR_COUNT

  // 计算总体音量水平
  const totalRms = Math.sqrt(props.audioData.reduce((sum, value) => sum + value * value, 0) / props.audioData.length)
  const volumeLevel = getVolumeLevel(totalRms * AMPLITUDE_MULTIPLIER)
  const colors = COLORS[volumeLevel]

  // 将音频数据分段并计算每段的平均振幅
  const segmentLength = Math.floor(props.audioData.length / BAR_COUNT)
  const currentBars = Array(BAR_COUNT).fill(0)

  for (let i = 0; i < BAR_COUNT; i++) {
    const start = i * segmentLength
    const end = start + segmentLength
    const segment = props.audioData.slice(start, end)

    // 计算RMS值来获得更好的振幅表现
    const rms = Math.sqrt(segment.reduce((sum, value) => sum + value * value, 0) / segment.length)

    // 使用非线性缩放来增强小信号
    const amplitude = Math.pow(rms, 0.8) * AMPLITUDE_MULTIPLIER

    // 平滑过渡
    const targetHeight = Math.max(MIN_BAR_HEIGHT, amplitude * rect.height * 0.9)
    const previousHeight = previousBars.value[i]
    currentBars[i] = previousHeight + (targetHeight - previousHeight) * (1 - SMOOTHING)
  }

  previousBars.value = currentBars

  // 绘制柱状图
  currentBars.forEach((height, i) => {
    const x = i * (barWidth + BAR_GAP)
    const y = rect.height - height

    // 创建渐变
    const gradient = ctx.createLinearGradient(x, y, x, rect.height)
    gradient.addColorStop(0, colors.TOP)
    gradient.addColorStop(0.5, colors.MIDDLE)
    gradient.addColorStop(1, colors.BOTTOM)

    // 绘制主体
    ctx.fillStyle = gradient
    ctx.fillRect(x, y, barWidth, height)

    // 添加顶部高光效果
    ctx.fillStyle = colors.TOP
    ctx.fillRect(x, y, barWidth, 2)
  })
}

// 动画循环
const animate = () => {
  if (props.isActive) {
    drawSpectrum()
    animationFrameId = requestAnimationFrame(animate)
  }
}

// 监听活动状态变化
watch(
  () => props.isActive,
  newValue => {
    if (newValue) {
      animate()
    } else if (animationFrameId !== null) {
      cancelAnimationFrame(animationFrameId)
      animationFrameId = null

      // 重置柱状图到最小高度
      previousBars.value = Array(BAR_COUNT).fill(MIN_BAR_HEIGHT)

      // 清空画布
      const canvas = canvasRef.value
      if (canvas) {
        const ctx = canvas.getContext('2d')
        if (ctx) {
          ctx.clearRect(0, 0, canvas.width, canvas.height)
        }
      }
    }
  },
)

// 监听音频数据变化
watch(
  () => props.audioData,
  () => {
    if (props.isActive) {
      drawSpectrum()
    }
  },
)

onUnmounted(() => {
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId)
  }
})
</script>
