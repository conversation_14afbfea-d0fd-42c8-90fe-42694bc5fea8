<template>
  <header class="fixed left-0 top-0 z-50 w-full transition-all duration-300" :class="{ 'bg-white/90 shadow-sm backdrop-blur-md': isScrolled }">
    <div class="mx-auto flex max-w-7xl items-center justify-between px-4 py-3 lg:px-8">
      <!-- Logo区域 -->
      <NuxtLink to="/" class="flex items-center">
        <Logo class="h-8 w-8" />
        <span class="ml-2 text-xl font-bold text-gray-900">Hi-Offer</span>
      </NuxtLink>

      <!-- 桌面端导航菜单 -->
      <nav class="hidden lg:block">
        <ul class="flex items-center space-x-8">
          <li v-for="item in navItems" :key="item.path">
            <NuxtLink
              :to="item.path"
              class="relative px-1 py-2 font-medium transition-colors"
              :class="route.path === item.path ? 'text-primary-600' : 'text-gray-700 hover:text-primary-600'"
            >
              {{ item.name }}
              <span v-if="route.path === item.path" class="absolute bottom-0 left-0 h-0.5 w-full bg-primary-600 transition-all"></span>
            </NuxtLink>
          </li>
          <li>
            <a
              href="https://hi-offer.net"
              target="_blank"
              rel="noopener noreferrer"
              class="px-1 py-2 font-medium text-gray-700 transition-colors hover:text-primary-600"
            >
              博客
            </a>
          </li>
        </ul>
      </nav>

      <!-- 桌面端登录注册按钮 -->
      <div class="hidden items-center space-x-4 lg:flex">
        <!-- 未登录状态 -->
        <template v-if="!session">
          <NuxtLink to="/login" class="font-medium text-gray-700 transition-colors hover:text-primary-600">登录</NuxtLink>
          <UiButton @click="$router.push('/register')" variant="primary" size="sm"> 注册 </UiButton>
        </template>
        <!-- 已登录状态 -->
        <template v-else>
          <UiButton @click="$router.push('/')" variant="primary" size="sm"> 回到主页 </UiButton>
        </template>
      </div>

      <!-- 移动端汉堡菜单按钮 -->
      <UiButton @click="isMenuOpen = !isMenuOpen" variant="ghost" size="sm" class="lg:hidden" aria-label="菜单">
        <svg v-if="!isMenuOpen" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </UiButton>
    </div>

    <!-- 移动端菜单 -->
    <Transition
      enter-active-class="transition duration-200 ease-out"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition duration-150 ease-in"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div v-if="isMenuOpen" class="fixed inset-0 z-40 bg-white/95 backdrop-blur-sm lg:hidden">
        <div class="flex h-full flex-col">
          <!-- 顶部导航栏 -->
          <div class="flex items-center justify-between border-b border-gray-100 px-4 py-3">
            <NuxtLink to="/" class="flex items-center" @click="isMenuOpen = false">
              <Logo class="h-8 w-8" />
              <span class="ml-2 text-xl font-bold text-gray-900">Hi-Offer</span>
            </NuxtLink>
            <UiButton @click="isMenuOpen = false" variant="ghost" size="sm" aria-label="关闭菜单">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </UiButton>
          </div>

          <!-- 导航菜单 -->
          <nav class="flex-1 overflow-y-auto px-4 py-6">
            <ul class="space-y-2">
              <li v-for="item in navItems" :key="item.path">
                <NuxtLink
                  :to="item.path"
                  class="block rounded-lg px-4 py-3 font-medium transition-colors"
                  :class="route.path === item.path ? 'bg-primary-50 text-primary-600' : 'text-gray-700 hover:bg-gray-50 hover:text-primary-600'"
                  @click="isMenuOpen = false"
                >
                  {{ item.name }}
                </NuxtLink>
              </li>
              <li>
                <a
                  href="https://hi-offer.net"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="block rounded-lg px-4 py-3 font-medium text-gray-700 transition-colors hover:bg-gray-50 hover:text-primary-600"
                  @click="isMenuOpen = false"
                >
                  博客
                </a>
              </li>
            </ul>
          </nav>

          <!-- 底部按钮 -->
          <div class="border-t border-gray-100 p-4">
            <!-- 未登录状态 -->
            <div v-if="!session" class="grid grid-cols-2 gap-4">
              <UiButton
                @click="
                  () => {
                    $router.push('/login')
                    isMenuOpen = false
                  }
                "
                variant="outline"
                size="sm"
                full-width
              >
                登录
              </UiButton>
              <UiButton
                @click="
                  () => {
                    $router.push('/register')
                    isMenuOpen = false
                  }
                "
                variant="primary"
                size="sm"
                full-width
              >
                注册
              </UiButton>
            </div>
            <!-- 已登录状态 -->
            <div v-else>
              <UiButton
                @click="
                  () => {
                    $router.push('/')
                    isMenuOpen = false
                  }
                "
                variant="primary"
                size="sm"
                full-width
              >
                回到主页
              </UiButton>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </header>
</template>

<script setup lang="ts">
import Logo from '@/assets/icons/logo.svg'

// 控制移动端菜单显示状态
const isMenuOpen = ref(false)

// 控制滚动状态
const isScrolled = ref(false)

// 用户认证状态
const { data: session } = useAuth()

// 导航菜单项
const navItems = [
  { name: '首页', path: '/' },
  { name: '关于我们', path: '/about' },
  { name: '价格', path: '/pricing' },
]

// 当前路由高亮逻辑
const route = useRoute()

// 监听路由变化，关闭移动端菜单
watch(
  () => route.path,
  () => {
    isMenuOpen.value = false
  },
)

// 监听滚动
onMounted(() => {
  const handleScroll = () => {
    isScrolled.value = window.scrollY > 20
    if (isMenuOpen.value && window.scrollY > 20) {
      isMenuOpen.value = false
    }
  }

  window.addEventListener('scroll', handleScroll)

  // 初始检查
  handleScroll()

  // 清理事件监听
  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
  })
})
</script>

<style scoped>
:deep(.router-link-active) {
  color: var(--primary-600, #4e47ff);
}
</style>
