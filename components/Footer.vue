<template>
  <footer class="z-100 relative border-t border-gray-100 bg-white py-12">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <!-- Logo和简介部分 -->
      <div class="mb-8 flex flex-col items-start">
        <div class="flex items-center">
          <Logo class="h-8 w-auto" />
          <span class="ml-3 text-xl font-semibold text-gray-900">Hi-Offer</span>
        </div>
        <p class="mt-3 max-w-md text-sm text-gray-500">让面试更高效、更公平，为企业和求职者提供智能化的面试体验。</p>
      </div>

      <!-- 导航链接部分 -->
      <div class="grid grid-cols-2 gap-8 sm:grid-cols-3 md:grid-cols-4">
        <!-- 产品栏目 -->
        <!-- <div>
          <h3 class="text-sm font-medium text-gray-900">产品</h3>
          <ul class="mt-4 space-y-3">
            <li>
              <NuxtLink to="/dashboard" class="text-sm text-gray-500 transition-colors hover:text-indigo-600">仪表盘</NuxtLink>
            </li>
            <li>
              <NuxtLink to="/interviews" class="text-sm text-gray-500 transition-colors hover:text-indigo-600">面试记录</NuxtLink>
            </li>
            <li>
              <NuxtLink to="/settings" class="text-sm text-gray-500 transition-colors hover:text-indigo-600">个人设置</NuxtLink>
            </li>
          </ul>
        </div> -->

        <!-- 资源栏目 -->
        <div>
          <h3 class="text-sm font-medium text-gray-900">资源</h3>
          <ul class="mt-4 space-y-3">
            <!-- <li>
              <a href="https://hi-offer.net" target="_blank" rel="noopener noreferrer" class="text-sm text-gray-500 transition-colors hover:text-indigo-600"
                >博客</a
              >
            </li>
            <li>
              <NuxtLink to="/about" class="text-sm text-gray-500 transition-colors hover:text-indigo-600">关于我</NuxtLink>
            </li> -->
                        <li>
              <ClientOnly>
                <Popover :offset="8" position="bottom">
                  <template #default="{ isShow }">
                    <button class="text-sm text-gray-500 transition-colors hover:text-indigo-600">
                      用户群
                    </button>
                  </template>
                  <template #content="{ close }">
                    <div class="w-64 p-4">
                      <div class="text-center">
                        <h4 class="mb-3 text-sm font-medium text-gray-900">加入Hi-Offer用户群</h4>
                        <div class="mb-3 flex justify-center">
                          <!-- 微信群二维码 -->
                          <div class="overflow-hidden rounded-lg border border-gray-200">
                            <img 
                              src="/images/wechat-group-qr.png" 
                              alt="Hi-Offer微信用户群二维码" 
                              class="h-32 w-32 object-cover"
                            />
                          </div>
                        </div>
                        <p class="text-xs text-gray-500">扫码加入用户交流群</p>
                        <p class="mt-1 text-xs text-gray-400">获取产品更新和使用技巧</p>
                      </div>
                    </div>
                  </template>
                </Popover>
                <template #fallback>
                  <button class="text-sm text-gray-500 transition-colors hover:text-indigo-600">
                    用户群
                  </button>
                </template>
              </ClientOnly>
            </li>
          </ul>
        </div>

        <!-- 法律栏目 -->
        <div>
          <h3 class="text-sm font-medium text-gray-900">隐私 & 条款</h3>
          <ul class="mt-4 space-y-3">
            <li>
              <NuxtLink to="/privacy" class="text-sm text-gray-500 transition-colors hover:text-indigo-600">隐私政策</NuxtLink>
            </li>
            <li>
              <NuxtLink to="/terms" class="text-sm text-gray-500 transition-colors hover:text-indigo-600">服务条款</NuxtLink>
            </li>
          </ul>
        </div>

        <!-- 社交媒体栏目 -->
        <!-- <div>
          <h3 class="text-sm font-medium text-gray-900">关注我</h3>
          <div class="mt-4 flex space-x-4">
            <a href="#" class="text-gray-400 transition-colors hover:text-indigo-600">
              <span class="sr-only">微信</span>
              <Github class="size-5 fill-[#24292F]" />
            </a>
            <a href="https://hi-offer.net" target="_blank" rel="noopener noreferrer" class="text-gray-400 transition-colors hover:text-indigo-600">
              <span class="sr-only">博客</span>
            </a>
          </div>
        </div> -->
      </div>

      <!-- 版权信息 -->
      <div class="mt-10 border-t border-gray-100 pt-8">
        <!-- 备案信息和联系方式 -->
        <div class="mb-6 text-center">
          <div class="flex flex-col space-y-2 text-xs text-gray-500 sm:flex-row sm:justify-center sm:space-x-6 sm:space-y-0">
            <div class="flex items-center justify-center space-x-4">
              <span>📧 联系邮箱：<EMAIL></span>
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-400">
            <span>粤ICP备2025418393号</span>
          </div>
        </div>
        
        <!-- 版权信息 -->
        <p class="text-center text-xs text-gray-400">
          &copy; {{ new Date().getFullYear() }} Hi-Offer | 由
          <a href="https://hi-offer.net" target="_blank" rel="noopener noreferrer" class="text-gray-500 transition-colors hover:text-indigo-600"> Hi-Offer </a>
          精心打造
        </p>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import Github from '@/assets/icons/github.svg'
import Logo from '@/assets/icons/logo.svg'
import Popover from '@/components/Popover/index.client.vue'

</script>
