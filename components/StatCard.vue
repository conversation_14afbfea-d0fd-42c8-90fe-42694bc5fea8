<template>
  <div class="flex flex-col items-center text-center">
    <div class="mb-3 overflow-hidden" :style="{ animationDelay: `${index * 0.2}s` }">
      <div class="animate-countUp text-4xl font-bold text-transparent md:text-5xl lg:text-6xl">
        <span class="bg-gradient-to-r from-purple-600 via-pink-500 to-indigo-600 bg-clip-text">{{ stat.value }}</span>
      </div>
    </div>
    <div class="animate-fadeIn text-sm font-medium text-gray-600 md:text-base" :style="{ animationDelay: `${0.2 + index * 0.2}s` }">
      {{ stat.label }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { useIntersectionObserver } from '@vueuse/core'

const props = defineProps({
  stat: {
    type: Object,
    required: true,
  },
  index: {
    type: Number,
    default: 0,
  },
})

const isVisible = ref(false)
const el = ref(null)
const countRef = ref(null)
const displayValue = ref('0')

// 数值动画
const animateValue = (start: number, end: string, duration: number) => {
  // 如果结尾有非数字字符（如 "50万+"），提取数字部分
  const endValue = parseInt(end.replace(/\D/g, ''))
  let startTimestamp: number | null = null

  const step = (timestamp: number) => {
    if (!startTimestamp) startTimestamp = timestamp
    const progress = Math.min((timestamp - startTimestamp) / duration, 1)
    // 使用缓动函数让动画更自然
    const easeOutProgress = 1 - Math.pow(1 - progress, 3)
    const currentValue = Math.floor(easeOutProgress * (endValue - start) + start)

    // 添加原始单位（如 "万+"）
    const unit = end.replace(/[0-9]/g, '')
    displayValue.value = currentValue + unit

    if (progress < 1) {
      window.requestAnimationFrame(step)
    }
  }

  window.requestAnimationFrame(step)
}

useIntersectionObserver(
  el,
  ([{ isIntersecting }]) => {
    if (isIntersecting && !isVisible.value) {
      isVisible.value = true
      // 开始数值动画
      setTimeout(
        () => {
          animateValue(0, props.stat.value, 1500)
        },
        300 + props.index * 100,
      )
    }
  },
  { threshold: 0.1 },
)
</script>

<style scoped>
@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-countUp {
  animation: countUp 1s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 1s ease-out forwards;
}

.text-gradient-01 {
  background: linear-gradient(90deg, #ff3bff 0%, #ecbfbf 38.02%, #5c24ff 75.83%, #d94fd5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}
</style>
