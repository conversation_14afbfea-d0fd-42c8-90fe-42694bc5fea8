<template>
  <div
    ref="el"
    class="group relative overflow-hidden rounded-2xl bg-white p-8 shadow-lg transition-all duration-500 hover:-translate-y-2 hover:shadow-xl"
    :class="{ 'animate-scaleIn': isVisible }"
    :style="`animation-delay: ${index * 0.2}s`"
  >
    <!-- 背景装饰 -->
    <div
      class="absolute -right-16 -top-16 h-32 w-32 rounded-full bg-gradient-to-r opacity-10 transition-transform duration-500 group-hover:scale-[2.5]"
      :class="feature.color"
    ></div>

    <!-- 图标 -->
    <div
      :class="`mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r ${feature.color} text-white shadow-lg transition-all duration-300 group-hover:scale-110`"
    >
      <div class="text-2xl font-bold">
        <component :is="iconMap[feature.iconText]" class="h-10 w-10" />
      </div>
    </div>

    <!-- 内容 -->
    <h3 class="mb-3 text-xl font-bold text-gray-800 transition-all duration-300 group-hover:translate-x-2">{{ feature.title }}</h3>
    <p class="text-gray-600 transition-all duration-300 group-hover:translate-x-2">{{ feature.description }}</p>

    <!-- 隐藏的"了解更多"链接，悬停时显示 -->
    <!-- <div class="mt-4 transform opacity-0 transition-all duration-300 group-hover:translate-x-2 group-hover:opacity-100">
      <a href="#" class="inline-flex items-center text-sm font-medium text-purple-600 hover:text-purple-800">
        了解更多
        <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </a>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { useIntersectionObserver } from '@vueuse/core'
import TargetCoreIcon from '@/assets/icons/target-core.svg'
import ArrowUpwardIcon from '@/assets/icons/arrow-upward.svg'
import RefreshCycleIcon from '@/assets/icons/refresh-cycle.svg'

const props = defineProps({
  feature: {
    type: Object,
    required: true,
  },
  index: {
    type: Number,
    default: 0,
  },
})

const isVisible = ref(false)
const el = ref(null)

const iconMap: Record<string, Component> = {
  'target-core': TargetCoreIcon,
  'arrow-upward': ArrowUpwardIcon,
  'refresh-cycle': RefreshCycleIcon,
}

useIntersectionObserver(
  el,
  ([{ isIntersecting }]) => {
    if (isIntersecting) {
      isVisible.value = true
    }
  },
  { threshold: 0.1 },
)
</script>

<style scoped>
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
</style>
