<template>
  <div
    class="font-inter relative flex w-full max-w-sm flex-col items-center justify-center overflow-hidden rounded-lg bg-[#ebf1ff] p-6 shadow-xl sm:max-w-2xl sm:p-8 lg:max-w-4xl"
  >
    <!-- 生成中状态 -->
    <template v-if="isGenerating">
      <Bg class="pointer-events-none absolute left-1/2 top-1/2 z-10 h-full w-full -translate-x-1/2 -translate-y-1/2" />
      <div class="relative z-20 flex flex-col items-center gap-8 sm:gap-12">
        <IconDocument class="h-12 w-12 sm:h-16 sm:w-16" />
        <h3 class="-mt-4 text-center text-xl font-bold leading-8 sm:-mt-6 sm:text-[28px] sm:leading-[42px]">正在生成面试评价...</h3>
        <div class="h-[6px] w-48 overflow-hidden rounded-full bg-[#00000008] shadow-[0_0.5px_2px_#0000001A,0_0_1px_#00000033] sm:w-64">
          <div class="animate-progress h-full rounded-full bg-[#2534de]" />
        </div>
        <div class="flex flex-col items-center gap-2">
          <button
            class="flex items-center justify-center gap-1 rounded-md border border-[#0000000F] bg-white px-4 py-2 text-sm font-medium hover:bg-[#dfe1e5] sm:px-6 md:px-3 md:text-base"
            @click="emit('stop')"
          >
            停止生成
          </button>
          <button v-if="showSkipButton" class="text-xs text-gray-400 hover:text-gray-500" @click="handleSkip">跳过生成，直接结束</button>
        </div>
      </div>
    </template>

    <!-- 评价结果 -->
    <template v-else-if="evaluation && isShowingResult">
      <div class="scrollbar max-h-[75vh] w-full overflow-y-auto rounded-lg sm:max-h-[80vh] lg:max-h-[85vh]">
        <div class="flex items-center justify-between rounded-t-lg border-b bg-white px-4 py-3 sm:px-6 sm:py-4">
          <h2 class="text-lg font-medium text-gray-900 sm:text-xl">面试评价</h2>
          <button
            @click="handleClose"
            class="flex items-center gap-1 rounded-lg bg-red-50 px-2 py-1 text-xs text-red-600 transition-colors hover:bg-red-100 sm:px-3 sm:py-1.5 sm:text-sm"
          >
            <span>退出面试</span>
          </button>
        </div>
        <div class="rounded-b-lg bg-white p-4 sm:p-6">
          <InterviewEvaluationResult :evaluation="evaluation" />
        </div>
      </div>
    </template>

    <!-- 初始确认状态 -->
    <template v-else>
      <Bg class="pointer-events-none absolute left-1/2 top-1/2 z-10 h-full w-full -translate-x-1/2 -translate-y-1/2 object-cover" />
      <div class="relative z-20 flex flex-col items-center gap-8 sm:gap-12">
        <IconDocument class="h-12 w-12 sm:h-16 sm:w-16" />
        <h3 class="-mt-4 text-center text-xl font-bold leading-8 sm:-mt-6 sm:text-[28px] sm:leading-[42px]">确定要结束本次面试吗？</h3>
        <p class="text-center text-sm text-gray-500 sm:text-base">系统将为您生成本次面试的详细评价</p>
        <div class="flex flex-col gap-3 sm:flex-row sm:gap-4">
          <button
            @click="emit('stop')"
            class="flex items-center justify-center gap-1 rounded-lg border bg-white px-4 py-3 text-sm text-gray-700 transition-all hover:bg-gray-50 sm:text-base"
          >
            取消
          </button>
          <button
            @click="emit('check')"
            class="flex items-center justify-center gap-1 rounded-lg bg-[#2534de] px-4 py-3 text-sm font-semibold text-white transition-all hover:opacity-85 sm:text-base"
          >
            确认结束
            <IconArrow class="h-4 w-4 rotate-180 sm:h-5 sm:w-5" />
          </button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import IconDocument from '@/assets/icons/icon-document.svg'
import IconArrow from '@/assets/icons/present-arrow-left.svg'
import Bg from '@/assets/icons/interview-generating-panel-bg.svg'
import type { ComprehensiveEvaluation } from '~/server/prompt-service/interview-evaluation'
import { useRouter } from 'vue-router'
import { useInterviewStore } from '~/stores/interview'
import { useMessages } from '~/composables/useMessages'
import { usePrepareInterviewStore } from '~/stores/prepare-interview'
import { useEventBus } from '@/composables/useEventBus'

const props = defineProps<{
  evaluation?: ComprehensiveEvaluation | null
  isGenerating: boolean
}>()

const isShowingResult = ref(false)

const emit = defineEmits<{
  (e: 'stop'): void
  (e: 'check'): void
  (e: 'reset'): void
  (e: 'evaluate'): void
}>()

watch(
  () => props.evaluation,
  newVal => {
    if (newVal) {
      isShowingResult.value = true
    }
  },
)

// 监听evaluate事件，触发评价生成
watch(
  () => emit,
  () => {
    // 这里会被父组件调用来生成评价
  },
)

const router = useRouter()
const interviewStore = useInterviewStore()
const { messages } = useMessages()
const { formData } = usePrepareInterviewStore()
const eventBus = useEventBus()

const showSkipButton = ref(false)

// 3秒后显示跳过按钮
onMounted(() => {
  setTimeout(() => {
    if (props.isGenerating) {
      showSkipButton.value = true
    }
  }, 3000)
})

// 重置showSkipButton状态
watch(
  () => props.isGenerating,
  newVal => {
    if (!newVal) {
      showSkipButton.value = false
    }
  },
)

const handleSkip = () => {
  interviewStore.endInterview()
  router.push('/dashboard')
}

const handleClose = async () => {
  if (!props.evaluation) return

  try {
    //  to do 保存面试记录
    // 计算持续时间（秒）
    // const duration = Math.floor((Date.now() - (interviewStore.startTime || 0)) / 1000)

    // 准备面试记录数据
    // const interviewData = {
    //   type: interviewStore.currentType,
    //   interviewer: interviewStore.currentInterviewer?.name || '系统面试官',
    //   duration,
    //   date: new Date().toISOString(),
    //   messages: messages.value
    //     .filter(msg => !msg.isTemp)
    //     .map(msg => ({
    //       role: msg.role,
    //       content: msg.content,
    //       timestamp: new Date().toISOString(),
    //     })),
    //   evaluation: {
    //     stageInsights: props.evaluation.stageInsights,
    //     technicalInsights: props.evaluation.technicalInsights,
    //     communicationInsights: props.evaluation.communicationInsights,
    //     careerInsights: props.evaluation.careerInsights,
    //     practicalAdvice: props.evaluation.practicalAdvice,
    //     overallSummary: props.evaluation.overallSummary,
    //     keyRecommendations: props.evaluation.keyRecommendations,
    //     nextSteps: props.evaluation.nextSteps,
    //   },
    // }

    // 调用保存接口
    // const { data, error } = await useFetch('/api/interview/create', {
    //   method: 'POST',
    //   body: interviewData,
    // })

    // if (error.value) {
    //   throw new Error(error.value.message || '保存面试记录失败')
    // }

    // if (!data.value) {
    //   throw new Error('保存面试记录失败')
    // }

    // 重置状态并退出
    isShowingResult.value = false
    emit('reset')
    emit('stop')
    interviewStore.endInterview()
    router.push('/dashboard')
  } catch (error: any) {
    // 使用 eventBus 显示错误提示
    eventBus.emit('showToast', { message: error.message || '保存面试记录失败，请重试', type: 'error' })
  }
}
</script>

<style scoped>
.animate-progress {
  width: 0;
  animation: progress 1.5s ease-in-out infinite;
}

@keyframes progress {
  0% {
    width: 0;
    margin-left: 0;
  }
  50% {
    width: 100%;
    margin-left: 0;
  }
  100% {
    width: 0;
    margin-left: 100%;
  }
}
</style>
