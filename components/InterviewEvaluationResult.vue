<template>
  <div class="space-y-6">
    <!-- 总体评价摘要 -->
    <div class="rounded-xl border border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-6">
      <h3 class="mb-4 flex items-center gap-2 text-xl font-semibold text-gray-900">
        <span class="h-6 w-1 rounded-full bg-blue-500"></span>
        总体评价
      </h3>
      <p class="mb-4 leading-relaxed text-gray-700">{{ evaluation.overallSummary }}</p>

      <!-- 核心建议 -->
      <div class="mt-4">
        <h4 class="mb-3 font-medium text-gray-900">核心建议</h4>
        <div class="grid gap-2 md:grid-cols-2">
          <div
            v-for="(recommendation, index) in evaluation.keyRecommendations"
            :key="index"
            class="flex items-start gap-2 rounded-lg bg-white/80 p-3 shadow-sm"
          >
            <div class="mt-1 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
            <span class="text-sm text-gray-700">{{ recommendation }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 各阶段洞察 -->
    <div v-if="evaluation.stageInsights?.length">
      <h3 class="mb-6 flex items-center gap-2 text-xl font-semibold text-gray-900">
        <span class="h-6 w-1 rounded-full bg-green-500"></span>
        面试阶段表现
      </h3>
      <div class="grid gap-4 md:grid-cols-2">
        <div
          v-for="stage in evaluation.stageInsights"
          :key="stage.stage"
          class="rounded-xl border border-gray-100 bg-white p-5 shadow-sm transition-shadow hover:shadow-md"
        >
          <h4 class="mb-3 text-lg font-semibold text-gray-900">{{ stage.stageName }}</h4>
          <p class="mb-4 text-sm text-gray-600">{{ stage.summary }}</p>

          <!-- 亮点 -->
          <div v-if="stage.highlights?.length" class="mb-3">
            <span class="text-sm font-medium text-green-600">亮点表现：</span>
            <ul class="mt-1 space-y-1">
              <li v-for="highlight in stage.highlights" :key="highlight" class="flex items-start gap-2 text-sm text-gray-600">
                <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-green-500"></span>
                {{ highlight }}
              </li>
            </ul>
          </div>

          <!-- 改进点 -->
          <div v-if="stage.improvements?.length" class="mb-3">
            <span class="text-sm font-medium text-orange-600">改进空间：</span>
            <ul class="mt-1 space-y-1">
              <li v-for="improvement in stage.improvements" :key="improvement" class="flex items-start gap-2 text-sm text-gray-600">
                <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-orange-500"></span>
                {{ improvement }}
              </li>
            </ul>
          </div>

          <!-- 具体建议 -->
          <div v-if="stage.specificAdvice?.length">
            <span class="text-sm font-medium text-blue-600">具体建议：</span>
            <ul class="mt-1 space-y-1">
              <li v-for="advice in stage.specificAdvice" :key="advice" class="flex items-start gap-2 text-sm text-gray-600">
                <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
                {{ advice }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术能力洞察 -->
    <div class="rounded-xl border border-gray-100 bg-white p-6 shadow-sm">
      <h3 class="mb-6 flex items-center gap-2 text-xl font-semibold text-gray-900">
        <span class="h-6 w-1 rounded-full bg-purple-500"></span>
        技术能力分析
      </h3>
      <div class="grid gap-6 md:grid-cols-2">
        <!-- 知识盲点 -->
        <div v-if="evaluation.technicalInsights?.knowledgeGaps?.length">
          <h4 class="mb-3 font-medium text-red-600">需要补强的知识点</h4>
          <ul class="space-y-2">
            <li v-for="gap in evaluation.technicalInsights.knowledgeGaps" :key="gap" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-red-500"></span>
              {{ gap }}
            </li>
          </ul>
        </div>

        <!-- 实践技能 -->
        <div v-if="evaluation.technicalInsights?.practicalSkills?.length">
          <h4 class="mb-3 font-medium text-green-600">实践技能优势</h4>
          <ul class="space-y-2">
            <li v-for="skill in evaluation.technicalInsights.practicalSkills" :key="skill" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-green-500"></span>
              {{ skill }}
            </li>
          </ul>
        </div>

        <!-- 问题解决方法 -->
        <div v-if="evaluation.technicalInsights?.problemSolvingApproach?.length">
          <h4 class="mb-3 font-medium text-blue-600">问题解决思路</h4>
          <ul class="space-y-2">
            <li v-for="approach in evaluation.technicalInsights.problemSolvingApproach" :key="approach" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
              {{ approach }}
            </li>
          </ul>
        </div>

        <!-- 技术沟通 -->
        <div v-if="evaluation.technicalInsights?.technicalCommunication?.length">
          <h4 class="mb-3 font-medium text-indigo-600">技术表达能力</h4>
          <ul class="space-y-2">
            <li v-for="comm in evaluation.technicalInsights.technicalCommunication" :key="comm" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-indigo-500"></span>
              {{ comm }}
            </li>
          </ul>
        </div>
      </div>

      <!-- 技术改进建议 -->
      <div v-if="evaluation.technicalInsights?.improvements?.length" class="mt-6 rounded-lg bg-purple-50 p-4">
        <h4 class="mb-3 font-medium text-purple-800">技术提升建议</h4>
        <ul class="space-y-2">
          <li v-for="improvement in evaluation.technicalInsights.improvements" :key="improvement" class="flex items-start gap-2 text-sm text-purple-700">
            <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-purple-600"></span>
            {{ improvement }}
          </li>
        </ul>
      </div>
    </div>

    <!-- 沟通表达分析 -->
    <div class="rounded-xl border border-gray-100 bg-white p-6 shadow-sm">
      <h3 class="mb-6 flex items-center gap-2 text-xl font-semibold text-gray-900">
        <span class="h-6 w-1 rounded-full bg-orange-500"></span>
        沟通表达分析
      </h3>
      <div class="grid gap-6 md:grid-cols-2">
        <!-- 沟通优势 -->
        <div v-if="evaluation.communicationInsights?.strengths?.length">
          <h4 class="mb-3 font-medium text-green-600">沟通优势</h4>
          <ul class="space-y-2">
            <li v-for="strength in evaluation.communicationInsights.strengths" :key="strength" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-green-500"></span>
              {{ strength }}
            </li>
          </ul>
        </div>

        <!-- 改进领域 -->
        <div v-if="evaluation.communicationInsights?.areasForImprovement?.length">
          <h4 class="mb-3 font-medium text-orange-600">待改进领域</h4>
          <ul class="space-y-2">
            <li v-for="area in evaluation.communicationInsights.areasForImprovement" :key="area" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-orange-500"></span>
              {{ area }}
            </li>
          </ul>
        </div>

        <!-- 表达技巧 -->
        <div v-if="evaluation.communicationInsights?.expressionTips?.length">
          <h4 class="mb-3 font-medium text-blue-600">表达技巧建议</h4>
          <ul class="space-y-2">
            <li v-for="tip in evaluation.communicationInsights.expressionTips" :key="tip" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
              {{ tip }}
            </li>
          </ul>
        </div>

        <!-- 互动风格 -->
        <div v-if="evaluation.communicationInsights?.interactionStyle?.length">
          <h4 class="mb-3 font-medium text-indigo-600">互动风格优化</h4>
          <ul class="space-y-2">
            <li v-for="style in evaluation.communicationInsights.interactionStyle" :key="style" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-indigo-500"></span>
              {{ style }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 职业发展洞察 -->
    <div class="rounded-xl border border-gray-100 bg-white p-6 shadow-sm">
      <h3 class="mb-6 flex items-center gap-2 text-xl font-semibold text-gray-900">
        <span class="h-6 w-1 rounded-full bg-teal-500"></span>
        职业发展分析
      </h3>
      <div class="grid gap-6 md:grid-cols-2">
        <!-- 动机表现 -->
        <div v-if="evaluation.careerInsights?.motivation?.length">
          <h4 class="mb-3 font-medium text-green-600">职业动机</h4>
          <ul class="space-y-2">
            <li v-for="motiv in evaluation.careerInsights.motivation" :key="motiv" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-green-500"></span>
              {{ motiv }}
            </li>
          </ul>
        </div>

        <!-- 职业规划 -->
        <div v-if="evaluation.careerInsights?.careerPlanning?.length">
          <h4 class="mb-3 font-medium text-blue-600">职业规划</h4>
          <ul class="space-y-2">
            <li v-for="plan in evaluation.careerInsights.careerPlanning" :key="plan" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-blue-500"></span>
              {{ plan }}
            </li>
          </ul>
        </div>

        <!-- 角色匹配度 -->
        <div v-if="evaluation.careerInsights?.roleAlignment?.length">
          <h4 class="mb-3 font-medium text-purple-600">岗位匹配度</h4>
          <ul class="space-y-2">
            <li v-for="align in evaluation.careerInsights.roleAlignment" :key="align" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-purple-500"></span>
              {{ align }}
            </li>
          </ul>
        </div>

        <!-- 发展建议 -->
        <div v-if="evaluation.careerInsights?.developmentSuggestions?.length">
          <h4 class="mb-3 font-medium text-orange-600">发展建议</h4>
          <ul class="space-y-2">
            <li v-for="suggestion in evaluation.careerInsights.developmentSuggestions" :key="suggestion" class="flex items-start gap-2 text-sm text-gray-600">
              <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-orange-500"></span>
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 实用建议 -->
    <div v-if="evaluation.practicalAdvice?.length">
      <h3 class="mb-6 flex items-center gap-2 text-xl font-semibold text-gray-900">
        <span class="h-6 w-1 rounded-full bg-red-500"></span>
        具体改进方案
      </h3>
      <div class="space-y-4">
        <div v-for="advice in evaluation.practicalAdvice" :key="advice.category" class="rounded-xl border border-gray-100 bg-white p-5 shadow-sm">
          <h4 class="mb-4 text-lg font-semibold text-gray-900">{{ advice.categoryName }}</h4>
          <div class="space-y-4">
            <div v-for="(item, index) in advice.items" :key="index" class="rounded-lg bg-gray-50 p-4">
              <div class="mb-2">
                <span class="text-sm font-medium text-red-600">问题：</span>
                <span class="text-sm text-gray-700">{{ item.issue }}</span>
              </div>
              <div class="mb-2">
                <span class="text-sm font-medium text-blue-600">建议：</span>
                <span class="text-sm text-gray-700">{{ item.suggestion }}</span>
              </div>
              <div v-if="item.examples?.length">
                <span class="text-sm font-medium text-green-600">具体示例：</span>
                <ul class="mt-1 space-y-1">
                  <li v-for="example in item.examples" :key="example" class="flex items-start gap-2 text-sm text-gray-600">
                    <span class="mt-1.5 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-green-500"></span>
                    {{ example }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下一步行动计划 -->
    <div v-if="evaluation.nextSteps?.length" class="rounded-xl border border-gray-100 bg-gradient-to-r from-green-50 to-teal-50 p-6">
      <h3 class="mb-4 flex items-center gap-2 text-xl font-semibold text-gray-900">
        <span class="h-6 w-1 rounded-full bg-green-500"></span>
        下一步行动计划
      </h3>
      <div class="grid gap-3 md:grid-cols-2">
        <div v-for="(step, index) in evaluation.nextSteps" :key="index" class="flex items-start gap-3 rounded-lg bg-white/80 p-4 shadow-sm">
          <div class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-green-100 text-sm font-semibold text-green-600">
            {{ index + 1 }}
          </div>
          <span class="text-sm text-gray-700">{{ step }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ComprehensiveEvaluation } from '~/server/prompt-service/interview-evaluation'

const props = defineProps<{
  evaluation: ComprehensiveEvaluation
}>()
</script>
