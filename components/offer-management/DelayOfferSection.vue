<template>
  <div class="flex min-h-[400px] flex-col md:flex-row">
    <!-- 左栏：原则说明 -->
    <div class="flex flex-col p-8 md:w-1/3">
      <h3 class="mb-6 flex items-center text-xl font-semibold text-purple-700">
        <svg class="mr-2 h-6 w-6 text-purple-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" />
          <path d="M12 8v4l3 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        拖延offer的核心原则
      </h3>
      <ul class="space-y-4 text-sm text-gray-700">
        <li v-for="(principle, idx) in DELAY_PRINCIPLES" :key="idx" class="flex items-start gap-3">
          <span class="inline-flex h-6 w-6 items-center justify-center rounded-full bg-purple-50 font-bold text-purple-600">{{ idx + 1 }}</span>
          <span>{{ principle }}</span>
        </li>
      </ul>
    </div>
    <!-- 分割线 -->
    <div class="my-8 hidden w-px bg-gradient-to-b from-purple-100 to-transparent md:block"></div>
    <!-- 右栏：操作区 -->
    <div class="flex flex-col gap-8 p-8 md:w-2/3">
      <!-- 选择理由 -->
      <div>
        <label class="mb-3 block text-base font-medium text-gray-800">选择拖延理由</label>
        <Listbox v-model="selectedReason">
          <div class="relative">
            <ListboxButton
              class="relative w-full cursor-pointer rounded-lg border-2 border-purple-200 bg-white py-3 pl-4 pr-10 text-left text-sm text-gray-700 shadow-sm transition focus:border-purple-500 focus:ring-2 focus:ring-purple-100"
            >
              <span>{{ selectedOption?.label || '请选择拖延理由' }}</span>
              <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                <svg class="h-5 w-5 text-purple-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
                </svg>
              </span>
            </ListboxButton>
            <ListboxOptions
              class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none"
            >
              <template v-for="group in allOptions" :key="group.group">
                <div v-if="group.options.length" class="px-3 py-2 text-xs font-medium text-gray-400">{{ group.group }}</div>
                <ListboxOption
                  v-for="option in group.options"
                  :key="option.id"
                  :value="option.id"
                  class="ui-active:bg-purple-100 ui-active:text-purple-700 relative cursor-pointer select-none py-2 pl-10 pr-4 transition hover:bg-purple-50"
                >
                  <span :class="selectedReason === option.id ? 'font-semibold text-purple-700' : 'font-normal'">
                    {{ option.label }}
                  </span>
                  <span v-if="selectedReason === option.id" class="absolute inset-y-0 left-0 flex items-center pl-3 text-purple-600">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                    </svg>
                  </span>
                </ListboxOption>
              </template>
            </ListboxOptions>
          </div>
        </Listbox>
        <p class="mt-2 text-xs text-gray-400">选择后可查看推荐话术</p>
      </div>
      <!-- 参考话术 -->
      <Transition name="fade">
        <SpeechTemplatePanel
          v-if="selectedReason"
          :templates="currentTemplates"
          :activeIndex="activeTemplateIndex"
          @update:activeIndex="val => (activeTemplateIndex = val)"
          @copy="copyToClipboard"
        />
      </Transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Listbox, ListboxButton, ListboxOptions, ListboxOption } from '@headlessui/vue'
import { useToast } from 'vue-toast-notification'
import SpeechTemplatePanel from './SpeechTemplatePanel.vue'
import { DELAY_REASONS, DELAY_PRINCIPLES } from '@/stores/offer'

const selectedReason = ref(DELAY_REASONS[0].id)
const activeTemplateIndex = ref(0)
const toast = useToast()
const allOptions = [{ group: '系统话术', options: DELAY_REASONS }]
const selectedOption = computed(() => DELAY_REASONS.find(r => r.id === selectedReason.value) || null)

const currentTemplates = computed(() => {
  const option = selectedOption.value
  if (!option) return []
  return option.templates || []
})

const copyToClipboard = () => {
  toast.success('已复制', {
    duration: 2000,
    position: 'top-right',
  })
}
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
