<template>
  <div v-if="templates.length" class="space-y-4">
    <label class="block text-base font-medium text-gray-800">参考模板</label>
    <div class="relative overflow-hidden rounded-lg border-2 border-dashed border-gray-300">
      <textarea :value="currentTemplate" class="min-h-[180px] w-full cursor-pointer select-none px-4 py-3 text-base text-gray-700 focus:outline-none" />
      <div class="flex items-center justify-between border-t border-gray-200 p-2">
        <div class="flex items-center gap-2">
          <button
            @click="$emit('update:activeIndex', activeIndex - 1)"
            :disabled="activeIndex === 0 || templates.length <= 1"
            class="rounded-lg p-1 transition"
            :class="
              activeIndex === 0 || templates.length <= 1 ? 'cursor-not-allowed bg-gray-100 text-gray-400' : 'bg-purple-100 text-purple-700 hover:bg-purple-200'
            "
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <span class="text-xs text-gray-500"> {{ templates.length ? activeIndex + 1 : 0 }}/{{ templates.length }} </span>
          <button
            @click="$emit('update:activeIndex', activeIndex + 1)"
            :disabled="activeIndex === templates.length - 1 || templates.length <= 1"
            class="rounded-lg p-1 transition"
            :class="
              activeIndex === templates.length - 1 || templates.length <= 1
                ? 'cursor-not-allowed bg-gray-100 text-gray-400'
                : 'bg-purple-100 text-purple-700 hover:bg-purple-200'
            "
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
        <button @click="handleCopy" class="rounded-lg bg-purple-500 px-4 py-2 text-sm text-white shadow transition hover:bg-purple-600">复制</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { useToast } from 'vue-toast-notification'
type Template = {
  title: string
  content: string
}
const props = defineProps({
  templates: {
    type: Array as PropType<Template[]>,
    default: () => [],
  },
  activeIndex: {
    type: Number,
    default: 0,
  },
})
const emits = defineEmits(['update:activeIndex', 'copy'])
const toast = useToast()
const currentTemplate = computed(() => {
  if (!props.templates.length) return ''
  return props.templates[props.activeIndex]?.content || ''
})

const handleCopy = () => {
  navigator.clipboard.writeText(currentTemplate.value)
  toast.success('已复制', {
    duration: 2000,
    position: 'top-right',
  })
  emits('copy', currentTemplate.value)
}
</script>
