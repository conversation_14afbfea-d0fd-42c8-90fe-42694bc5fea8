<template>
  <div class="rounded-2xl bg-white p-6">
    <h2 class="mb-4 text-lg font-semibold text-gray-800">{{ title || '查看所有模板' }}</h2>
    <div v-for="(group, groupIdx) in groups" :key="group.group" class="mb-4">
      <button
        class="flex w-full items-center justify-between rounded-lg bg-purple-50 px-4 py-3 text-left text-base font-medium text-purple-700 transition hover:bg-purple-100"
        @click="toggleGroup(groupIdx)"
      >
        <span>{{ group.group }}</span>
        <svg
          :class="isOpen(groupIdx) ? 'rotate-180' : ''"
          class="h-5 w-5 text-purple-400 transition-transform"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      <transition name="fade">
        <div v-if="isOpen(groupIdx)" class="mt-2 grid gap-4 md:grid-cols-2">
          <div v-for="option in group.options" :key="option.id" class="rounded-lg border border-purple-100 bg-purple-50 p-4">
            <div class="mb-2 text-base font-semibold text-purple-700">{{ option.label }}</div>
            <div v-if="option.templates">
              <div v-for="(tpl, idx) in option.templates" :key="idx" class="mb-2 rounded bg-white p-3 text-sm shadow">
                <div class="mb-1 font-medium text-gray-600">{{ tpl.title }}</div>
                <div class="whitespace-pre-line text-gray-700">{{ tpl.content }}</div>
              </div>
            </div>
            <div v-else-if="option.template">
              <div class="rounded bg-white p-3 text-sm shadow">
                <div class="whitespace-pre-line text-gray-700">{{ option.template }}</div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  groups: Array<{
    group: string
    options: Array<any>
  }>
  title?: string
}>()

const openGroups = ref<number[]>([])

watch(
  () => props.groups,
  () => {
    openGroups.value = []
  },
  { deep: true },
)

const toggleGroup = (idx: number) => {
  if (openGroups.value.includes(idx)) {
    openGroups.value = openGroups.value.filter(i => i !== idx)
  } else {
    openGroups.value.push(idx)
  }
}

const isOpen = (idx: number) => openGroups.value.includes(idx)
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
