<template>
  <div ref="containerRef" class="relative h-full overflow-hidden bg-gradient-to-br from-gray-800 to-gray-700 shadow-xl">
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.2),rgba(255,255,255,0))]"></div>

    <!-- 摄像头未开启时显示的提示 -->
    <div v-if="!isVideoEnabled" class="flex h-full items-center justify-center">
      <div class="text-center text-gray-400">
        <div class="i-carbon-camera text-6xl opacity-20"></div>
        <p class="mt-4 text-sm">{{ cameraError || '正在启动摄像头...' }}</p>
      </div>
    </div>

    <!-- 摄像头视频显示 -->
    <video v-show="isVideoEnabled" ref="videoRef" class="absolute inset-0 h-full w-full object-cover" autoplay playsinline muted></video>
  </div>
</template>

<script setup lang="ts">
import { useUserMedia } from '@vueuse/core'

const videoRef = ref<HTMLVideoElement | null>(null)
const cameraError = ref<string>('')
const mediaConfig = ref<MediaTrackConstraints>({
  width: { ideal: 1920, min: 1280 },
  height: { ideal: 1080, min: 720 },
  aspectRatio: 16 / 9,
  frameRate: { ideal: 30, min: 24 },
  facingMode: 'user',
})

// 获取摄像头支持的参数
const getOptimalConstraints = async () => {
  try {
    // 获取第一个视频设备
    const devices = await navigator.mediaDevices.enumerateDevices()
    const videoDevice = devices.find(device => device.kind === 'videoinput')

    if (!videoDevice) {
      throw new Error('未找到摄像头设备')
    }

    // 先尝试获取设备的能力
    const stream = await navigator.mediaDevices.getUserMedia({
      video: {
        deviceId: videoDevice.deviceId,
        width: { ideal: 3840 },
        height: { ideal: 2160 },
      },
    })

    // 获取实际支持的参数
    const track = stream.getVideoTracks()[0]
    const capabilities = track.getCapabilities()
    const settings = track.getSettings()

    // 停止临时流
    stream.getTracks().forEach(track => track.stop())

    // 返回最优的约束条件
    return {
      deviceId: videoDevice.deviceId,
      width: {
        ideal: capabilities.width?.max || settings.width || 1920,
        min: 1280,
      },
      height: {
        ideal: capabilities.height?.max || settings.height || 1080,
        min: 720,
      },
      aspectRatio: 16 / 9,
      frameRate: {
        ideal: capabilities.frameRate?.max || settings.frameRate || 30,
        min: 24,
      },
      facingMode: 'user',
    }
  } catch (error) {
    console.error('获取摄像头参数失败:', error)
    // 返回默认参数
    return mediaConfig.value
  }
}

// 创建 useUserMedia 实例
const {
  stream: videoStream,
  enabled: isVideoEnabled,
  start: startVideo,
  stop: stopVideo,
} = useUserMedia({
  constraints: {
    video: mediaConfig.value,
  },
})

// 监听视频流变化
watch(videoStream, stream => {
  if (stream && videoRef.value) {
    videoRef.value.srcObject = stream
  }
})

// 组件挂载时启动摄像头
onMounted(async () => {
  try {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      cameraError.value = '您的浏览器不支持访问摄像头'
      return
    }

    const devices = await navigator.mediaDevices.enumerateDevices()
    const hasVideoDevice = devices.some(device => device.kind === 'videoinput')

    if (!hasVideoDevice) {
      cameraError.value = '未检测到摄像头设备，请确保摄像头已正确连接'
      return
    }

    // 获取最优的视频约束条件
    const constraints = await getOptimalConstraints()
    // 更新配置
    mediaConfig.value = constraints
    // 启动摄像头
    await startVideo()
    cameraError.value = ''
  } catch (error: any) {
    if (error.name === 'NotAllowedError') {
      cameraError.value = '请允许访问摄像头以继续面试'
    } else if (error.name === 'NotFoundError' || (error.name === 'NotReadableError' && !navigator.mediaDevices.getSupportedConstraints().deviceId)) {
      cameraError.value = '未检测到摄像头设备，请确保摄像头已正确连接'
    } else if (error.name === 'NotReadableError') {
      cameraError.value = '无法访问摄像头，可能被其他应用程序占用'
    } else if (error.name === 'OverconstrainedError') {
      cameraError.value = '摄像头不支持请求的分辨率，正在尝试使用默认设置'
      mediaConfig.value = { facingMode: 'user' }
      await startVideo()
    } else {
      cameraError.value = '摄像头初始化失败，请检查设备连接或刷新页面重试'
    }
  }
})

// 组件卸载时停止摄像头
onBeforeUnmount(() => {
  stopVideo()
})
</script>

<style scoped>
/* 水平翻转摄像头画面，使其符合自拍镜像效果 */
video {
  transform: scaleX(-1);
  object-fit: cover;
  width: 100%;
  height: 100%;
  background-color: transparent;
}
</style>
