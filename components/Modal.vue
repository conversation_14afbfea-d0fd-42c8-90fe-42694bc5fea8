<template>
  <Teleport to="body">
    <div v-if="keepAlive || modelValue" v-show="modelValue" class="relative" :style="{ zIndex, pointerEvents: passThroughMask ? 'none' : 'auto' }">
      <slot name="mask">
        <div
          v-if="!noMask"
          class="mask fixed inset-0 bg-black bg-opacity-70"
          :class="[maskClass, fadeInOpacityAnimation]"
          @click="closeByMask && onClose()"
        ></div>
      </slot>
      <div
        class="scrollbar fixed inset-0 m-auto overflow-y-auto whitespace-nowrap text-center"
        :class="[noMask ? 'h-fit w-fit' : 'h-full w-full', wrapperClass, fadeInTransformAnimation]"
      >
        <div class="fixed inset-0" @click="closeByMask && onClose()"></div>
        <div class="inline-block h-full w-0 align-middle"></div>
        <div class="pointer-events-auto inline-block max-w-full whitespace-normal text-left align-middle" :class="outerContainerClass">
          <div
            class="relative overflow-hidden bg-white text-gray-900 dark:bg-gray-800 dark:text-white"
            :style="{ margin }"
            :class="[!noBackground && 'rounded-2xl', containerClass]"
          >
            <div v-if="showCloseButton" @click="onClose" class="absolute right-4 top-4 z-10 cursor-pointer" :class="closeButtonClass">
              <slot name="closeButton">
                <IconModalClose v-if="!noCloseButton" class="z-50 text-gray-600 dark:text-gray-400" :class="[closeButtonIconClass]" />
              </slot>
            </div>
            <slot></slot>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>
<script lang="ts">
const modalStack: { close(): void; canCloseByEsc(): boolean }[] = []

const onEscPressed = (e: KeyboardEvent) => {
  const top = modalStack[modalStack.length - 1]
  if (e.key === 'Escape' && top?.canCloseByEsc()) {
    modalStack.pop()?.close()
  }
}

if (import.meta.client) {
  window.addEventListener('keydown', onEscPressed, false)
}
</script>
<script setup lang="ts">
import IconModalClose from '@/assets/icons/modal-close.svg'

defineOptions({
  inheritAttrs: false,
})

const emit = defineEmits<{
  (ev: 'update:modelValue', value: boolean): void
}>()

const props = withDefaults(
  defineProps<{
    onClose?: () => void
    onShow?: () => void
    modelValue?: boolean
    keepAlive?: boolean
    noBackground?: boolean
    wrapperClass?: string
    containerClass?: string
    outerContainerClass?: string
    margin?: number | string
    noMask?: boolean
    closeByMask?: boolean
    noCloseButton?: boolean
    closeButtonClass?: string
    closeButtonIconClass?: string
    maskClass?: string
    showCloseButton?: boolean
    closeByEsc?: boolean
    fadeInAnimation?: boolean | string
    zIndex?: number
    inheritZIndex?: boolean
    passThroughMask?: boolean
  }>(),
  {
    closeByMask: true,
    showCloseButton: true,
    closeByEsc: true,
    fadeInAnimation: true,
    inheritZIndex: true,
  },
)

const fadeInTransformAnimation = computed(() => {
  if (props.fadeInAnimation === false) {
    return ''
  }
  return typeof props.fadeInAnimation === 'string' ? props.fadeInAnimation : 'fade-in-up'
})

const fadeInOpacityAnimation = computed(() => {
  if (props.fadeInAnimation === false) {
    return ''
  }
  return typeof props.fadeInAnimation === 'string' ? props.fadeInAnimation : 'fade-in'
})

const margin = computed(() => {
  if (props.margin === undefined || props.margin === null) {
    return '16px'
  }
  return typeof props.margin === 'number' ? `${props.margin}px` : props.margin
})

watch(
  () => props.modelValue,
  isShow => {
    if (isShow) {
      props.onShow?.()
    }
  },
  { immediate: true },
)

const zIndexGroup = props.zIndex !== undefined ? `modal-z-from-${props.zIndex}` : undefined
const injectedGroupName = inject<string | undefined>('modalZIndexGroup', undefined)
const modalZIndexGroup = zIndexGroup ?? (props.inheritZIndex ? injectedGroupName : undefined)
zIndexGroup && provide('modalZIndexGroup', zIndexGroup)
const { index: zIndex, floatTop } = useZIndex(modalZIndexGroup, props.zIndex)

defineExpose({
  show(flowToTop = true) {
    emit('update:modelValue', true)
    if (!props.modelValue || flowToTop) {
      updateStack()
      floatTop()
    }
  },
  close() {
    emit('update:modelValue', false)
  },
})

const updateStack = () => {
  const idx = modalStack.indexOf(modalRef)
  if (idx > -1) {
    modalStack.splice(idx, 1)
  }
  modalStack.push(modalRef)
}

const modalRef = {
  close: () => {
    emit('update:modelValue', false)
  },
  canCloseByEsc: () => props.closeByEsc,
}

if (import.meta.client) {
  watch(
    () => props.modelValue,
    (isShow, _, onCleanup) => {
      if (isShow) {
        updateStack()
        floatTop()
      } else {
        const idx = modalStack.indexOf(modalRef)
        if (idx > -1) {
          modalStack.splice(idx, 1)
        }
      }
    },
    { immediate: true },
  )
}

const onClose = () => {
  emit('update:modelValue', false)
  props.onClose?.()
}
</script>
