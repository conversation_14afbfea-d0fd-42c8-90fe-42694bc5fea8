<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" class="relative z-50" @close="handleClose">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="relative max-h-[95vh] w-full max-w-7xl transform rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
              <!-- 顶部装饰条 -->
              <div class="absolute inset-x-1.5 top-0 h-1.5 rounded-t-md bg-gradient-to-r from-purple-600/20 via-purple-600/40 to-pink-500/20"></div>

              <!-- 关闭按钮 -->
              <button class="absolute right-4 top-4 z-10 text-gray-400 hover:text-gray-600" @click="handleClose">
                <CloseIcon class="h-5 w-5" />
              </button>

              <div class="scrollbar max-h-[95vh] overflow-y-auto p-4">
                <Paywall />
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup>
import { Dialog, DialogPanel, TransitionChild, TransitionRoot } from '@headlessui/vue'
import CloseIcon from '@/assets/icons/close-icon.svg'

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['close'])

const handleClose = () => {
  emit('close')
}
</script>
