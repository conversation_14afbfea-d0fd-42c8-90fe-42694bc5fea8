<template>
  <div class="min-h-screen bg-gradient-to-b from-purple-50/70 to-white">
    <div class="mx-auto max-w-7xl px-4 py-20 sm:px-6 lg:px-8">
      <!-- 标题部分优 -->
      <div class="mx-auto max-w-4xl text-center">
        <h1 class="text-4xl font-bold tracking-tight sm:text-5xl">
          <span class="animate-gradient bg-gradient-to-r from-purple-600 via-pink-500 to-purple-600 bg-[length:200%_auto] bg-clip-text text-transparent">
            Credits 充值
          </span>
        </h1>
        <p class="mt-6 text-lg leading-8 text-gray-600">解锁 Hi-Offer 智能助手，让 AI 为你的求职加速。从简历服务到面试，一站式提升你的职业竞争力</p>
      </div>

      <!-- 移动端价格选择器-->
      <div class="mx-auto mt-12 block lg:hidden">
        <div class="rounded-2xl border border-gray-200/60 bg-white/80 p-6 shadow-lg backdrop-blur-sm">
          <!-- 标题部分 -->
          <div class="mb-8 flex items-center justify-between gap-4">
            <div class="flex flex-1 items-center gap-4">
              <div class="flex h-14 w-14 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-600 to-pink-500 shadow-lg shadow-purple-200/50">
                <ClockIcon class="h-7 w-7 text-white" />
              </div>
              <div>
                <div class="text-lg font-semibold text-gray-900">选择充值方案</div>
                <div class="text-sm text-gray-500">选择最适合你的方案</div>
              </div>
            </div>
          </div>

          <!-- 价格选择器 -->
          <div class="mt-4">
            <div class="relative">
              <div class="flex items-center justify-between space-x-3">
                <UiSelectionButton
                  v-for="plan in plans"
                  :key="plan.key"
                  @click="handleChangePlan(plan)"
                  :selected="selectedPlan?.key === plan.key"
                  variant="default"
                  size="lg"
                  class="relative !h-28 w-1/3 !p-3"
                >
                  <div class="flex flex-col items-center justify-center">
                    <span class="text-xs font-medium">{{ plan.name.split(' ')[0] }}</span>
                    <div class="mt-2 flex flex-col items-center">
                      <div class="flex items-center gap-1.5">
                        <span class="text-xs text-gray-400 line-through">¥{{ plan.originalPrice }}</span>
                        <span
                          class="inline-flex items-center rounded-full bg-purple-50 px-2 py-0.5 text-[10px] font-medium text-purple-700 ring-1 ring-inset ring-purple-700/10 max-xs:hidden"
                        >
                          限时优惠
                        </span>
                      </div>
                      <span class="mt-1 text-2xl font-bold">¥{{ plan.price }}</span>
                    </div>
                  </div>
                  <div
                    v-if="plan.popular"
                    class="absolute -top-2.5 left-1/2 flex -translate-x-1/2 transform items-center justify-center rounded-full bg-gradient-to-r from-purple-600 to-pink-500 px-3 py-1 shadow-sm"
                  >
                    <span class="text-nowrap text-[10px] font-medium text-white">最多人选</span>
                  </div>
                </UiSelectionButton>
              </div>
            </div>
          </div>

          <!-- Credits 展示 -->
          <div v-if="selectedPlan" class="mt-8">
            <div class="mb-6 flex items-center justify-between">
              <span class="text-sm font-medium text-gray-700">获得 Credits</span>
              <div class="flex items-baseline">
                <span class="text-3xl font-bold text-purple-600">{{ selectedPlan.credits }}</span>
                <span class="ml-1.5 text-sm text-gray-500">Credits</span>
              </div>
            </div>

            <!-- 功能列表 -->
            <div class="mt-4 space-y-2">
              <div
                v-for="feature in selectedPlan.features"
                :key="feature"
                class="flex items-start rounded-xl p-2.5 transition-colors duration-200 hover:bg-purple-50/70"
              >
                <CheckCircleIcon class="h-5 w-5 flex-shrink-0 text-purple-500" />
                <span class="ml-3 text-sm text-gray-600">{{ feature }}</span>
              </div>
            </div>

            <UiButton @click="handleSubscribe(selectedPlan)" variant="primary" size="lg" full-width> 立即充值 </UiButton>
          </div>
        </div>
      </div>

      <!-- 桌面端价格卡片网格 -->
      <div class="mx-auto mt-16 hidden max-w-7xl grid-cols-1 gap-8 lg:grid lg:grid-cols-3">
        <div
          v-for="plan in plans"
          :key="plan.key"
          class="group relative flex cursor-pointer flex-col rounded-2xl border bg-white/80 p-8 backdrop-blur-sm transition-all duration-300 hover:-translate-y-1"
          :class="{
            'border-2 border-purple-500 shadow-lg shadow-purple-200/30 ring-1 ring-purple-500/20': selectedPlan?.key === plan.key,
            'border-gray-200/60 shadow-sm hover:border-purple-200 hover:shadow-lg hover:shadow-purple-200/20': selectedPlan?.key !== plan.key,
          }"
          @click="handleChangePlan(plan)"
        >
          <div class="flex-1">
            <h3 class="text-xl font-semibold text-gray-900">
              {{ plan.name }}
            </h3>
            <p v-if="plan.popular" class="absolute right-4 top-3">
              <span class="rounded-full bg-gradient-to-r from-purple-600 to-pink-500 px-4 py-1 text-sm font-medium text-white shadow-sm"> 超值优惠 </span>
            </p>
            <p class="mt-4 text-sm leading-6 text-gray-500">
              {{ plan.description }}
            </p>
            <div class="mt-8">
              <div class="flex items-center gap-2">
                <span class="text-sm text-gray-400 line-through">¥{{ plan.originalPrice }}</span>
                <span
                  class="inline-flex items-center rounded-full bg-purple-50 px-2.5 py-0.5 text-xs font-medium text-purple-700 ring-1 ring-inset ring-purple-700/10"
                >
                  限时优惠
                </span>
                <span class="ml-1 text-4xl font-bold tracking-tight text-gray-900"> ¥{{ plan.price }} </span>
              </div>
              <p class="mt-3 text-sm font-medium text-gray-600">获得 {{ plan.credits }} AI Credits</p>
            </div>
            <ul class="mt-8 space-y-4">
              <li v-for="feature in plan.features" :key="feature" class="flex items-start">
                <div class="flex-shrink-0">
                  <CheckCircleIcon class="h-5 w-5 text-purple-500" />
                </div>
                <p class="ml-3 text-sm text-gray-500">{{ feature }}</p>
              </li>
            </ul>
          </div>
          <UiButton @click="handleSubscribe(plan)" variant="primary" size="md" full-width class="mt-8"> 立即充值 </UiButton>
        </div>
      </div>

      <PaymentDialog
        v-if="selectedPlan && isPaymentDialogOpen"
        :isOpen="true"
        :plan="selectedPlan"
        @close="handlePaymentClose"
        @success="handlePaymentSuccess"
        @timeout="handlePaymentTimeout"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { webConfig } from '~/utils/config'
import PaymentDialog from '~/components/payment/PaymentDialog.vue'
import ClockIcon from '~/assets/icons/clock-icon.svg'
import CheckCircleIcon from '~/assets/icons/check-circle-icon.svg'

type PlanKey = 'basic-credit' | 'pro-credit' | 'max-credit'

interface Plan {
  name: string
  key: PlanKey
  description: string
  price: number
  originalPrice: number
  credits: number
  features: string[]
  popular?: boolean
}

const plans: Plan[] = [
  {
    name: '基础 credit 套餐',
    key: 'basic-credit',
    description: '适合初次体验,开启面试、简历助手之旅',
    price: webConfig.sku['basic-credit'].price,
    originalPrice: webConfig.sku['basic-credit'].originPrice,
    credits: webConfig.sku['basic-credit'].credit,
    features: ['模拟面试', '面试表现分析', '简历深度优化', '简历内容扩写', '面试题预测', '简历多语言翻译'],
  },
  {
    name: '超值 credit 套餐',
    key: 'pro-credit',
    description: '超值优惠，助你全方位提升求职竞争力',
    price: webConfig.sku['pro-credit'].price,
    originalPrice: webConfig.sku['pro-credit'].originPrice,
    credits: webConfig.sku['pro-credit'].credit,
    popular: true,
    features: ['模拟面试', '面试表现分析', '简历深度优化', '简历内容扩写', '面试题预测', '简历多语言翻译'],
  },
  {
    name: '尊享 credit 套餐',
    key: 'max-credit',
    description: '最具性价比，为求职成功提供最强助力',
    price: webConfig.sku['max-credit'].price,
    originalPrice: webConfig.sku['max-credit'].originPrice,
    credits: webConfig.sku['max-credit'].credit,
    features: ['模拟面试', '面试表现分析', '简历深度优化', '简历内容扩写', '面试题预测', '简历多语言翻译'],
  },
]

const selectedPlan = ref<Plan | null>(plans[1])
const isPaymentDialogOpen = ref(false)

const handleChangePlan = (plan: Plan) => {
  selectedPlan.value = plan
}

const handleSubscribe = (_plan: Plan) => {
  selectedPlan.value = _plan
  isPaymentDialogOpen.value = true
}

const handlePaymentSuccess = () => {
  isPaymentDialogOpen.value = false
  // TODO: 更新用户状态
  navigateTo('/dashboard')
}

const handlePaymentClose = () => {
  isPaymentDialogOpen.value = false
}

const handlePaymentTimeout = () => {
  isPaymentDialogOpen.value = false
}
</script>

<style scoped>
.animate-gradient {
  background-size: 200% auto;
  animation: gradient 4s linear infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>
