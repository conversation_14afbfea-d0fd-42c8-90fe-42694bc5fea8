<template>
  <div class="relative inline-block">
    <!-- 语音输入按钮 -->
    <button
      ref="voiceButton"
      @mousedown="startRecording"
      @mouseup="stopRecording"
      @mouseleave="stopRecording"
      @touchstart="startRecording"
      @touchend="stopRecording"
      :disabled="isRecognizing || !hasPermission"
      :class="[
        'flex items-center justify-center rounded-md p-2 transition-all duration-200',
        isRecording ? 'scale-110 bg-red-500 text-white shadow-lg' : 'bg-gray-100 text-gray-600 hover:bg-gray-200',
        (isRecognizing || !hasPermission) && 'cursor-not-allowed opacity-50',
      ]"
      :title="getButtonTitle()"
    >
      <MicrophoneIcon :class="['h-5 w-5 transition-all duration-200', isRecording && 'animate-pulse']" />
    </button>

    <!-- 录音状态提示 -->
    <div
      v-if="isRecording"
      class="absolute left-1/2 top-full z-10 mt-2 -translate-x-1/2 whitespace-nowrap rounded-md bg-black/80 px-3 py-2 text-sm text-white shadow-lg"
    >
      <div class="flex items-center gap-2">
        <div class="h-2 w-2 animate-pulse rounded-full bg-red-500"></div>
        <span>按住空格键说话 ({{ recordingDuration }}s)</span>
      </div>
    </div>

    <!-- 权限请求提示 -->
    <div
      v-if="showPermissionPrompt"
      class="absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 rounded-md border border-orange-200 bg-orange-50 p-3 text-sm text-orange-800 shadow-lg"
    >
      <div class="mb-2 font-medium">需要麦克风权限</div>
      <div class="mb-3">请允许访问麦克风以使用语音输入功能</div>
      <button @click="requestPermission" class="rounded bg-orange-500 px-3 py-1 text-xs text-white hover:bg-orange-600">授权麦克风</button>
    </div>

    <!-- 错误提示 -->
    <div
      v-if="error"
      class="absolute left-1/2 top-full z-10 mt-2 w-64 -translate-x-1/2 rounded-md border border-red-200 bg-red-50 p-3 text-sm text-red-800 shadow-lg"
    >
      <div class="mb-1 font-medium">语音识别失败</div>
      <div>{{ error }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import MicrophoneIcon from '@/assets/icons/microphone-icon.svg'
import { useStreamingASR } from '@/composables/useStreamingASR'

interface Props {
  disabled?: boolean
  maxDuration?: number // 最大录音时长（秒）
}

interface Emits {
  (e: 'result', text: string): void
  (e: 'error', error: string): void
  (e: 'start'): void
  (e: 'stop'): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  maxDuration: 60,
})

const emit = defineEmits<Emits>()

// 语音识别相关状态
const { isRecognizing, displayText, error: asrError, startRecognition, clearResults } = useStreamingASR()

// 录音相关状态
const isRecording = ref(false)
const hasPermission = ref(false)
const showPermissionPrompt = ref(false)
const recordingDuration = ref(0)
const error = ref('')

// 媒体录制相关
let mediaRecorder: MediaRecorder | null = null
let audioContext: AudioContext | null = null
let audioWorkletNode: AudioWorkletNode | null = null
let mediaStream: MediaStream | null = null
let pcmData: Float32Array[] = []
let recordingTimer: ReturnType<typeof setInterval> | null = null
let spaceKeyPressed = ref(false)

// DOM 引用
const voiceButton = ref<HTMLButtonElement>()

/**
 * 获取按钮提示文本
 */
const getButtonTitle = () => {
  if (!hasPermission.value) return '点击授权麦克风权限'
  if (isRecognizing.value) return '正在识别语音...'
  if (isRecording.value) return '正在录音，松开停止'
  return '按住空格键或点击开始语音输入'
}

/**
 * 请求麦克风权限
 */
const requestPermission = async () => {
  try {
    showPermissionPrompt.value = false
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        sampleRate: 16000,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true,
      },
    })

    // 立即停止流，我们只是为了获取权限
    stream.getTracks().forEach(track => track.stop())

    hasPermission.value = true
    error.value = ''
  } catch (err: any) {
    console.error('获取麦克风权限失败:', err)
    error.value = '无法获取麦克风权限，请检查浏览器设置'
    hasPermission.value = false
  }
}

/**
 * 开始录音
 */
const startRecording = async () => {
  if (props.disabled || isRecording.value || isRecognizing.value) return

  if (!hasPermission.value) {
    showPermissionPrompt.value = true
    return
  }

  try {
    error.value = ''
    clearResults()

    // 获取音频流 - 16kHz 单声道
    mediaStream = await navigator.mediaDevices.getUserMedia({
      audio: {
        sampleRate: 16000,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true,
      },
    })

    // 创建音频上下文
    audioContext = new AudioContext({
      sampleRate: 16000,
    })

    // 加载音频处理工作线程
    await audioContext.audioWorklet.addModule('/audio-processor.js')

    // 创建音频源和处理节点
    const source = audioContext.createMediaStreamSource(mediaStream)
    audioWorkletNode = new AudioWorkletNode(audioContext, 'audio-processor')

    // 监听音频数据
    pcmData = []
    audioWorkletNode.port.onmessage = event => {
      if (event.data.type === 'audioData') {
        pcmData.push(new Float32Array(event.data.data))
      }
    }

    // 连接音频节点
    source.connect(audioWorkletNode)
    audioWorkletNode.connect(audioContext.destination)
    isRecording.value = true
    recordingDuration.value = 0

    // 开始计时
    recordingTimer = setInterval(() => {
      recordingDuration.value++

      // 检查是否超过最大时长
      if (recordingDuration.value >= props.maxDuration) {
        stopRecording()
      }
    }, 1000)

    emit('start')
  } catch (err: any) {
    console.error('开始录音失败:', err)
    error.value = '无法开始录音，请检查麦克风权限'
    isRecording.value = false

    // 清理资源
    cleanupRecording()
  }
}

/**
 * 清理录音资源
 */
const cleanupRecording = () => {
  // 停止音频上下文
  if (audioContext) {
    audioContext.close()
    audioContext = null
  }

  // 断开音频节点
  if (audioWorkletNode) {
    audioWorkletNode.disconnect()
    audioWorkletNode = null
  }

  // 停止媒体流
  if (mediaStream) {
    mediaStream.getTracks().forEach(track => track.stop())
    mediaStream = null
  }

  // 清理录制器
  if (mediaRecorder) {
    mediaRecorder = null
  }
}

/**
 * 停止录音
 */
const stopRecording = async () => {
  if (!isRecording.value) return

  isRecording.value = false

  // 清除计时器
  if (recordingTimer) {
    clearInterval(recordingTimer)
    recordingTimer = null
  }

  // 处理录音数据
  if (pcmData.length > 0) {
    await processRecording()
  }

  // 清理资源
  cleanupRecording()

  emit('stop')
}

/**
 * 将 PCM 数据转换为 WAV 格式
 */
const pcmToWav = (pcmData: Float32Array[], sampleRate: number = 16000): ArrayBuffer => {
  // 合并所有 PCM 数据
  const totalLength = pcmData.reduce((sum, chunk) => sum + chunk.length, 0)
  const mergedData = new Float32Array(totalLength)
  let offset = 0

  for (const chunk of pcmData) {
    mergedData.set(chunk, offset)
    offset += chunk.length
  }

  // 转换为 16-bit PCM
  const int16Data = new Int16Array(mergedData.length)
  for (let i = 0; i < mergedData.length; i++) {
    const sample = Math.max(-1, Math.min(1, mergedData[i]))
    int16Data[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff
  }

  // 创建 WAV 文件头
  const buffer = new ArrayBuffer(44 + int16Data.length * 2)
  const view = new DataView(buffer)

  // WAV 文件头
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i))
    }
  }

  writeString(0, 'RIFF')
  view.setUint32(4, 36 + int16Data.length * 2, true)
  writeString(8, 'WAVE')
  writeString(12, 'fmt ')
  view.setUint32(16, 16, true)
  view.setUint16(20, 1, true) // PCM format
  view.setUint16(22, 1, true) // mono
  view.setUint32(24, sampleRate, true)
  view.setUint32(28, sampleRate * 2, true)
  view.setUint16(32, 2, true)
  view.setUint16(34, 16, true)
  writeString(36, 'data')
  view.setUint32(40, int16Data.length * 2, true)

  // 写入音频数据
  const audioData = new Int16Array(buffer, 44)
  audioData.set(int16Data)

  return buffer
}

/**
 * 处理录音数据
 */
const processRecording = async () => {
  try {
    if (pcmData.length === 0) {
      error.value = '录音数据为空'
      return
    }

    // 将 PCM 数据转换为 WAV 格式
    const wavBuffer = pcmToWav(pcmData, 16000)
    const audioBlob = new Blob([wavBuffer], { type: 'audio/wav' })
    const audioFile = new File([audioBlob], 'recording.wav', { type: 'audio/wav' })

    // 开始语音识别
    await startRecognition(audioFile)
  } catch (err: any) {
    console.error('处理录音失败:', err)
    error.value = '处理录音失败'
    emit('error', error.value)
  }
}

/**
 * 键盘事件处理
 */
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.code === 'Space' && !spaceKeyPressed.value && !event.repeat) {
    event.preventDefault()
    spaceKeyPressed.value = true
    startRecording()
  }
}

const handleKeyUp = (event: KeyboardEvent) => {
  if (event.code === 'Space' && spaceKeyPressed.value) {
    event.preventDefault()
    spaceKeyPressed.value = false
    stopRecording()
  }
}

// 监听错误状态
watch(asrError, newError => {
  if (newError) {
    error.value = newError
    emit('error', newError)
  }
})

// 监听识别结果，实时发送给父组件
watch(displayText, newText => {
  if (newText && newText.trim()) {
    emit('result', newText.trim())
  }
})

// 组件挂载时检查权限和添加键盘监听
onMounted(async () => {
  // 检查是否已有麦克风权限
  try {
    const permissions = await navigator.permissions.query({ name: 'microphone' as PermissionName })
    hasPermission.value = permissions.state === 'granted'

    permissions.onchange = () => {
      hasPermission.value = permissions.state === 'granted'
    }
  } catch (err) {
    // 某些浏览器可能不支持 permissions API
    console.warn('无法检查麦克风权限状态:', err)
  }

  // 添加全局键盘事件监听
  document.addEventListener('keydown', handleKeyDown)
  document.addEventListener('keyup', handleKeyUp)
})

// 组件卸载时清理
onUnmounted(() => {
  // 停止录音
  if (isRecording.value) {
    stopRecording()
  }

  // 清除计时器
  if (recordingTimer) {
    clearInterval(recordingTimer)
  }

  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeyDown)
  document.removeEventListener('keyup', handleKeyUp)
})
</script>

<style scoped>
.voice-input-container {
  position: relative;
  display: inline-block;
}
</style>
