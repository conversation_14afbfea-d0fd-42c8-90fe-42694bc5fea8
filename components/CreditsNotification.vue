<template>
  <Transition
    enter-active-class="transition-all ease-out duration-300"
    enter-from-class="opacity-0 -translate-y-4 scale-95"
    enter-to-class="opacity-100 translate-y-0 scale-100"
    leave-active-class="transition-all ease-in duration-200"
    leave-from-class="opacity-100 translate-y-0 scale-100"
    leave-to-class="opacity-0 -translate-y-4 scale-95"
  >
    <div v-if="isVisible" class="fixed left-1/2 top-6 z-50 w-full max-w-sm -translate-x-1/2 transform px-4">
      <div class="relative overflow-hidden rounded-xl border border-gray-200/50 bg-white shadow-xl backdrop-blur-sm">
        <!-- 顶部装饰条 -->
        <div class="absolute inset-x-0 top-0 h-1 bg-gradient-to-r from-purple-500 to-pink-500"></div>

        <div class="relative p-4">
          <div class="flex items-center gap-3">
            <!-- 图标容器 -->
            <div class="relative flex-shrink-0">
              <div class="relative flex h-10 w-10 items-center justify-center rounded-lg border border-amber-100 bg-amber-50">
                <CurrencyDollarIcon class="h-5 w-5 text-amber-600" />
              </div>
              <!-- 警告小点 -->
              <div class="absolute -right-1 -top-1 h-3 w-3 animate-pulse rounded-full bg-red-500"></div>
            </div>

            <!-- 内容区域 -->
            <div class="min-w-0 flex-1">
              <h3 class="text-sm font-semibold text-gray-900">Credits 余额不足</h3>
              <p class="mt-1 text-xs text-gray-600">余额不足，请购买更多 Credits</p>

              <!-- 按钮组 -->
              <div class="mt-3 flex items-center gap-2">
                <button
                  @click="handlePurchase"
                  class="inline-flex items-center gap-1.5 rounded-lg bg-gradient-to-r from-purple-600 to-pink-500 px-3 py-1.5 text-xs font-medium text-white shadow-sm transition-all duration-200 hover:scale-105 hover:shadow-md"
                >
                  <ShoppingCartIcon class="h-3.5 w-3.5" />
                  立即购买
                </button>

                <button @click="handleClose" class="text-xs font-medium text-gray-500 transition-colors hover:text-gray-700">稍后再说</button>
              </div>
            </div>

            <!-- 关闭按钮 -->
            <button @click="handleClose" class="flex-shrink-0 rounded-lg p-1.5 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600">
              <XMarkIcon class="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { CurrencyDollarIcon, ShoppingCartIcon, XMarkIcon } from '@heroicons/vue/24/outline'

interface Props {
  isVisible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isVisible: false,
})

const emit = defineEmits<{
  close: []
  purchase: []
}>()

const autoCloseTimer = ref<NodeJS.Timeout | null>(null)

const handleClose = () => {
  emit('close')
}

const handlePurchase = () => {
  emit('purchase')
  handleClose()
}

const clearAutoCloseTimer = () => {
  if (autoCloseTimer.value) {
    clearTimeout(autoCloseTimer.value)
    autoCloseTimer.value = null
  }
}

watch(
  () => props.isVisible,
  newValue => {
    clearAutoCloseTimer()

    if (newValue) {
      autoCloseTimer.value = setTimeout(() => {
        handleClose()
      }, 5000)
    }
  },
)

onBeforeUnmount(() => {
  clearAutoCloseTimer()
})
</script>
