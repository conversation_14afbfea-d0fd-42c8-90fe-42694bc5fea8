<template>
  <div ref="containerRef">
    <slot></slot>
  </div>
</template>
<script lang="ts">
interface Props {
  preventClose?: boolean
}

export const scopes = new Set<{ props: Props; container: HTMLDivElement }>()
</script>
<script setup lang="ts">
const props = defineProps<Props>()

const containerRef = ref<HTMLDivElement>()

watch(containerRef, (container, _oldContainer, onCleanUp) => {
  if (!container) return
  const scope = { props, container }
  scopes.add(scope)
  onCleanUp(() => {
    scopes.delete(scope)
  })
})
</script>
