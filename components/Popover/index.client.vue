<template>
  <div v-bind="$attrs" ref="slotRef" :class="inline && 'inline-block'">
    <slot :isShow="isShow"></slot>
  </div>
  <Teleport to="body">
    <div
      v-if="isShow"
      ref="contentRef"
      class="absolute z-50 max-h-[100vh] rounded-lg shadow-lg"
      :style="{
        left: (props.customPosition ? props.customPosition.left : pos.left) + 'px',
        top: (props.customPosition ? props.customPosition.top : pos.top) + 'px',
        position: layout,
        zIndex,
      }"
      :class="[
        isShow && (position === 'bottom' ? 'fade-in-down' : 'fade-in-up'),
        isShow ? 'opacity-100' : 'pointer-events-none opacity-0',
        showBorder ? 'border border-gray-200' : '',
        containerClass || 'bg-white/90 backdrop-blur-md',
      ]"
    >
      <div
        v-if="showArrow"
        class="arrow absolute h-4 w-4 rotate-45"
        :class="[arrowClass || 'bg-white/90 backdrop-blur-md', showBorder ? 'border border-gray-200' : '']"
        :style="{ left: arrowOffsetX + 'px', top: arrowOffsetY + 'px' }"
      ></div>
      <PopoverScope preventClose>
        <div class="content relative z-10 rounded-xl bg-white/90 backdrop-blur-md" @click="autoClose && close()">
          <slot name="content" :key="renderKey" :close="close"></slot>
        </div>
      </PopoverScope>
    </div>
  </Teleport>
</template>
<script setup lang="ts">
import { scopes } from './Scope.client.vue'
import { useWindowScroll } from '@vueuse/core'
import { useVModel } from '@vueuse/core'

const { y: windowScrollY } = useWindowScroll()

defineOptions({
  inheritAttrs: false,
})

type Position = 'top' | 'bottom' | 'left' | 'right'

const props = withDefaults(
  defineProps<{
    isShow?: boolean
    showArrow?: boolean
    offset?: number
    /**
     * @default 0
     * @example marginXandY or [marginY, marginX]
     */
    margin?: number | [number, number]
    autoClose?: boolean // close after click
    disabled?: boolean
    showBorder?: boolean
    rerenderOnShow?: boolean
    inline?: boolean
    position?: Position | Position[]
    customPosition?: { left: number; top: number }
    align?: 'start' | 'center' | 'end'
    containerClass?: string
    arrowClass?: string
    anchor?: HTMLElement | string
    closeOnScroll?: boolean
    disableCloseByClickOutside?: boolean
  }>(),
  {
    position: 'bottom',
    offset: 20,
    closeOnScroll: false,
  },
)

const emit = defineEmits<{
  (ev: 'click'): void
  (ev: 'show'): void
  (ev: 'hide'): void
  (ev: 'update:isShow', value: boolean): void
}>()

const pos = ref({ left: 0, top: 0 })
const arrowOffsetX = ref(0)
const arrowOffsetY = ref(0)
const contentRef = ref<HTMLDivElement>()
const slotRef = ref<HTMLDivElement>()
const isShow = useVModel(props, 'isShow', emit, { defaultValue: false, passive: true })

let renderKey = ref(0)
const { index: zIndex, floatTop } = useZIndex()

const position = computed(() => props.position || 'bottom')
const layout = ref<'absolute' | 'fixed'>('absolute')
const triggerRef = computed(() => {
  const el = props.anchor
  if (typeof el === 'string') {
    return document.querySelector(el) as HTMLElement
  } else if (el instanceof HTMLElement) {
    return el
  }
  return slotRef.value
})

watch(
  triggerRef,
  (el, _oldEl, onCleanUp) => {
    const listener = (ev: Event) => {
      if (props.disabled) return
      ev.stopPropagation()
      onToggle()
    }
    el?.addEventListener('click', listener)
    onCleanUp(() => {
      el?.removeEventListener('click', listener)
    })
  },
  { immediate: true },
)

const isFixedOrStickyElement = (el: HTMLElement) => {
  let node: HTMLElement | null = el
  while (node) {
    const style = window.getComputedStyle(node)
    if (style.position === 'fixed' || style.position === 'sticky') {
      return true
    }
    node = node.parentElement
  }
  return false
}

const getPositionInPage = (el: HTMLElement) => {
  const rect = el.getBoundingClientRect()
  const isFixedOrSticky = isFixedOrStickyElement(el)

  return {
    pageX: rect.left,
    pageY: rect.top,
    width: rect.width,
    height: rect.height,
    fixedMode: isFixedOrSticky,
  }
}

function normalizeMargin(margin: number | [number, number]): [number, number] {
  if (Array.isArray(margin)) {
    return margin
  }
  return [margin, margin]
}

const calcPos = (position: Position) => {
  let triggerEl = triggerRef.value
  let contentEl = contentRef.value
  if (!triggerEl || !contentEl) return
  floatTop()
  const triggerRect = getPositionInPage(triggerEl)
  const contentRect = getPositionInPage(contentEl)
  if (triggerRect.fixedMode) {
    layout.value = 'fixed'
  } else {
    layout.value = 'absolute'
  }
  const [marginY, marginX] = normalizeMargin(props.margin || 0)
  let top = 0
  let left = 0
  if (position === 'top') {
    top = triggerRect.pageY - contentRect.height - props.offset
  } else if (position === 'bottom') {
    top = triggerRect.pageY + triggerRect.height + props.offset
  } else if (position === 'left') {
    left = triggerRect.pageX - contentRect.width - props.offset
  } else {
    left = triggerRect.pageX + triggerRect.width + props.offset
  }
  let arrowOffsetX = 0
  let arrowOffsetY = 0
  if (position === 'top' || position === 'bottom') {
    arrowOffsetX = contentRect.width / 2 - 8
    if (position === 'bottom') {
      arrowOffsetY = -8
    } else {
      arrowOffsetY = contentRect.height - 8 - 2 // sub 2px border
    }
    left = triggerRect.pageX - contentRect.width / 2 + triggerRect.width / 2

    if (props.align === 'start') {
      arrowOffsetX -= triggerRect.pageX - left
      left = triggerRect.pageX
    } else if (props.align === 'end') {
      arrowOffsetX += left + contentRect.width - triggerRect.pageX - triggerRect.width
      left = triggerRect.pageX - contentRect.width + triggerRect.width
    }
  } else {
    arrowOffsetY = contentRect.height / 2 - 8
    if (position === 'right') {
      arrowOffsetX = -8
    } else {
      arrowOffsetX = contentRect.width - 8 - 2
    }

    if (props.align === 'start') {
      arrowOffsetY -= triggerRect.pageY - top
      top = triggerRect.pageY
    } else if (props.align === 'end') {
      arrowOffsetY += top + contentRect.height - triggerRect.pageY - triggerRect.height
      top = triggerRect.pageY - contentRect.height + triggerRect.height
    }
    top = triggerRect.pageY - contentRect.height / 2 + triggerRect.height / 2
  }
  let isOverflowLeft = false
  let isOverflowRight = false
  let isOverflowTop = false
  let isOverflowBottom = false
  const right = document.documentElement.clientWidth - left - contentRect.width
  if (left - marginX < 0) {
    arrowOffsetX += left - marginX
    left = marginX
    isOverflowLeft = true
  } else if (right - marginX < 0) {
    arrowOffsetX -= right - marginX
    left += right - marginX
    isOverflowRight = true
  }
  const bottom = document.documentElement.clientHeight - top - contentRect.height
  if (top - marginY < 0) {
    arrowOffsetY += top - marginY
    top = marginY
    isOverflowTop = true
  } else if (bottom - marginY < 0) {
    arrowOffsetY -= bottom - marginY
    top += bottom - marginY
    isOverflowBottom = true
  }
  let scrollX = 0
  let scrollY = 0
  if (layout.value === 'absolute') {
    scrollX = window.scrollX
    scrollY = window.scrollY
  }
  const isOverflowX = isOverflowLeft || isOverflowRight
  const isOverflowY = isOverflowTop || isOverflowBottom
  const isOverflow = isOverflowX || isOverflowY
  return {
    left: left + scrollX,
    top: top + scrollY,
    isOverflow,
    isOverflowX,
    isOverflowY,
    isOverflowLeft,
    isOverflowRight,
    isOverflowTop,
    isOverflowBottom,
    arrowOffsetX,
    arrowOffsetY,
  }
}

const updatePos = () => {
  requestAnimationFrame(() => {
    const nonNullable = <T,>(v: T): v is NonNullable<T> => v !== null && v !== undefined
    const overflowsCount = (pos: { isOverflowLeft: boolean; isOverflowRight: boolean; isOverflowTop: boolean; isOverflowBottom: boolean }) => {
      return [pos.isOverflowLeft, pos.isOverflowRight, pos.isOverflowTop, pos.isOverflowBottom].filter(Boolean).length
    }
    const positionList = Array.isArray(position.value) ? position.value : [position.value]
    const posList = positionList
      .map(p => calcPos(p))
      .filter(nonNullable)
      .sort((a, b) => {
        // find the least overflows position
        const aOverflows = overflowsCount(a)
        const bOverflows = overflowsCount(b)
        if (aOverflows === bOverflows) {
          return 0
        }
        return aOverflows < bOverflows ? -1 : 1
      })
    pos.value = posList[0]
    arrowOffsetX.value = posList[0].arrowOffsetX
    arrowOffsetY.value = posList[0].arrowOffsetY
  })
}

const onToggle = async () => {
  emit('click')
  if (isShow.value) {
    isShow.value = false
    emit('hide')
  } else {
    if (props.rerenderOnShow) {
      renderKey.value += 1
    }
    if (props.disabled) return
    isShow.value = true
    await nextTick()
    updatePos()
    emit('show')
  }
}

const onClose = async (ev: MouseEvent | TouchEvent) => {
  if (props.disableCloseByClickOutside) return
  const el = ev.target as HTMLElement
  const shouldIgnore = [...scopes].some(scope => {
    if (scope.props.preventClose) {
      const container = scope.container
      if (container.contains(el)) {
        return true
      }
    }
    return false
  })
  if (shouldIgnore) return

  let triggerEl = triggerRef.value
  let contentEl = contentRef.value
  if (triggerEl?.contains(el) || contentEl?.contains(el)) {
    return
  }
  close()
}

const close = () => {
  isShow.value = false
  emit('hide')
}

const onResize = () => {
  if (isShow.value) {
    updatePos()
  }
}

defineExpose({
  close,
  updatePos,
})

watch(
  isShow,
  async (isShow, _, onCleanUp) => {
    if (isShow) {
      window.addEventListener('resize', onResize, false)
      nextTick(() => {
        updatePos()
      })
    }
    onCleanUp(() => {
      window.removeEventListener('resize', onResize, false)
    })
  },
  { immediate: true },
)

watch(windowScrollY, () => {
  if (isShow.value && props.closeOnScroll) {
    close()
  }
})

const onScrollOutside = (event: Event) => {
  if (!isShow.value || !props.closeOnScroll) return

  const target = event.target as HTMLElement
  const triggerEl = triggerRef.value
  const contentEl = contentRef.value

  if (contentEl?.contains(target) || triggerEl?.contains(target)) {
    return
  }

  close()
}

onMounted(() => {
  window.addEventListener('click', onClose, true)
  window.addEventListener('touchstart', onClose, true)
  window.addEventListener('scroll', onScrollOutside, true)
})

onBeforeUnmount(() => {
  window.removeEventListener('click', onClose, true)
  window.removeEventListener('touchstart', onClose, true)
  window.removeEventListener('scroll', onScrollOutside, true)
})
</script>
