{"arrowParens": "avoid", "bracketSameLine": false, "bracketSpacing": true, "semi": false, "singleQuote": true, "jsxSingleQuote": false, "quoteProps": "as-needed", "trailingComma": "all", "singleAttributePerLine": false, "vueIndentScriptAndStyle": false, "proseWrap": "preserve", "insertPragma": false, "printWidth": 160, "requirePragma": false, "tabWidth": 2, "useTabs": false, "embeddedLanguageFormatting": "auto", "organizeImportsSkipDestructiveCodeActions": true, "plugins": ["prettier-plugin-organize-imports", "prettier-plugin-tailwindcss"]}