# 简历诊断功能使用说明

## 功能概述

简历诊断功能是一个智能化的简历分析和优化工具，通过AI技术帮助用户识别简历中的问题并提供针对性的改进建议。

## 功能特点

### 🔍 智能诊断分析
- **技能匹配分析**：分析简历技能与目标职位的匹配度
- **优势技能识别**：识别简历中的优势技能点
- **匹配经验总结**：总结与目标职位相关的经验

### 🎯 问题识别
- **全面问题诊断**：识别简历中的各类问题
- **影响评估**：评估问题对求职的具体影响
- **分类整理**：按问题类型进行分类展示

### 💡 优化建议
- **具体建议**：提供可操作的优化建议
- **个性化指导**：根据目标职位提供定制化建议
- **实用改进方案**：提供实际的改进方案

### 📝 简历生成
- **优化后简历**：基于诊断结果生成优化后的简历
- **一键复制**：支持一键复制优化后的简历内容
- **保持原有风格**：在优化的同时保持简历的原有风格

## 使用流程

### 阶段一：诊断分析
1. **输入简历内容**：在左侧输入框中粘贴您的简历内容
2. **输入职位描述**：添加目标职位的职位描述和任职要求
3. **选择诊断模式**：选择标准模式或专业模式
4. **点击开始诊断**：系统将进行智能分析

### 阶段二：简历优化
1. **查看诊断报告**：详细查看技能匹配、问题诊断和优化建议
2. **点击生成优化简历**：基于诊断结果生成优化后的简历
3. **复制优化内容**：一键复制优化后的简历内容

## 技术架构

### 前端组件结构
```
pages/dashboard/resume-diagnosis.vue          # 主页面
├── components/resume/
│   ├── ResumeDiagnosisForm.vue              # 输入表单组件
│   ├── ResumeDiagnosisResult.vue            # 结果展示组件
│   └── DiagnosisStageProgress.vue           # 进度指示器组件
```

### 后端API接口
```
/api/resume/agent-stage-one                   # 阶段一：诊断分析
/api/resume/agent-stage-two                   # 阶段二：简历生成
```

### 数据流程
1. **阶段一**：用户输入 → 分析SOP → 诊断SOP → 建议SOP → 返回诊断结果
2. **阶段二**：诊断结果 → 简历生成SOP → 返回优化后的简历

## 设计特色

### 🎨 视觉设计
- **紫色主题**：与项目整体设计保持一致
- **渐变背景**：美观的渐变背景设计
- **卡片式布局**：清晰的卡片式内容组织
- **响应式设计**：支持多种屏幕尺寸

### 🔄 交互体验
- **阶段进度指示**：清晰的进度指示器
- **实时状态反馈**：加载状态和错误提示
- **流畅动画**：平滑的过渡动画效果
- **一键操作**：简单易用的操作流程

### 📱 用户体验
- **左右分栏**：输入和结果分离展示
- **状态管理**：完整的状态管理机制
- **错误处理**：友好的错误提示和处理
- **性能优化**：组件化设计，提升性能

## 使用建议

### 最佳实践
1. **详细描述**：提供尽可能详细的简历内容和职位描述
2. **明确目标**：明确目标职位的具体要求
3. **多次尝试**：可以尝试不同的诊断模式
4. **结合建议**：结合AI建议和个人经验进行优化

### 注意事项
- 确保简历内容的准确性和完整性
- 职位描述应该具体和相关
- 建议在标准模式和专业模式之间进行对比
- 生成的简历建议需要人工审核和调整

## 更新日志

### v1.0.0
- ✅ 完成基础诊断功能
- ✅ 实现两阶段分析流程
- ✅ 添加进度指示器
- ✅ 支持复制功能
- ✅ 完善错误处理 