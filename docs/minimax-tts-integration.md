# MiniMax TTS 集成说明

## 概述

新的 `useInterviewerAgentWithMinimax` composable 替换了原有的 `useInterviewerAgent`，集成了 MiniMax TTS 服务，提供更高质量的语音合成功能。

## 主要特性

### 1. 高质量语音合成

- 使用 MiniMax 的先进 TTS 模型
- 支持多种音色和情绪
- 可调节语速、音量、音高等参数

### 2. 智能音频管理

- 自动处理浏览器播放限制
- 音频缓存机制，避免重复请求
- 优雅的错误处理和降级

### 3. 实时设置调整

- 通过设置面板实时调整 TTS 参数
- 设置自动保存到本地存储
- 支持语音效果预览测试

## 配置选项

### TTS 参数

| 参数         | 类型   | 默认值           | 说明                       |
| ------------ | ------ | ---------------- | -------------------------- |
| `model`      | string | 'speech-02-hd'   | TTS 模型，推荐使用高清模型 |
| `voiceId`    | string | 'female-tianmei' | 音色 ID                    |
| `speed`      | number | 1.0              | 语速 (0.5-2.0)             |
| `volume`     | number | 1.0              | 音量 (0.1-2.0)             |
| `pitch`      | number | 0                | 音高 (-12 到 12)           |
| `emotion`    | string | 'happy'          | 情绪类型                   |
| `format`     | string | 'mp3'            | 音频格式                   |
| `sampleRate` | number | 32000            | 采样率                     |
| `bitrate`    | number | 128000           | 比特率                     |

### 可用音色

- `female-tianmei`: 甜美女性
- `female-yujie`: 御姐
- `female-chengshu`: 成熟女性
- `female-shaonv`: 少女
- `male-qn-jingying`: 精英青年
- `male-qn-qingse`: 青涩青年
- `presenter_female`: 女性主持人
- `presenter_male`: 男性主持人

### 可用情绪

- `happy`: 愉快
- `neutral`: 中性
- `sad`: 悲伤
- `angry`: 愤怒
- `surprised`: 惊讶
- `fearful`: 恐惧
- `disgusted`: 厌恶

## 使用方法

### 基本使用

```typescript
const {
  interviewer,
  isLoadingQuestionText,
  isSynthesizingAudio,
  isStreamingText,
  revealedQuestionContent,
  displayQuestionContent,
  handleQuestionCycle,
  getInitialQuestion,
  getNextQuestion,
  setInterviewerName,
  resetInterviewerState,
  isPlaybackBlockedByInteraction,
  triggerManualAudioPlayback,
  updateTTSOptions,
} = useInterviewerAgentWithMinimax({
  interviewerAudioPlayer,
  messages,
  streamingInterviewerMessageInChat,
  messageHistoryRef,
  interviewerInitialName: '面试官',
  ttsOptions: {
    model: 'speech-02-hd',
    voiceId: 'female-tianmei',
    speed: 1.0,
    volume: 1.0,
    emotion: 'happy',
  },
})
```

### 动态更新设置

```typescript
// 更新 TTS 设置
updateTTSOptions({
  voiceId: 'male-qn-jingying',
  speed: 1.2,
  emotion: 'neutral',
})
```

### 手动触发音频播放

```typescript
// 当浏览器阻止自动播放时
if (isPlaybackBlockedByInteraction.value) {
  await triggerManualAudioPlayback()
}
```

## 错误处理

composable 内置了完善的错误处理机制：

1. **网络错误**: 自动重试和降级到文本显示
2. **认证错误**: 提供具体的错误信息
3. **播放限制**: 提供手动播放选项
4. **参数错误**: 验证和纠正无效参数

## 性能优化

1. **音频缓存**: 避免重复的 TTS 请求
2. **流式文本**: 文本显示与音频播放同步
3. **资源清理**: 自动清理音频 URL 和事件监听器
4. **错误降级**: TTS 失败时优雅降级到文本显示

## 注意事项

1. 确保 MiniMax API Key 和 Group ID 已正确配置
2. 浏览器可能会阻止自动播放，需要用户交互后才能播放音频
3. TTS 请求可能需要一定时间，建议显示加载状态
4. 音频文件会占用一定内存，长时间使用时注意内存管理

## 迁移指南

从原有的 `useInterviewerAgent` 迁移到 `useInterviewerAgentWithMinimax`：

1. 更新 import 语句
2. 添加 `ttsOptions` 配置
3. 可选：添加设置面板支持
4. 测试音频播放功能

迁移后，所有原有的 API 保持兼容，只是 TTS 服务从 CosyVoice 切换到了 MiniMax。
