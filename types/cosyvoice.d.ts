// @ts-ignore
import type { Buffer } from 'node:buffer' // NodeJS.Timeout 需要 Buffer

/**
 * 客户端发送给服务端的消息头部结构。
 */
export type ClientMessageHeader = {
  /** 指令类型 */
  action: 'run-task' | 'continue-task' | 'finish-task'
  /** 任务ID, 需确保在同一任务中传递的task_id一致 */
  task_id: string
  /** 固定字符串："duplex" */
  streaming: 'duplex'
}

/**
 * 语音合成参数。
 * 详细说明参见阿里云官方文档。
 */
export type SynthesisParameters = {
  /** 文本类型，固定为 "PlainText" */
  text_type?: 'PlainText'
  /**
   * 指定语音合成所使用的音色。
   * 支持默认音色或通过声音复刻功能定制的专属音色。
   */
  voice: string
  /** 音频编码格式，支持 pcm、wav 和 mp3。 */
  format?: 'pcm' | 'wav' | 'mp3'
  /** 音频采样率，支持 8000, 16000, 22050, 24000, 44100, 48000。 */
  sample_rate?: 8000 | 16000 | 22050 | 24000 | 44100 | 48000
  /** 音量，取值范围：0～100。默认值：50。 */
  volume?: number
  /** 合成音频的语速，取值范围：0.5~2。默认值：1.0。 */
  rate?: number
  /** 合成音频的语调，取值范围：0.5~2。默认值：1.0。 */
  pitch?: number
}

/**
 * 'run-task' 指令的载荷。
 */
export type RunTaskPayload = {
  task_group: 'audio'
  task: 'tts'
  function: 'SpeechSynthesizer'
  model: string // 模型名称, 如 'cosyvoice-v1', 'cosyvoice-v2'
  parameters: SynthesisParameters
  /**
   * 输入文本。
   * 文本长度限制：单次不超过2000字符（汉字算2字符，英文/数字/标点/空格算1字符）。
   * 编码格式：UTF-8。
   */
  input: { text?: string } // text可省略，在continue-task指令中发送
}

/**
 * 'continue-task' 指令的载荷。
 */
export type ContinueTaskPayload = {
  /**
   * 输入文本。
   * 文本长度限制：单次不超过2000字符。
   * 编码格式：UTF-8。
   */
  input: { text: string }
}

/**
 * 'finish-task' 指令的载荷。
 */
export type FinishTaskPayload = {
  input: Record<string, never> // 固定格式: {}
}

/**
 * 客户端发送给服务端的完整消息结构。
 */
export type ClientMessage =
  | { header: ClientMessageHeader & { action: 'run-task' }; payload: RunTaskPayload }
  | { header: ClientMessageHeader & { action: 'continue-task' }; payload: ContinueTaskPayload }
  | { header: ClientMessageHeader & { action: 'finish-task' }; payload: FinishTaskPayload }

// 服务端返回事件相关类型定义
export type TaskStartedPayload = Record<string, never>

export type ResultGeneratedPayload = {
  usage?: {
    characters?: number
  }
}

export type TaskFinishedPayload = {
  output?: {
    sentence?: { words?: any[] } // 详细的句子和词语信息，目前可忽略
  }
  usage?: {
    /** 本次请求中计费的有效字符数 (累加值，以最后一次为准) */
    characters?: number
  }
}

export type TaskFailedPayload = Record<string, never>

/**
 * 'task-started' 事件：任务已成功开启。
 */
export type TaskStartedMessage = {
  header: {
    task_id: string
    event: 'task-started'
    attributes: Record<string, never>
  }
  payload: TaskStartedPayload
}

/**
 * 'result-generated' 事件：服务端持续返回的事件，可忽略。
 */
export type ResultGeneratedMessage = {
  header: {
    task_id: string
    event: 'result-generated'
    attributes: { request_uuid: string } // Request ID，用于问题排查
  }
  payload: ResultGeneratedPayload
}

/**
 * 'task-finished' 事件：任务已结束。
 */
export type TaskFinishedMessage = {
  header: {
    task_id: string
    event: 'task-finished'
    attributes: { request_uuid: string } // Request ID
  }
  payload: TaskFinishedPayload
}

/**
 * 'task-failed' 事件：任务失败。
 */
export type TaskFailedMessage = {
  header: {
    task_id: string
    event: 'task-failed'
    error_code: string // 错误码
    error_message: string // 错误信息
    attributes: Record<string, never>
  }
  payload: TaskFailedPayload
}

/**
 * 服务端返回给客户端的完整消息结构 (JSON格式事件)。
 * 注意：二进制音频流不包含在任何事件中，需单独接收。
 */
export type ServerMessage = TaskStartedMessage | ResultGeneratedMessage | TaskFinishedMessage | TaskFailedMessage

/**
 * 任务上下文，用于跟踪单个语音合成任务的状态和数据。
 */
export type TaskContext = {
  taskId: string
  resolve: (audioBuffer: Buffer) => void
  reject: (error: Error) => void
  audioChunks: Buffer[]
  outputFilePath?: string
  isTaskStarted: boolean // 标记是否已收到 task-started 事件
  modelParameters: { model: string; parameters: SynthesisParameters }
  /** 存储待通过 continue-task 发送的文本块 */
  texts: string[]
  /** task-started 事件的超时计时器 */
  taskStartedTimeoutId?: NodeJS.Timeout
}
