// SenseVoice 语音识别 API 类型定义

/**
 * 提交任务参数
 */
export type SenseVoiceSubmitTaskParams = {
  /** 音频文件公网 URL 列表 */
  fileUrls: string[]
  /** 需要识别的音轨索引，默认 [0] */
  channelId?: number[]
  /** 是否过滤语气词，默认 false */
  disfluencyRemovalEnabled?: boolean
  /** 语言代码，SenseVoice 只支持单一语种，默认 auto */
  languageHints?: string[]
}

/**
 * 提交任务响应
 */
export type SenseVoiceSubmitTaskResponse = {
  /** 任务状态 */
  task_status: string
  /** 任务 ID */
  task_id: string
}

/**
 * 查询任务响应
 */
export type SenseVoiceQueryTaskResponse = {
  /** 任务 ID */
  task_id: string
  /** 任务状态 */
  task_status: string
  /** 子任务状态 */
  subtask_status?: string
  /** 文件 URL */
  file_url?: string
  /** 识别结果下载链接（24小时有效） */
  transcription_url?: string
  /** 识别结果（部分场景直接返回） */
  results?: SenseVoiceTranscriptionResult[]
}

/**
 * 识别结果结构
 */
export type SenseVoiceTranscriptionResult = {
  audio_format: string
  channels: number[]
  original_sampling_rate: number
  original_duration_in_milliseconds: number
  channel_id: number
  content_duration_in_milliseconds: number
  transcript: string
  sentences: SenseVoiceSentence[]
}

/**
 * 句子级别识别结果
 */
export type SenseVoiceSentence = {
  begin_time: number
  end_time: number
  text: string
} 