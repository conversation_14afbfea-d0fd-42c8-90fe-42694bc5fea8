export interface InterviewEvaluation {
  overallScore: number
  feedback?: string
  strengths?: string[]
  weaknesses?: string[]
}

export interface Interview {
  _id: string
  type: InterviewType
  userId: string
  duration: number
  date: string
  evaluation?: InterviewEvaluation
  status: 'pending' | 'completed' | 'cancelled'
  createdAt: string
  updatedAt: string
}

const InterviewTypeEnum = {
  'NEW-MEDIA-OPERATION': 'new-media-operation',
  'PRODUCT-MANAGER': 'product-manager',
  'SALES-SPECIALIST': 'sales-specialist',
  MARKETING: 'marketing',
  'FRONTEND-ENGINEER': 'frontend',
  'BACKEND-ENGINEER': 'backend',
  'FULLSTACK-ENGINEER': 'fullstack',
  'TEST-ENGINEER': 'test-engineer',
  'ALGORITHM-ENGINEER': 'algorithm',
  'UI-DESIGNER': 'ui-designer',
  'PROJECT-MANAGER': 'project-manager',
  'CUSTOMER-SERVICE': 'customer-service',
  'HUMAN-RESOURCE-SPECIALIST': 'human-resource-specialist',
} as const

export type InterviewType = (typeof InterviewTypeEnum)[keyof typeof InterviewTypeEnum]

export type InterviewStage = 'introduction' | 'technical' | 'project' | 'design' | 'ending' | 'terminated'
