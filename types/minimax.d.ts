// 模型类型
export type T2AModel = 'speech-02-hd' | 'speech-02-turbo' | 'speech-01-hd' | 'speech-01-turbo' | 'speech-01-240228' | 'speech-01-turbo-240228'

// 音频格式类型
export type AudioFormat = 'mp3' | 'pcm' | 'flac' | 'wav'

// 采样率类型
export type SampleRate = 8000 | 16000 | 22050 | 24000 | 32000 | 44100

// 比特率类型
export type Bitrate = 32000 | 64000 | 128000 | 256000

// 情绪类型
export type Emotion = 'happy' | 'sad' | 'angry' | 'fearful' | 'disgusted' | 'surprised' | 'neutral'

// 语言增强类型
export type LanguageBoost =
  | 'Chinese'
  | 'Chinese,Yue'
  | 'English'
  | 'Arabic'
  | 'Russian'
  | 'Spanish'
  | 'French'
  | 'Portuguese'
  | 'German'
  | 'Turkish'
  | 'Dutch'
  | 'Ukrainian'
  | 'Vietnamese'
  | 'Indonesian'
  | 'Japanese'
  | 'Italian'
  | 'Korean'
  | 'Thai'
  | 'Polish'
  | 'Romanian'
  | 'Greek'
  | 'Czech'
  | 'Finnish'
  | 'Hindi'
  | 'auto'

// 系统音色类型
export type SystemVoiceId =
  | 'male-qn-qingse' // 青涩青年音色
  | 'male-qn-jingying' // 精英青年音色
  | 'male-qn-badao' // 霸道青年音色
  | 'male-qn-daxuesheng' // 青年大学生音色
  | 'female-shaonv' // 少女音色
  | 'female-yujie' // 御姐音色
  | 'female-chengshu' // 成熟女性音色
  | 'female-tianmei' // 甜美女性音色
  | 'presenter_male' // 男性主持人
  | 'presenter_female' // 女性主持人
  | 'audiobook_male_1' // 男性有声书1
  | 'audiobook_male_2' // 男性有声书2
  | 'audiobook_female_1' // 女性有声书1
  | 'audiobook_female_2' // 女性有声书2
  | 'male-qn-qingse-jingpin' // 青涩青年音色-beta
  | 'male-qn-jingying-jingpin' // 精英青年音色-beta
  | 'male-qn-badao-jingpin' // 霸道青年音色-beta
  | 'male-qn-daxuesheng-jingpin' // 青年大学生音色-beta
  | 'female-shaonv-jingpin' // 少女音色-beta
  | 'female-yujie-jingpin' // 御姐音色-beta
  | 'female-chengshu-jingpin' // 成熟女性音色-beta
  | 'female-tianmei-jingpin' // 甜美女性音色-beta
  | 'clever_boy' // 聪明男童
  | 'cute_boy' // 可爱男童
  | 'lovely_girl' // 萌萌女童
  | 'cartoon_pig' // 卡通猪小琪
  | 'bingjiao_didi' // 病娇弟弟
  | 'junlang_nanyou' // 俊朗男友
  | 'chunzhen_xuedi' // 纯真学弟
  | 'lengdan_xiongzhang' // 冷淡学长
  | 'badao_shaoye' // 霸道少爷
  | 'tianxin_xiaoling' // 甜心小玲
  | 'qiaopi_mengmei' // 俏皮萌妹
  | 'wumei_yujie' // 妩媚御姐
  | 'diadia_xuemei' // 嗲嗲学妹
  | 'danya_xuejie' // 淡雅学姐
  | 'Santa_Claus' // Santa Claus
  | 'Grinch' // Grinch
  | 'Rudolph' // Rudolph
  | 'Arnold' // Arnold
  | 'Charming_Santa' // Charming Santa
  | 'Charming_Lady' // Charming Lady
  | 'Sweet_Girl' // Sweet Girl
  | 'Cute_Elf' // Cute Elf
  | 'Attractive_Girl' // Attractive Girl
  | 'Serene_Woman' // Serene Woman

// 语音设置接口
export interface VoiceSetting {
  voice_id: SystemVoiceId | string // 系统音色或复刻音色
  speed?: number // 范围[0.5,2]，默认值为1.0
  vol?: number // 范围(0,10]，默认值为1.0
  pitch?: number // 范围[-12,12]，默认值为0
  emotion?: Emotion // 情绪类型
  latex_read?: boolean // 是否支持朗读latex公式
  english_normalization?: boolean // 是否支持英语文本规范化
}

// 音频设置接口
export interface AudioSetting {
  sample_rate?: SampleRate // 采样率
  bitrate?: Bitrate // 比特率
  format: AudioFormat // 音频格式
  channel?: 1 | 2 // 声道数，1=单声道，2=双声道
}

// 发音字典接口
export interface PronunciationDict {
  tone?: string[] // 替换发音列表
}

// T2A 请求参数接口
export interface T2ARequest {
  model: T2AModel
  text: string // 长度限制<10000字符
  stream?: boolean // 是否流式输出
  language_boost?: LanguageBoost
  output_format?: 'url' | 'hex'
  voice_setting: VoiceSetting
  audio_setting: AudioSetting
  pronunciation_dict?: PronunciationDict
  subtitle_enable?: boolean // 是否开启字幕服务
}

// T2A 响应接口
export interface T2AResponse {
  data: {
    audio: string
    status: number
  }
  extra_info: {
    audio_length: number
    audio_sample_rate: number
    audio_size: number
    audio_bitrate: number
    word_count: number
    invisible_character_ratio: number
    audio_format: string
    usage_characters: number
  }
  trace_id: string
  base_resp: {
    status_code: number
    status_msg: string
  }
}

// 错误状态码类型
export type ErrorStatusCode =
  | 1000 // 未知错误
  | 1001 // 超时
  | 1002 // 触发限流
  | 1004 // 鉴权失败
  | 1039 // 触发TPM限流
  | 1042 // 非法字符超过10%
  | 2013 // 输入格式信息不正常
