export interface StageInsight {
  stage: string
  stageName: string
  summary: string
  highlights: string[]
  improvements: string[]
  specificAdvice: string[]
}

export interface TechnicalInsights {
  knowledgeGaps: string[]
  practicalSkills: string[]
  problemSolvingApproach: string[]
  technicalCommunication: string[]
  improvements: string[]
}

export interface CommunicationInsights {
  strengths: string[]
  areasForImprovement: string[]
  expressionTips: string[]
  interactionStyle: string[]
}

export interface CareerInsights {
  motivation: string[]
  careerPlanning: string[]
  roleAlignment: string[]
  developmentSuggestions: string[]
}

export interface PracticalAdvice {
  category: string
  categoryName: string
  items: Array<{
    issue: string
    suggestion: string
    examples?: string[]
  }>
}

export interface Evaluation {
  stageInsights: StageInsight[]
  technicalInsights: TechnicalInsights
  communicationInsights: CommunicationInsights
  careerInsights: CareerInsights
  practicalAdvice: PracticalAdvice[]
  overallSummary: string
  keyRecommendations: string[]
  nextSteps: string[]
}
