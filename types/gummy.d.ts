// server/services/asr/gummy.types.ts

/**
 * 客户端发送给服务端的指令头部。
 */
export type ClientCommandHeader = {
  action: 'run-task' | 'finish-task'
  task_id: string
  streaming: 'duplex' // 固定为 "duplex"
}

/**
 * 'run-task' 指令中 parameters 字段的类型。
 */
export type RunTaskParameters = {
  sample_rate: number // 音频采样率, e.g., 16000
  format: string // 音频格式: pcm, wav, mp3, opus, speex, aac, amr
  vocabulary_id?: string // 热词ID
  source_language?: string // 源语言代码, e.g., 'zh', 'en', 'auto' (默认)
  transcription_enabled?: boolean // 是否开启识别, 默认 true
  translation_enabled?: boolean // 是否开启翻译, 默认 false
  translation_target_languages?: string[] // 翻译目标语言代码, e.g., ['en']
}

/**
 * 'run-task' 指令的 payload。
 */
export type RunTaskPayload = {
  model: 'gummy-realtime-v1' // 模型名称，固定
  parameters: RunTaskParameters
  input: {} // 固定为空对象
  task: 'asr' // 固定为 "asr"
  task_group: 'audio' // 固定为 "audio"
  function: 'recognition' // 固定为 "recognition"
}

/**
 * 'run-task' 指令的完整结构。
 */
export type RunTaskCommand = {
  header: ClientCommandHeader & { action: 'run-task' }
  payload: RunTaskPayload
}

/**
 * 'finish-task' 指令的 payload。
 */
export type FinishTaskPayload = {
  input: {} // 固定为空对象
}

/**
 * 'finish-task' 指令的完整结构。
 */
export type FinishTaskCommand = {
  header: ClientCommandHeader & { action: 'finish-task' }
  payload: FinishTaskPayload
}

/**
 * 服务端返回给客户端的事件头部。
 */
export type ServerEventHeader = {
  task_id: string
  event: 'task-started' | 'result-generated' | 'task-finished' | 'task-failed'
  attributes: Record<string, any> // 通常为空
  error_code?: string // 仅 'task-failed' 事件包含
  error_message?: string // 仅 'task-failed' 事件包含
}

/**
 * 'task-started' 事件。
 */
export type TaskStartedEvent = {
  header: ServerEventHeader & { event: 'task-started' }
  payload: {} // payload 为空对象
}

/**
 * 字词时间戳信息。
 */
export type Word = {
  begin_time: number // 字开始时间 (ms)
  end_time: number // 字结束时间 (ms)
  text: string // 字
  punctuation?: string // 标点
  fixed?: boolean // 中间结果内容是否可能变化
}

/**
 * 语义断句发生时，后一句未断句的识别/翻译结果。
 */
export type StashResult = {
  // 文档描述为 "stash: object"，具体结构未详细给出，通常包含未成句的文本片段
  // 根据示例，它可能包含与 TranscriptionResultData/TranslationResultData 类似的字段
  text?: string
  words?: Word[]
  // ... 可能有其他字段
  [key: string]: any // 允许其他属性
}

/**
 * 识别结果数据。
 */
export type TranscriptionResultData = {
  sentence_id: number
  begin_time: number
  end_time: number
  text: string
  words: Word[]
  stash?: StashResult
  sentence_end: boolean
}

/**
 * 翻译结果数据。
 */
export type TranslationResultData = {
  sentence_id: number
  lang: string
  begin_time: number
  end_time: number
  text: string
  words: Word[]
  stash?: StashResult
  sentence_end: boolean
}

/**
 * 'result-generated' 事件中 payload.output 的结构。
 */
export type ResultGeneratedPayloadOutput = {
  transcription?: TranscriptionResultData
  translations?: TranslationResultData[]
}

/**
 * 'result-generated' 事件。
 */
export type ResultGeneratedEvent = {
  header: ServerEventHeader & { event: 'result-generated' }
  payload: {
    output: ResultGeneratedPayloadOutput
  }
}

/**
 * 'task-finished' 事件的 payload。
 */
export type TaskFinishedPayload = {
  output: {} // 为空对象
  usage: null | any // 示例中为 null
}

/**
 * 'task-finished' 事件。
 */
export type TaskFinishedEvent = {
  header: ServerEventHeader & { event: 'task-finished' }
  payload: TaskFinishedPayload
}

/**
 * 'task-failed' 事件。
 */
export type TaskFailedEvent = {
  header: ServerEventHeader & { event: 'task-failed'; error_code: string; error_message: string }
  payload: {} // payload 为空对象
}

/**
 * 所有服务端可能返回的事件类型联合。
 */
export type ServerEvent = TaskStartedEvent | ResultGeneratedEvent | TaskFinishedEvent | TaskFailedEvent

/**
 * GummyService 构造函数配置项。
 */
export type GummyServiceConfig = {
  apiKey: string
  workspace?: string // 可选, 用于 X-DashScope-WorkSpace 请求头
  userAgent?: string // 可选, 用于 user-agent 请求头
  dataInspection?: boolean // 是否启用 X-DashScope-DataInspection, Gummy文档建议 "enable"

  // 事件回调
  onTaskStarted?: (taskId: string) => void
  onResultGenerated?: (result: ResultGeneratedPayloadOutput, taskId: string) => void
  onTaskFinished?: (taskId: string) => void
  onTaskFailed?: (errorCode: string, errorMessage: string, taskId: string) => void

  // 底层 WebSocket 事件回调
  onSocketOpen?: () => void
  onSocketError?: (error: Error) => void
  onSocketClose?: (code: number, reason: string) => void
}
