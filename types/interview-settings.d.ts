// 面试官风格选项
export type InterviewerStyle =
  | 'objective' // 客观冷静
  | 'friendly' // 友好鼓励
  | 'challenging' // 严格挑战
  | 'technical' // 技术深入
  | 'patient' // 耐心指导
  | 'humorous' // 幽默风趣
  | 'creative' // 思维发散
  | 'pressure' // 压力施加

// 面试覆盖范围选项
export type InterviewCoverage =
  | 'comprehensive' // 全面（技能、经验、项目）
  | 'technical_skills' // 专业技能
  | 'behavioral' // 行为问题
  | 'project_experience' // 项目经验
  | 'algorithms' // 算法与数据结构
  | 'system_design' // 系统设计
  | 'teamwork' // 团队协作能力
  | 'leadership' // 领导力与管理
  | 'career_planning' // 职业规划与发展
  | 'salary_negotiation' // 薪资与福利谈判

// 面试语言选项
export type InterviewLanguage = '中文' | 'English'

// 音频源选项
export type AudioSource = 'default' | 'microphone' | 'system'

// 面试设置接口
export interface InterviewSettings {
  duration: number
  interviewerStyle: InterviewerStyle
  coverage: InterviewCoverage
  language: InterviewLanguage
  autoTranscribe: boolean
  audioSource: AudioSource
}

// 选项类型定义
export interface OptionItem {
  value: string
  label: string
}

// 面试设置表单选项
export interface InterviewSettingsFormOptions {
  styleOptions: OptionItem[]
  coverageOptions: OptionItem[]
  languageOptions: OptionItem[]
  audioSourceOptions: OptionItem[]
}
