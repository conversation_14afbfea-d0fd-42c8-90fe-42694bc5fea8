import { dirname, join } from 'path'
import { fileURLToPath } from 'url'
import svgLoader from 'vite-svg-loader'

const currentDir = dirname(fileURLToPath(import.meta.url))

export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: {
    enabled: true,
    editor: 'cursor', // 使用Cursor
  },
  css: ['~/assets/css/loaders.css', '~/assets/css/vars.scss', '~/assets/css/animations.css'],
  alias: {
    '@': currentDir,
    '~': currentDir,
    assets: './assets',
    public: './public',
  },
  vite: {
    plugins: [
      svgLoader({
        defaultImport: 'component',
        svgoConfig: {
          plugins: [
            { name: 'cleanupIds', params: { remove: false, minify: false } },
            (function () {
              const prefixMap = new Map()
              return {
                name: 'prefixIds',
                params: {
                  prefix: (node, info) => {
                    const filePath = info.path
                    if (!prefixMap.has(filePath)) {
                      const randomId = Math.random().toString(36).substring(2, 8)
                      const prefix = `svg-${randomId}-`
                      prefixMap.set(filePath, prefix)
                    }
                    return prefixMap.get(filePath)
                  },
                },
              }
            })(),
          ],
        },
      }),
    ],
    resolve: {
      alias: {
        '@': join(currentDir),
        '~': join(currentDir),
      },
    },
    // 开发环境 允许 host ngrok代理
    // server: {
    //   hmr: {
    //     host: 'localhost',
    //   },
    //   allowedHosts: ['localhost', '460a-36-235-134-121.ngrok-free.app'],
    // },
  },
  modules: ['@nuxtjs/tailwindcss', '@nuxt/image', '@pinia/nuxt', '@sidebase/nuxt-auth', '@nuxtjs/sitemap', '@nuxtjs/robots'],
  site: {
    url: process.env.NUXT_PUBLIC_SITE_URL || 'https://hi-offer.com',
    name: 'Hi-Offer',
    description: 'Hi-offer是一个全方位的职业发展平台，提供职业评估、简历优化、简历翻译、简历帮写、和模拟面试等服务。',
    defaultLocale: 'zh-CN',
  },
  pinia: {
    autoImports: ['defineStore', 'storeToRefs'],
  },
  runtimeConfig: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID,
      clientSecret: process.env.GITHUB_CLIENT_SECRET,
    },
    auth: {
      secret: process.env.AUTH_SECRET,
    },
    public: {
      cryptoKey: process.env.NUXT_CRYPTO_KEY,
      stagewiseToolbarEnabled: process.env.NUXT_PUBLIC_STAGEWISE_TOOLBAR_ENABLED,
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'https://hi-offer.com',
    },
  },
  auth: {
    baseURL: process.env.AUTH_ORIGIN,
    provider: {
      type: 'authjs',
    },
    globalAppMiddleware: false,
    defaultProvider: 'github',
    signInPage: '/login',
  },
  typescript: {
    tsConfig: {
      compilerOptions: {
        moduleResolution: 'bundler',
        allowImportingTsExtensions: true,
        paths: {
          '@heroicons/vue/*': ['./node_modules/@heroicons/vue/*'],
        },
      },
    },
  },
  devServer: {
    host: '0.0.0.0',
    port: 3131,
  },
  app: {
    head: {
      title: 'Hi-Offer',
    },
  },
  build: {
    transpile: ['vue-chartjs', 'chart.js'],
  },
})
