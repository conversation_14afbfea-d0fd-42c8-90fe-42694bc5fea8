version: '3.8'

services:
  web:
    image: peanutsplash/resilo:latest
    # 注入env file
    env_file:
      - .env
    ports:
      - '3131:3131'
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/resilo
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mongodb
      - redis
    restart: unless-stopped

  mongodb:
    image: mongo:latest
    ports:
      - '27017:27017'
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=resilo

  redis:
    image: redis:latest
    ports:
      - '10637:6379'
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

volumes:
  mongodb_data:
  redis_data:
