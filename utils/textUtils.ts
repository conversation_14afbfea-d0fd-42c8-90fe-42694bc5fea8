/**
 * 文本处理工具函数
 */

export type TextSegment = {
  text: string
  isKeyword: boolean
}

/**
 * 高亮文本中的关键词
 * @param text 需要处理的文本
 * @param keywords 关键词数组
 * @returns 分段后的文本数组，每段标记是否为关键词
 */
export const highlightKeywords = (text: string, keywords: string[]): TextSegment[] => {
  if (!text || !keywords || keywords.length === 0) {
    return [{ text, isKeyword: false }]
  }

  const escapedKeywords = keywords.map(k => k.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
  const regex = new RegExp(`(${escapedKeywords.join('|')})`, 'gi')
  const parts: TextSegment[] = []
  let lastIndex = 0
  let match

  while ((match = regex.exec(text)) !== null) {
    if (match.index > lastIndex) {
      parts.push({ text: text.substring(lastIndex, match.index), isKeyword: false })
    }
    parts.push({ text: match[0], isKeyword: true })
    lastIndex = regex.lastIndex
  }

  if (lastIndex < text.length) {
    parts.push({ text: text.substring(lastIndex), isKeyword: false })
  }

  if (parts.length === 0 && text) {
    parts.push({ text: text, isKeyword: false })
  }

  return parts
}

/**
 * 解析包含 `**关键词**` 标记的文本为分段数组
 * @param markedText 带有标记的文本
 * @returns 分段后的文本数组，每段标记是否为关键词
 */
export const parseMarkedTextToSegments = (markedText: string | undefined | null): TextSegment[] => {
  if (!markedText) return [{ text: '', isKeyword: false }]

  const segments: TextSegment[] = []
  let lastIndex = 0
  const regex = /\*\*(.*?)\*\*/g // 匹配 **keyword**
  let match

  while ((match = regex.exec(markedText)) !== null) {
    // 添加标记前的文本部分
    if (match.index > lastIndex) {
      segments.push({ text: markedText.substring(lastIndex, match.index), isKeyword: false })
    }
    // 添加标记的关键词部分 (match[1] 是捕获组的内容)
    segments.push({ text: match[1], isKeyword: true })
    lastIndex = regex.lastIndex
  }

  // 添加最后一个标记后的剩余文本部分
  if (lastIndex < markedText.length) {
    segments.push({ text: markedText.substring(lastIndex), isKeyword: false })
  }

  // 如果没有解析出任何片段 (例如，文本为空或没有标记)，
  // 返回一个包含原始文本 (或空字符串) 的非关键词片段，以避免渲染错误。
  return segments.length > 0 ? segments : [{ text: markedText || '', isKeyword: false }]
}

/**
 * 移除文本中的 ** ** 标记
 * @param text 带有标记的文本
 * @returns 移除标记后的纯文本
 */
export const stripMarkers = (text: string | undefined | null): string => {
  if (!text) return ''
  return text.replace(/\*\*(.*?)\*\*/g, '$1')
}
