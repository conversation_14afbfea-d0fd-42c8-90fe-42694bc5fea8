/**
 * 时间处理工具函数
 */

/**
 * 等待指定毫秒数
 * @param ms 毫秒数
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 格式化时间为 12 小时制的时间字符串
 * @param date 日期对象
 * @returns 格式化后的时间字符串，如 "10:30 AM"
 */
export const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })
}

/**
 * 格式化秒数为分:秒格式
 * @param seconds 秒数
 * @returns 格式化后的时间字符串，如 "1:30"
 */
export const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`
}
