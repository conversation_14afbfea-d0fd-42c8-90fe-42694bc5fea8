export const ErrorCode = {
  // 4xx 客户端错误
  BAD_REQUEST: {
    code: 400,
    message: 'Bad Request',
    displayMessage: '请求错误',
  },
  UNAUTHORIZED: {
    code: 401,
    message: 'Unauthorized',
    displayMessage: '请先登录',
  },
  FORBIDDEN: {
    code: 403,
    message: 'Forbidden',
    displayMessage: '无权限',
  },
  NOT_FOUND: {
    code: 404,
    message: 'Not Found',
    displayMessage: '资源不存在',
  },
  CONFLICT: {
    code: 409,
    message: 'Conflict',
    displayMessage: '资源冲突',
  },
  UNPROCESSABLE_ENTITY: {
    code: 422,
    message: 'Unprocessable Entity',
    displayMessage: '请求数据验证失败',
  },
  TOO_MANY_REQUESTS: {
    code: 429,
    message: 'Too Many Requests',
    displayMessage: '请求过多，请稍后再试',
  },

  // 5xx 服务器错误
  INTERNAL_SERVER_ERROR: {
    code: 500,
    message: 'Internal Server Error',
    displayMessage: '服务器内部错误',
  },
  BAD_GATEWAY: {
    code: 502,
    message: 'Bad Gateway',
    displayMessage: '网关错误',
  },
  SERVICE_UNAVAILABLE: {
    code: 503,
    message: 'Service Unavailable',
    displayMessage: '服务不可用',
  },
  GATEWAY_TIMEOUT: {
    code: 504,
    message: 'Gateway Timeout',
    displayMessage: '网关超时',
  },

  //1000-1099: 用户相关错误
  USER_NOT_FOUND: {
    code: 1001,
    message: 'User Not Found',
    displayMessage: '用户不存在',
  },
  USER_EMAIL_EXISTS: {
    code: 1002,
    message: 'Email already registered',
    displayMessage: '邮箱已注册',
  },
  USER_GITHUB_LOGIN_REQUIRED: {
    code: 1003,
    message: 'This email is registered via GitHub, please login with GitHub',
    displayMessage: '该邮箱已通过GitHub注册，请使用GitHub登录',
  },
  USER_INVALID_PASSWORD: {
    code: 1004,
    message: 'Invalid password',
    displayMessage: '密码错误',
  },
  USER_INSUFFICIENT_BALANCE: {
    code: 1005,
    message: 'Insufficient balance',
    displayMessage: '余额不足',
  },

  // 2000-2099: 面试评估相关错误
  EVALUATION_GENERATION_FAILED: {
    code: 2001,
    message: 'Failed to generate interview evaluation',
    displayMessage: '面试评估生成失败',
  },
  EVALUATION_INVALID_TYPE: {
    code: 2002,
    message: 'Invalid evaluation type',
    displayMessage: '无效的评估类型',
  },
  EVALUATION_EMPTY_MESSAGES: {
    code: 2003,
    message: 'Empty interview messages',
    displayMessage: '面试对话内容不能为空',
  },

  // 3000-3099: AI 相关错误
  AI_RESPONSE_ERROR: {
    code: 3001,
    message: 'AI Response Error',
    displayMessage: 'AI 响应失败',
  },
  AI_MODEL_CALL_FAILED: {
    code: 3002,
    message: 'AI Model Call Failed',
    displayMessage: 'AI 模型调用失败',
  },
} as const

export type ErrorCodeType = (typeof ErrorCode)[keyof typeof ErrorCode]
