import CryptoJS from 'crypto-js'

export const createCrypto = (secretKey: string) => {
  if (!secretKey || typeof secretKey !== 'string' || secretKey.length < 1) {
    throw new Error('Invalid crypto key')
  }

  // 确保密钥长度为32字节
  const normalizedKey = CryptoJS.SHA256(secretKey).toString().slice(0, 32)

  const encrypt = (text: string): string => {
    try {
      return CryptoJS.AES.encrypt(text, normalizedKey).toString()
    } catch (error) {
      console.error('Encryption error:', error)
      throw new Error('加密失败')
    }
  }

  const decrypt = (ciphertext: string): string => {
    try {
      const bytes = CryptoJS.AES.decrypt(ciphertext, normalizedKey)
      return bytes.toString(CryptoJS.enc.Utf8)
    } catch (error) {
      console.error('Decryption error:', error)
      throw new Error('解密失败')
    }
  }

  return {
    encrypt,
    decrypt
  }
}
