import { z } from 'zod'

const envSchema = z.object({
  // MongoDB
  MONGODB_URI: z.string().url(),

  // Redis
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.coerce.number().default(6379),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_DB: z.coerce.number().default(0),
  REDIS_KEY_PREFIX: z.string().default('resilo:'),

  // OpenAI
  OPENAI_API_KEY: z.string().min(1),
  OPENAI_BASE_URL: z.string().url().default('https://api.openai.com/v1'),

  // Deepseek
  DEEPSEEK_API_KEY: z.string().min(1),
  DEEPSEEK_BASE_URL: z.string().url().default('https://api.deepseek.com/v1'),

  // Moonshot
  MOONSOT_API_KEY: z.string().min(1),
  MOONSOT_BASE_URL: z.string().url().default('https://api.moonshot.cn/v1'),

  // Xunfei ASR (语音识别)
  XUNFEI_ASR_APP_ID: z.string().min(1),
  XUNFEI_ASR_API_KEY: z.string().min(1),
  XUNFEI_ASR_API_SECRET: z.string().min(1),

  // Xunfei TTS (语音合成)
  XUNFEI_TTS_APP_ID: z.string().min(1),
  XUNFEI_TTS_API_KEY: z.string().min(1),
  XUNFEI_TTS_API_SECRET: z.string().min(1),
  DEFAULT_XUNFEI_TTS_VCN: z.string().default('xiaoyan'),

  //aliyun
  ALIYUN_API_KEY: z.string().min(1),

  //chat model
  DEFAULT_CHAT_MODEL: z.string().default('deepseek-v3'),

  // zpay
  ZPAY_PID: z.string().min(1),
  ZPAY_SECRET_KEY: z.string().min(1),
  ZPAY_NOTIFY_URL: z.string().min(1),

  // App
  NODE_ENV: z.enum(['development', 'production', 'staging']).default('development'),

  // MiniMax
  MINIMAX_API_KEY: z.string().min(1),
  MINIMAX_GROUP_ID: z.string().min(1),
  MINIMAX_TTS_MODEL: z.string().default('speech-02-hd'),
  MINIMAX_TTS_VOICE_ID: z.string().default('male-qn-qingse'),
})

export type Env = z.infer<typeof envSchema>

export const validateEnv = (): Env => {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    if (error instanceof z.ZodError) {
      const { fieldErrors } = error.flatten()
      const errorMessage = Object.entries(fieldErrors)
        .map(([field, errors]) => `${field}: ${errors?.join(', ')}`)
        .join('\n')
      throw new Error(`ENV validation failed:\n${errorMessage}`)
    }
    throw error
  }
}

export const env = validateEnv()
