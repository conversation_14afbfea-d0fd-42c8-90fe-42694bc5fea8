import EventEmitter from 'events'
import { Readable } from 'stream'
export interface FunctionParams {
    type: 'function'
    name?: string
    args?: any
    id?: string
}

export interface TextParams {
    type: 'text'
    text: string
    id: string
}


export type TextContent = {
    type: 'text'
    text: string
}

export type CommonMessage = TextContent
type ModelType = string

export class CommonCompletionStream extends EventEmitter {
    private rowFormatter: (params: TextParams | FunctionParams) => string = params => (params.type === 'text' ? params.text : JSON.stringify(params))
    private error: any = undefined
    private isEnd = false
    constructor(public readonly options: { model: ModelType; inputMessages: CommonMessage[]; }) {
      super()
      this.on('error', () => {}) // prevent unhandled error throwing
    }
    usages: { promptTokens: number; completionTokens: number; promptTokensInOpenAI: number; completionTokensInOpenAI: number }[] = []
    messageBuffer: (TextParams | FunctionParams)[] = []
    setRowFormatter(formatter: (params: TextParams | FunctionParams) => string) {
      this.rowFormatter = formatter
    }
    emitUsage(usage: { promptTokens: number; completionTokens: number; promptTokensInOpenAI: number; completionTokensInOpenAI: number }) {
      this.usages.push(usage)
      this.emit('usage', usage)
    }
    emitMessagePart(params: TextParams | FunctionParams) {
      this.messageBuffer.push(params)
      this.emit('message', params)
    }
    emitError(error: Error) {
      this.error = error
      this.emit('error', error)
      this.end()
    }
    end() {
      this.isEnd = true
      this.emit('end')
    }
    onMessagePart(callback: (params: TextParams | FunctionParams) => void, options: { once?: boolean } = {}) {
      let called = false
      for (const item of this.messageBuffer) {
        if (options.once && called) return
        callback(item)
        called = true
      }
      if (options.once && !called) {
        this.once('message', callback)
      } else {
        this.on('message', callback)
      }
    }
    onUsage(callback: (usage: { promptTokens: number; completionTokens: number }) => void) {
      this.on('usage', callback)
    }
    onEnd(callback: () => void) {
      this.on('end', callback)
      if (this.isEnd) {
        callback()
        return
      }
    }
    onError(callback: (error: Error) => void) {
      this.on('error', callback)
    }
    toReadableStream(rowFormatter?: (params: TextParams | FunctionParams) => string) {
      const formatter = rowFormatter ?? this.rowFormatter
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const self = this
      const stream = new Readable({
        read(_size) {
          if (self.error) {
            stream.destroy(self.error)
            return
          }
        },
      })
      this.onMessagePart(params => {
        if (this.error) {
          stream.destroy(this.error)
          return
        }
        stream.push(formatter(params))
      })
      this.onEnd(() => {
        stream.push(null)
      })
      this.onError(err => {
        stream.destroy(err)
      })
      return stream
    }
    transformToString(rowFormatter?: (params: TextParams | FunctionParams) => string) {
      return new Promise<string>((resolve, reject) => {
        let text = ''
        this.toReadableStream(rowFormatter)
          .on('data', chunk => {
            text += chunk
          })
          .on('close', () => {
            resolve(text)
          })
          .on('error', reject)
      })
    }
    protected _getTotalUsageInfoSync() {
      if (this.error) throw new Error('Can not get usage info from a errored response')
      if (this.usages.length) {
        const usage = this.usages.reduce(
          (acc, cur) => {
            acc.promptTokens += cur.promptTokens
            acc.completionTokens += cur.completionTokens
            acc.promptTokensInOpenAI += cur.promptTokensInOpenAI
            acc.completionTokensInOpenAI += cur.completionTokensInOpenAI
            return acc
          },
          { promptTokens: 0, completionTokens: 0, promptTokensInOpenAI: 0, completionTokensInOpenAI: 0 },
        )
        return usage
      } 
    }
    async getTotalUsageInfo() {
      if (this.isEnd) {
        return this._getTotalUsageInfoSync()
      } else {
        return new Promise<Awaited<ReturnType<typeof this._getTotalUsageInfoSync>>>(resolve => {
          this.onEnd(() => resolve(this._getTotalUsageInfoSync()))
        })
      }
    }
    async *[Symbol.asyncIterator]() {
      const iterator = this.toReadableStream()
      for await (const chunk of iterator) {
        yield chunk
      }
    }
  }