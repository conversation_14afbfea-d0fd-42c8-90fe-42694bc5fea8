import { eventBus } from '@/composables/useEventBus'
import type { ApiResponse } from '~/types'
import { handleApiError } from '~/utils/error'

interface FetchOptions extends Omit<RequestInit, 'method' | 'body'> {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS'
  body?: any
  showError?: boolean // 是否显示错误提示
  customErrorMessage?: string // 自定义错误信息
}

export async function apiFetch<T = any>(url: string, options: FetchOptions = {}): Promise<T> {
  const { showError = true, customErrorMessage, body, ...fetchOptions } = options

  const finalOptions = {
    ...fetchOptions,
    headers: {
      'Content-Type': 'application/json',
      ...fetchOptions.headers,
    },
    body: body ? JSON.stringify(body) : undefined,
  }

  try {
    const response = await $fetch<ApiResponse<T>>(url, finalOptions)

    if (!response.success) {
      throw { data: response }
    }

    return response.data as T
  } catch (error: any) {
    const errorMessage = handleApiError(error)

    if (showError) {
      eventBus.emit('showToast', {
        message: customErrorMessage || errorMessage,
        type: 'error',
      })
    }

    throw error
  }
}
