import { z } from 'zod'

export const updateUserSettingsSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }).max(50, { message: 'Name cannot exceed 50 characters' }),
  bio: z.string().max(200, { message: 'Bio cannot exceed 200 characters' }).optional(),
  image: z.string().url().optional(),
})

export type UpdateUserSettingsSchema = z.infer<typeof updateUserSettingsSchema>
