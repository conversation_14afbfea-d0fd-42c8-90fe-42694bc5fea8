import { z } from 'zod'

const scoreSchema = z.number().min(0).max(100)

const evaluationSchema = z.object({
  stageEvaluations: z.array(
    z.object({
      stage: z.string(),
      score: scoreSchema,
      strengths: z.array(z.string()),
      weaknesses: z.array(z.string()),
      summary: z.string(),
    }),
  ),
  technicalDimension: z.object({
    basicKnowledge: scoreSchema,
    practicalAbility: scoreSchema,
    problemSolving: scoreSchema,
    technicalVision: scoreSchema,
    comments: z.string(),
  }),
  softSkillDimension: z.object({
    communication: scoreSchema,
    learning: scoreSchema,
    teamwork: scoreSchema,
    pressure: scoreSchema,
    comments: z.string(),
  }),
  careerDimension: z.object({
    careerPlanning: scoreSchema,
    growthPotential: scoreSchema,
    jobMatch: scoreSchema,
    comments: z.string(),
  }),
  overallScore: scoreSchema,
  overallSummary: z.string(),
  suggestions: z.array(z.string()),
})

export const createInterviewSchema = z.object({
  type: z.enum(['frontend', 'backend', 'fullstack', 'algorithm'] as const),
  interviewer: z.string().min(1),
  duration: z.number().min(0),
  date: z.string().datetime(),
  messages: z.array(
    z.object({
      role: z.string(),
      content: z.string(),
      timestamp: z.string().datetime(),
    }),
  ),
  evaluation: evaluationSchema,
})

export type CreateInterviewDTO = z.infer<typeof createInterviewSchema>

export const getInterviewDetailSchema = z.object({
  id: z.string({
    required_error: '面试记录ID不能为空',
    invalid_type_error: '面试记录ID必须是字符串',
  }),
})

export const getInterviewListSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
})

export type GetInterviewListQuery = z.infer<typeof getInterviewListSchema>
