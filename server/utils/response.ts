import { z } from 'zod'

interface Response<T> {
  success: boolean
  error: boolean
  errorCode: number
  errorMessage: string
  data: T
}

export function ResponseSchema(dataSchema: z.ZodType) {
  return z.object({
    data: dataSchema,
    errorCode: z.number(),
    errorMessage: z.string(),
  })
}

export function ErrRespSchema(errCode?: number) {
  return z.object({
    data: z.object({}),
    errorCode: errCode ? z.literal(errCode) : z.number(),
    errorMessage: z.string(),
  })
}

export function respSuccess<T>(data: T): Response<T> {
  return {
    success: true,
    error: false,
    errorCode: 0,
    errorMessage: '',
    data,
  }
}

export function respError(code: number, message: string, data: Record<string, any> = {}): Response<{}> {
  return {
    success: false,
    error: true,
    errorCode: code,
    errorMessage: message,
    data: data,
  }
}
