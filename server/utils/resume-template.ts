export const markdownTemplate = `
## 个人信息

- 张秀儿/男/1996/湖北/英语6级 | 手机：11164201049 , 邮箱：<EMAIL>
- 博客：https://javaguide.cn/>，Github：<https://github.com/Snailclimb> 
- 求职意向：Java后端开发 | 期望城市：上海/苏州/杭州

## 教育经历

<div style="display: flex; justify-content: space-between;">
  <div style="font-size: 15px;"><strong>北京理工大学（2019.09 - 2022.01）</strong></div>
  <div>GPA 3.7/4.0 (TOP 5%)</div>
</div>

<div style="display: flex; justify-content: space-between;">
  <div style="font-size: 15px;"><strong>湖南大学（2015.09 - 2019.06）</strong></div>
  <div>GPA 3.8/4.0 (TOP 10%)</div>
</div>

## 技能清单

下面是一些比较基本的 Java 后端开发技能（根据你自身的情况调整，切勿完全照搬）：

- **计算机基础** ：熟练掌握计算机网络、数据结构和算法、操作系统
- **Java**：熟悉 Java 语言，具备 JVM 调优和问题排查经验
- **开发工具** ：熟练使用 Maven/Gradle、Git、IDEA 、Docker 等开发工具，有 Linux 开发和部署经验
- **数据库**：熟练掌握 MySQL、Redis、Elasticsearch使用及常见优化手段
- **框架** ：熟练掌握 Spring、Spring MVC、SpringBoot、MyBatis 等开发框架，
- **分布式** ：熟练掌握分布式相关理论（如 CAP、Raft）以及解决方案（如分布式ID、分布式事务），熟练使用Spring Cloud Alibaba 全家桶（如 Dubbo、Nacos、Sentinel）
- **前端**：熟练掌握 HTML、CSS、Javascript、React、Vue 等前端技术，前后端分离架构开发经验丰富

还有一些工作招聘有一些特殊的要求比如：

- **Devops** : 熟练掌握 Jenkins，搭建过持续集成环境。
- **云原生** ：熟练掌握 Kubernetes 以及周边生态/ServiceMesh

## 工作经历/实习经历

<div style="display: flex; justify-content: space-between;">
  <div style="font-size: 15px;"><strong>快手（2018 年 6 月 ~ 2020 年 9 月 ）</strong></div>
  <div>Java 后端开发工程师</div>
</div>
1. 负责快手APP的评论、弹幕，搜索业务的功能迭代与稳定性建设。
2. 负责解决部门技术债，包括统一编码规范、提高测试覆盖率、重构支付业务等工作。
3. 主讲了10场技术Session，公司内部平台输出了23篇技术博文。



## 项目经历 

<div style="display: flex; justify-content: space-between;">
  <div style="font-size: 15px;"><strong>项目名称（2017 年 6 月 ~ 2017 年 9 月 ）</strong></div>
  <div>Java 后端开发工程师</div>
</div>

**项目描述**：简单描述项目是做什么的，尽量压缩在三行之内。

**技术栈** ：用了什么技术（如 Spring Boot + MySQL + Redis + Mybatis-plus + Spring Security + Oauth2）

**工作内容/个人职责**：分条描述，看着会清晰一些。一定要尽量结合具体的业务场景去写，不要只是单纯介绍技术是干什么的。介绍要具体（技术+场景+效果），避免模糊描述，也要注意精简语言（避免堆砌技术词，省略不必要的描述），一条工作描述尽量不要超过两行，对于大的功能模块可适当分多个子条。另外，还应该要避免过于书面化的介绍。



<div style="display: flex; justify-content: space-between;">
  <div style="font-size: 15px;"><strong>快鸭云盘（2017 年 6 月 ~ 2017 年 9 月 ）</strong></div>
  <div>Java 后端开发工程师</div>
</div>

**项目描述** : 一款面向个人和团队的在线云存储系统，稳定可靠的储存任何文件，支持文件分享，精细权限设置，性能优秀。

**技术栈** ：Spring Boot +  Mybatis-plus + Spring Security + MySQL + Redis + Elasticsearch + MinIO

**工作内容/个人职责** : 

1. 基于 SpringSecurity + JWT 实现了登录认证，实现了同端互斥登录和强制下线功能。
2. 权限模型使用业界主流的 RBAC，引入了 VIP 用户机制，VIP 用户上传下载速度不受限制。
3. 开发文件上传模块，基于 MinIO 实现了文件的分片上传、断点续传以及极速秒传功能。
4. 基于 CompletableFuture+ 线程池实现大文件的多线程下载，可自定义线程数。相同带宽下测试，多线程下载速度可达单线程下载的 5 倍。
5. ......

参考资料：[网盘项目通用的介绍模板、优化思路以及面试知识点考察分析](https://mp.weixin.qq.com/s/OIDl6ABLnBRvSSHngRvh5A)

## 荣誉奖项（可选）

如果你有含金量比较高的竞赛（比如ACM、阿里的天池大赛）的获奖经历的话，荣誉奖项这块内容一定要写一下！并且，你还可以将荣誉奖项这块内容适当往前放，放在一个更加显眼的位置。

## 开源项目（可选）

如果有觉得比较有价值的个人或者自己参与过开源项目的话，可以放在这里。

## 校园经历（可选）

如果有比较亮眼的校园经历的话就简单写一下，没有就不写！

## 个人评价

我是 XXX

<p style="text-align: right;">感谢您花时间阅读我的简历，期待能与您共事！</p>

<div style="page-break-after: always;"></div>`