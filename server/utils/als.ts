import { AsyncLocalStorage, createHook } from 'node:async_hooks'

interface RequestSession {
  userId?: string
  requestIp?: string
  transactionId?: string
  path?: string
  requestId: string
  userAgent?: string
  scopes: Scope[]
}

interface Scope {
  id: string
  name: string
}

export const requestHandlerSession = new AsyncLocalStorage<RequestSession>()
export const setAsyncLocalStore = (sess: Partial<RequestSession>) => {
  const session = requestHandlerSession.getStore()
  if (session) {
    Object.assign(session, sess)
  }
}
