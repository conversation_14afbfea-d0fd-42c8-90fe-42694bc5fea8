import { getServerSession } from '#auth'
import { defineEvent<PERSON>andler, EventHandlerResponse, getQuery, getRequestHeader, getRequestIP, getResponseHeader, H3Event, readBody, setResponseStatus } from 'h3'
import type { Session } from 'next-auth'
import { z } from 'zod'
import { InsufficientBalanceError } from '~/utils/error'
import { ErrorCode } from '~/utils/error-code'
import { requestHandlerSession } from './als'
type HandlerOptions<Q, B> = {
  auth?: boolean
  bodyRules?: B
  queryRules?: Q
}
const HANDLER_DURATION_WARNING_LIMIT = 5000

type QueryValue = string | number | undefined | null | boolean | Array<QueryValue> | Record<string, any>
type QueryObject = Record<string, QueryValue | QueryValue[]>

interface EventHandlerRequest {
  body?: any
  query?: QueryObject
  routerParams?: Record<string, string>
}

// 修改事件处理器类型，使用 z.infer 推断 schema 类型
interface EventHandler<Q extends z.ZodTypeAny | undefined = undefined, B extends z.ZodTypeAny | undefined = undefined> {
  (
    event: H3Event,
    params: {
      session: Session | null
      query: Q extends z.ZodTypeAny ? z.infer<Q> : any
      body: B extends z.ZodTypeAny ? z.infer<B> : any
    },
  ): EventHandlerResponse
}

// 更新函数重载
export function defineRouteHandler(handler: EventHandler): ReturnType<typeof defineEventHandler>
export function defineRouteHandler<B extends z.ZodTypeAny>(
  options: HandlerOptions<undefined, B>,
  handler: EventHandler<undefined, B>,
): ReturnType<typeof defineEventHandler>
export function defineRouteHandler<Q extends z.ZodTypeAny>(
  options: HandlerOptions<Q, undefined>,
  handler: EventHandler<Q, undefined>,
): ReturnType<typeof defineEventHandler>
export function defineRouteHandler<Q extends z.ZodTypeAny, B extends z.ZodTypeAny>(
  options: HandlerOptions<Q, B>,
  handler: EventHandler<Q, B>,
): ReturnType<typeof defineEventHandler>
export function defineRouteHandler<Q extends z.ZodTypeAny | undefined = undefined, B extends z.ZodTypeAny | undefined = undefined>(
  handlerOrOptions: EventHandler<Q, B> | HandlerOptions<Q, B>,
  handlerFn?: EventHandler<Q, B>,
) {
  const isHandlerOnly = typeof handlerOrOptions === 'function'
  const handler = isHandlerOnly ? (handlerOrOptions as EventHandler<any, any>) : handlerFn!

  if (!handler || typeof handler !== 'function') {
    throw new Error(`Handler must be a function, got ${typeof handler}`)
  }

  const options = isHandlerOnly ? ({} as HandlerOptions<Q, B>) : (handlerOrOptions as HandlerOptions<Q, B>)

  return defineEventHandler(async event => {
    try {
      let session: Session | null = null
      session = await getServerSession(event)
      if (options.auth && !session?.user) {
        return respError(ErrorCode.UNAUTHORIZED.code, ErrorCode.UNAUTHORIZED.message)
      }
      let body: any = undefined
      let query: any = undefined
      if (options.bodyRules) {
        body = await readBody(event)
        try {
          const result = options.bodyRules.safeParse(body)
          if (!result.success) {
            const errorMessage = result.error.message
            logger.error('body validation error:', errorMessage)
            setResponseStatus(event, 400)
            return respError(ErrorCode.BAD_REQUEST.code, errorMessage)
          }
          body = result.data
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Bad request'
          logger.error('body validation error:', errorMessage)
          setResponseStatus(event, 400)
          return respError(ErrorCode.BAD_REQUEST.code, errorMessage)
        }
      }
      if (options.queryRules) {
        query = await getQuery(event)
        try {
          const result = options.queryRules.safeParse(query)
          if (!result.success) {
            const errorMessage = result.error.message
            logger.error('query validation error:', errorMessage)
            setResponseStatus(event, 400)
            return respError(ErrorCode.BAD_REQUEST.code, errorMessage)
          }
          query = result.data
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Bad request'
          logger.error('query validation error:', errorMessage)
          setResponseStatus(event, 400)
          return respError(ErrorCode.BAD_REQUEST.code, errorMessage)
        }
      }
      const requestId = getResponseHeader(event, 'x-request-id') // x-request-id is set on RESPONSE header
      const transactionId = getRequestHeader(event, 'x-transaction-id')
      const userAgent = getRequestHeader(event, 'user-agent')
      const requestIp = getRequestIP(event, { xForwardedFor: true })
      const userId = session?.user?.id
      const params = {
        session: session,
        body,
        query,
      }

      const requestSessionData = {
        userId: userId?.toString() ?? '',
        requestId: typeof requestId === 'string' ? requestId : '',
        requestIp: typeof requestIp === 'string' ? requestIp : '',
        transactionId: typeof transactionId === 'string' ? transactionId : '',
        scopes: [] as any[],
        path: event.path,
        userAgent: userAgent ? userAgent.slice(0, 150) : '',
      }

      const response = await requestHandlerSession.run(requestSessionData, async () => {
        const now = Date.now()
        let processTime = 0
        event.node.res.once('pipe', () => {
          processTime = Date.now() - now
        })
        const response = await handler!(event, params)
        processTime = processTime || Date.now() - now
        if (processTime > HANDLER_DURATION_WARNING_LIMIT) {
          logger.warn('process too long in route:[%s] %dms', event.path, processTime)
        }
        return response
      })
      return response
    } catch (error: any) {
      if (error instanceof InsufficientBalanceError) {
        logger.warn(`Credit insufficient for user: ${error.message}`)
        const errorCode = ErrorCode.USER_INSUFFICIENT_BALANCE
        setResponseStatus(event, 402) // Payment Required
        return respError(errorCode.code, errorCode.displayMessage, {})
      }
      if (error.statusCode) {
        return respError(error.statusCode, error.message)
      }
      logger.error('Route handler error:', error)
      return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, ErrorCode.INTERNAL_SERVER_ERROR.displayMessage)
    }
  })
}
