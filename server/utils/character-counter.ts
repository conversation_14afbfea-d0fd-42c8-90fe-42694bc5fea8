/**
 * 计算文本的字符数
 * 规则：
 * - 汉字: 2个字符
 * - 其他字符（英文字母、希腊字母、标点符号、特殊符号、空格、回车等）: 1个字符
 */
const chineseRegex = /[\u4e00-\u9fa5]/;

export const calculateCharacterCount = (text: string): number => {
  if (!text) return 0

  let count = 0
  for (const char of text) {
    // 使用正则判断是否为汉字
    if (chineseRegex.test(char)) {
      count += 2
    } else {
      // 其他所有字符（包括英文字母、标点符号、空格、回车等）都算1个字符
      count += 1
    }
  }

  return count
}

/**
 * 计算文本的字符数（包含详细统计）
 */
const englishRegex = /[a-zA-Z]/;
const spaceRegex = /\s/;

export const calculateCharacterCountWithDetails = (
  text: string,
): {
  total: number
  chinese: number
  english: number
  punctuation: number
  space: number
} => {
  if (!text) {
    return {
      total: 0,
      chinese: 0,
      english: 0,
      punctuation: 0,
      space: 0,
    }
  }

  let chinese = 0
  let english = 0
  let punctuation = 0
  let space = 0

  for (const char of text) {
    if (chineseRegex.test(char)) {
      chinese += 2
    } else if (englishRegex.test(char)) {
      english += 1
    } else if (spaceRegex.test(char)) {
      space += 1
    } else {
      punctuation += 1
    }
  }

  return {
    total: chinese + english + punctuation + space,
    chinese,
    english,
    punctuation,
    space,
  }
}
