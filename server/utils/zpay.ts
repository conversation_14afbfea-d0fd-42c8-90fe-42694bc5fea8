import { createHash } from 'crypto'

// 定义支付查询响应的类型
export type ZPayQueryResponse = {
  code: number
  msg: string
  status: string
  name: string
  money: string
  out_trade_no: string
  trade_no: string
  type: string
  param: string
  addtime: string
  endtime: string
  pid: string
  buyer: string
}

// 定义PlanKey类型
export type PlanKey = 'free' | 'basic-credit' | 'pro-credit' | 'max-credit'

// 定义param字段解析后的类型
export type ZPayParamData = {
  planKey: PlanKey
  userId: string
  [key: string]: string
}

export const generateOutTradeNo = (uid: string) => {
  // use timestamp prefix + random string + uid suffix
  const timestamp = Date.now().toString().slice(-8)
  const random = Math.random().toString(36).slice(2, 6)
  const uidSuffix = uid.slice(-4)
  return `${timestamp}${random}${uidSuffix}`.toUpperCase()
}

// 实现参数排序和拼接功能
export const getVerifyParams = (params: Record<string, any>) => {
  const sortedParams: [string, string][] = []
  // 过滤掉sign、sign_type和空值参数
  for (const key in params) {
    if (!params[key] || key === 'sign' || key === 'sign_type') {
      continue
    }
    sortedParams.push([key, params[key].toString()])
  }
  // 按照参数名ASCII码从小到大排序
  sortedParams.sort()
  // 拼接成URL键值对的格式
  return sortedParams.map(([key, value]) => `${key}=${value}`).join('&')
}

// 生成MD5签名
export const generateSign = (paramsStr: string, key: string) => {
  return createHash('md5')
    .update(paramsStr + key)
    .digest('hex')
}

/**
 * 解析嵌套的JSON字符串
 * @param response 支付查询响应对象
 * @returns 解析后的响应对象，包含解析后的param字段
 */
export const parseZPayResponse = (
  response: unknown,
): {
  response: ZPayQueryResponse
  parsedParam: ZPayParamData | null
} => {
  // 确保response是对象类型
  if (typeof response !== 'object' || response === null) {
    throw new Error('Invalid response format')
  }
  // 将response转换为ZPayQueryResponse类型
  const typedResponse = response as ZPayQueryResponse
  // 解析param字段
  let parsedParam: ZPayParamData | null = null
  if (typedResponse.param) {
    try {
      const rawParam = JSON.parse(typedResponse.param)
      // 验证planKey是否为有效的PlanKey类型
      if (isValidPlanKey(rawParam.planKey)) {
        parsedParam = rawParam as ZPayParamData
      } else {
        logger.error('Invalid planKey:', rawParam.planKey)
      }
    } catch (error) {
      logger.error('Failed to parse param field:', error)
    }
  }

  return {
    response: typedResponse,
    parsedParam,
  }
}

/**
 * 验证字符串是否为有效的PlanKey
 * @param key 要验证的字符串
 * @returns 是否为有效的PlanKey
 */
const isValidPlanKey = (key: unknown): key is PlanKey => {
  const validKeys: PlanKey[] = ['free', 'basic-credit', 'pro-credit', 'max-credit']
  return typeof key === 'string' && validKeys.includes(key as PlanKey)
}

/**
 * 解析额外附加的参数
 * @param attach 附加参数
 * @returns 解析后的附加参数对象
 */
export const parseAttachParams = (attach: string) => {
  const params = attach.split('&')
  const result: Record<string, string> = {}
  for (const param of params) {
    const [key, value] = param.split('=')
    if (key && value) {
      result[key] = value
    }
  }
  return {
    planKey: result.planKey || '',
    userId: result.userId || '',
  }
}
