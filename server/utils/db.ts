import mongoose from 'mongoose'
import { mongoConfig } from '../config'
import logger from './logger'

let isConnected = false
let isInitialized = false

mongoose.connection.setMaxListeners(15)

const initializeEventListeners = () => {
  if (isInitialized) return

  mongoose.connection.on('error', err => {
    console.error('MongoDB 连接错误:', err)
    isConnected = false
  })

  mongoose.connection.on('disconnected', () => {
    console.log('MongoDB 连接断开')
    isConnected = false
  })

  process.on('SIGINT', async () => {
    try {
      await disconnectDB()
      console.log('MongoDB 连接已关闭')
      process.exit(0)
    } catch (err) {
      console.error('关闭 MongoDB 连接时出错:', err)
      process.exit(1)
    }
  })

  isInitialized = true
}

export const connectDB = async () => {
  if (isConnected) {
    logger.debug('已经连接到数据库')
    return
  }

  try {
    await mongoose.connect(mongoConfig.uri)
    isConnected = true
    initializeEventListeners()
    logger.info(`数据库连接成功`)
  } catch (error) {
    logger.error('连接数据库失败:', error)
    isConnected = false
    throw error
  }
}

export const disconnectDB = async () => {
  if (!isConnected) {
    return
  }

  try {
    await mongoose.connection.close()
    isConnected = false
    console.log('数据库连接已关闭')
  } catch (error) {
    console.error('关闭数据库连接时出错:', error)
    throw error
  }
}

process.on('beforeExit', async () => {
  await disconnectDB()
})
