export function getISODateString() {
  return new Date().toISOString()
}

export function getDateTimestamp() {
  return Math.floor(Date.now() / 1000)
}

export function getCreatedAt() {
  return getISODateString()
}

export function getCreated() {
  return getDateTimestamp()
}

export function getCreatedTime() {
  return {
    createdAt: getCreatedAt(),
    created: getCreated(),
  }
}

export function compareDate(date1: string, date2: string) {
  return new Date(date1).getTime() - new Date(date2).getTime()
}

export function addDays(date: string, days: number) {
  const result = new Date(date)
  result.setDate(result.getDate() + days)
  return result.toISOString()
}

export function addTime(date: Date, time: number) {
  return new Date(date.getTime() + time)
}

export function setYear(date: string, year: string) {
  const result = new Date(date)
  result.setFullYear(parseInt(year))
  return result.toISOString()
}

const ONE_SECOND = 1000
const ONE_MINUTE = 60 * 1000
const HALF_HOUR = 30 * 60 * 1000
const ONE_HOUR = 60 * 60 * 1000
const ONE_DAY = 24 * 60 * 60 * 1000
const ONE_WEEK = 7 * 24 * 60 * 60 * 1000
const ONE_MONTH = 30 * 24 * 60 * 60 * 1000
const ONE_YEAR = 365 * 24 * 60 * 60 * 1000

export const time = {
  ONE_SECOND,
  ONE_MINUTE,
  HALF_HOUR,
  ONE_HOUR,
  ONE_DAY,
  ONE_WEEK,
  ONE_MONTH,
  ONE_YEAR,
}
