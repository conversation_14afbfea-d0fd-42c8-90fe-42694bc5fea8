import { consola } from 'consola'
import WebSocket from 'ws'

const logger = consola.withTag('AliyunWebSocketClient')

export type AliyunWebSocketClientOptions = {
  url: string
  apiKey: string
  additionalHeaders?: Record<string, string>
  dataInspection?: boolean
}

type MessageQueueItem = string | Buffer

export class AliyunWebSocketClient {
  private ws: WebSocket | null = null
  private connectionPromise: Promise<void> | null = null
  private messageQueue: MessageQueueItem[] = []
  private isExplicitlyClosed: boolean = false
  private reconnectAttempts: number = 0
  private readonly url: string
  private readonly apiKey: string
  private readonly headers: Record<string, string>

  private onOpenCallback: (() => void) | null = null
  private onMessageCallback: ((data: WebSocket.Data, isBinary: boolean) => void) | null = null
  private onErrorCallback: ((error: Error) => void) | null = null
  private onCloseCallback: ((code: number, reason: string) => void) | null = null

  constructor(options: AliyunWebSocketClientOptions) {
    this.url = options.url
    this.apiKey = options.apiKey
    this.headers = {
      Authorization: `bearer ${this.apiKey}`,
      ...(options.dataInspection ? { 'X-DashScope-DataInspection': 'enable' } : {}),
      ...options.additionalHeaders,
    }

    if (!this.apiKey) {
      const errorMsg = '缺少 API Key。'
      logger.error(errorMsg)
      throw new Error(errorMsg)
    }
    logger.info('AliyunWebSocketClient 初始化完成。')
  }

  public async connect(): Promise<void> {
    this.isExplicitlyClosed = false
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      logger.debug('WebSocket 连接已打开。')
      return
    }

    if (this.connectionPromise) {
      logger.debug('WebSocket 连接尝试进行中，等待现有 Promise。')
      return this.connectionPromise
    }

    logger.info(`尝试建立 WebSocket 连接到 ${this.url}...`)
    this.connectionPromise = new Promise((resolve, reject) => {
      this.ws = new WebSocket(this.url, { headers: this.headers })

      this.ws.on('open', () => {
        logger.info('WebSocket 连接建立成功。')
        this.reconnectAttempts = 0
        this.connectionPromise = null
        this.processMessageQueue()
        if (this.onOpenCallback) this.onOpenCallback()
        resolve()
      })

      this.ws.on('message', (data: WebSocket.Data, isBinary: boolean) => {
        if (this.onMessageCallback) this.onMessageCallback(data, isBinary)
        else logger.debug('收到 WebSocket 消息，但没有设置 onMessageCallback。')
      })

      this.ws.on('error', (error: Error) => {
        logger.error('WebSocket 错误:', error.message)
        this.connectionPromise = null
        if (this.onErrorCallback) this.onErrorCallback(error)
        this.cleanupConnection(false)
        reject(error)
      })

      this.ws.on('close', (code: number, reason: Buffer) => {
        const reasonStr = reason.toString()
        logger.warn(`WebSocket 连接已关闭。代码: ${code}, 原因: ${reasonStr}`)
        this.connectionPromise = null
        if (this.onCloseCallback) this.onCloseCallback(code, reasonStr)

        if (!this.isExplicitlyClosed) {
          logger.warn('WebSocket 连接意外关闭。')
        }
        this.cleanupConnection(this.isExplicitlyClosed)
      })
    })
    return this.connectionPromise
  }

  private cleanupConnection(wasExplicitlyClosed: boolean): void {
    logger.debug('正在清理 WebSocket 连接...')
    if (this.ws) {
      this.ws.removeAllListeners()
      if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
        logger.debug('正在终止 WebSocket 连接。')
        this.ws.terminate()
      }
    }
    this.ws = null
    if (!wasExplicitlyClosed) {
      this.messageQueue = []
    }
    logger.debug('连接清理完成。')
  }

  public send(message: string | object | Buffer): void {
    let messageToSend: MessageQueueItem

    if (Buffer.isBuffer(message)) {
      messageToSend = message
    } else if (typeof message === 'string') {
      messageToSend = message
    } else {
      messageToSend = JSON.stringify(message)
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      if (Buffer.isBuffer(messageToSend)) {
        logger.debug('发送 WebSocket 二进制消息。大小:', messageToSend.length, 'bytes')
      } else {
        logger.debug('发送 WebSocket 文本消息:', messageToSend)
      }
      this.ws.send(messageToSend)
    } else {
      logger.debug('WebSocket 未打开。将消息加入队列。')
      this.messageQueue.push(messageToSend)
      this.connect().catch(err => {
        const errorMsg = `为发送队列消息确保连接失败: ${err.message}`
        logger.error(errorMsg)
        if (this.onErrorCallback) this.onErrorCallback(new Error(`发送消息失败，连接错误: ${err.message}`))
      })
    }
  }

  private processMessageQueue(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      logger.debug(`正在处理消息队列。有 ${this.messageQueue.length} 条消息待处理。`)
      while (this.messageQueue.length > 0) {
        const msg = this.messageQueue.shift()
        if (msg) {
          if (Buffer.isBuffer(msg)) {
            logger.debug('从队列发送 WebSocket 二进制消息。大小:', msg.length, 'bytes')
          } else {
            logger.debug('从队列发送 WebSocket 文本消息:', msg)
          }
          this.ws.send(msg)
        }
      }
      logger.debug('消息队列处理完毕。')
    }
  }

  public close(code: number = 1000, reason: string = 'Client closed connection'): void {
    logger.info(`调用 close 方法关闭 WebSocket。代码: ${code}, 原因: ${reason}`)
    this.isExplicitlyClosed = true
    this.messageQueue = []

    if (this.ws) {
      if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
        logger.debug('正在关闭 WebSocket 连接。')
        this.ws.close(code, reason)
      } else {
        logger.debug('WebSocket 已关闭或正在关闭中。在 cleanupConnection 中处理。')
        this.cleanupConnection(true)
      }
    } else {
      logger.debug('WebSocket 实例不存在。直接调用 cleanupConnection。')
      this.cleanupConnection(true)
    }
    logger.info('WebSocket 客户端已关闭。')
  }

  public onOpen(callback: () => void): void {
    this.onOpenCallback = callback
  }

  public onMessage(callback: (data: WebSocket.Data, isBinary: boolean) => void): void {
    this.onMessageCallback = callback
  }

  public onError(callback: (error: Error) => void): void {
    this.onErrorCallback = callback
  }

  public onClose(callback: (code: number, reason: string) => void): void {
    this.onCloseCallback = callback
  }

  public get isOpen(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }
}
