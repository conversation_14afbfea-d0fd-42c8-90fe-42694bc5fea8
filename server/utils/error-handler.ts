import { H3Error, H3Event, setResponseStatus } from 'h3'
import { InsufficientBalanceError } from '~/utils/error'
import { ErrorCode } from '~/utils/error-code'
import logger from './logger'
import { respError } from './response'

export class AppError extends H3Error {
  constructor(message: string, code: number = 400) {
    super(message)
    this.statusCode = code
    this.message = message
  }
}

/**
 * 包装异步函数，允许特定错误类型冒泡到全局错误处理器
 * @param fn 要执行的异步函数
 * @param bubbleErrors 需要冒泡的错误类型数组
 */
export async function withErrorBubbling<T>(fn: () => Promise<T>, bubbleErrors: Array<new (...args: any[]) => Error> = [InsufficientBalanceError]): Promise<T> {
  try {
    return await fn()
  } catch (error) {
    // 检查是否是需要冒泡的错误类型
    const shouldBubble = bubbleErrors.some(ErrorType => error instanceof ErrorType)
    if (shouldBubble) {
      throw error // 重新抛出，让全局处理器处理
    }
    // 其他错误继续抛出给调用者处理
    throw error
  }
}

/**
 * 专门用于处理带有积分检查的操作
 */
export async function withCreditErrorHandling<T>(fn: () => Promise<T>): Promise<T> {
  return withErrorBubbling(fn, [InsufficientBalanceError])
}

/**
 * 智能错误处理器 - 根据错误类型自动返回正确的响应
 * @param event H3Event 对象
 * @param error 捕获的错误
 * @param fallbackErrorCode 默认错误码
 * @returns 错误响应或重新抛出需要冒泡的错误
 */
export function handleApiError(
  event: H3Event,
  error: unknown,
  fallbackErrorCode: (typeof ErrorCode)[keyof typeof ErrorCode] = ErrorCode.INTERNAL_SERVER_ERROR,
) {
  // 处理余额不足错误
  if (error instanceof InsufficientBalanceError) {
    logger.warn(`Credit insufficient: ${error.message}`)
    const errorCode = ErrorCode.USER_INSUFFICIENT_BALANCE
    setResponseStatus(event, 402) // Payment Required
    return respError(errorCode.code, errorCode.displayMessage)
  }

  // 处理其他已知错误类型
  if (error instanceof AppError) {
    logger.warn('API Error:', error.message)
    setResponseStatus(event, error.statusCode)
    return respError(error.statusCode, error.message)
  }

  // 处理通用错误
  if (error instanceof Error) {
    logger.error('API Error:', error.message, error)
    return respError(fallbackErrorCode.code, fallbackErrorCode.displayMessage)
  }

  // 处理未知错误
  logger.error('Unknown API Error:', error)
  return respError(fallbackErrorCode.code, fallbackErrorCode.displayMessage)
}

export const handleError = (error: unknown) => {
  if (error instanceof AppError) {
    logger.warn({
      message: error.message,
      statusCode: error.statusCode,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    })
    throw error
  }

  if (error instanceof Error) {
    logger.error({
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      context: {
        name: error.name,
        cause: error.cause,
      },
    })
    throw createError({
      statusCode: 500,
      message: 'server error',
      data: process.env.NODE_ENV === 'development' ? error : undefined,
    })
  }

  logger.error('未知错误:', {
    error,
    timestamp: new Date().toISOString(),
    type: typeof error,
  })
  throw createError({
    statusCode: 500,
    message: 'server error',
  })
}
