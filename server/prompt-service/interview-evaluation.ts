import type { InterviewType } from '~/types/interview'

export interface StageInsight {
  stage: string
  stageName: string
  summary: string
  highlights: string[]
  improvements: string[]
  specificAdvice: string[]
}

export interface TechnicalInsights {
  knowledgeGaps: string[]
  practicalSkills: string[]
  problemSolvingApproach: string[]
  technicalCommunication: string[]
  improvements: string[]
}

export interface CommunicationInsights {
  strengths: string[]
  areasForImprovement: string[]
  expressionTips: string[]
  interactionStyle: string[]
}

export interface CareerInsights {
  motivation: string[]
  careerPlanning: string[]
  roleAlignment: string[]
  developmentSuggestions: string[]
}

export interface PracticalAdvice {
  category: string
  categoryName: string
  items: Array<{
    issue: string
    suggestion: string
    examples?: string[]
  }>
}

export interface ComprehensiveEvaluation {
  stageInsights: StageInsight[]
  technicalInsights: TechnicalInsights
  communicationInsights: CommunicationInsights
  careerInsights: CareerInsights
  practicalAdvice: PracticalAdvice[]
  overallSummary: string
  keyRecommendations: string[]
  nextSteps: string[]
}

export const useInterviewEvaluation = () => {
  const generateEvaluationPrompt = (type: InterviewType) => {
    return `作为资深面试官，请根据整个面试过程为候选人提供全面、实用的面试反馈和建议。请分析候选人的回答表现，结合简历背景和AI标准答案参考，给出具体可操作的改进建议。

请严格按照以下JSON格式返回评价结果（不要使用Markdown格式包装）：

{
  "stageInsights": [
    {
      "stage": "introduction",
      "stageName": "自我介绍阶段",
      "summary": "候选人在自我介绍中展现了良好的逻辑性",
      "highlights": ["表达清晰", "逻辑条理"],
      "improvements": ["缺乏亮点", "时间控制不佳"],
      "specificAdvice": ["建议用STAR法则重新组织自我介绍", "控制在2-3分钟内"]
    }
  ],
  "technicalInsights": {
    "knowledgeGaps": ["对React Hooks原理理解不够深入", "缺乏对性能优化的系统性认知"],
    "practicalSkills": ["项目经验丰富", "能够结合实际场景说明"],
    "problemSolvingApproach": ["思路清晰", "但缺乏多角度分析"],
    "technicalCommunication": ["专业术语使用准确", "但解释过于复杂"],
    "improvements": ["建议深入学习React原理", "练习用更简洁的语言解释技术概念"]
  },
  "communicationInsights": {
    "strengths": ["表达清晰", "语速适中"],
    "areasForImprovement": ["互动性不足", "缺乏自信"],
    "expressionTips": ["多使用具体数据和案例", "避免模糊的表述"],
    "interactionStyle": ["主动提问", "增强眼神交流"]
  },
  "careerInsights": {
    "motivation": ["对技术有热情", "学习意愿强"],
    "careerPlanning": ["目标相对清晰", "但缺乏具体规划"],
    "roleAlignment": ["技能与岗位匹配度较高"],
    "developmentSuggestions": ["制定3-5年详细发展计划", "考虑技术深度与广度的平衡"]
  },
  "practicalAdvice": [
    {
      "category": "technical_preparation",
      "categoryName": "技术准备",
      "items": [
        {
          "issue": "React Hooks原理回答不够深入",
          "suggestion": "系统学习React源码，理解Hooks的实现机制",
          "examples": ["可以从useState的源码入手", "理解fiber架构对Hooks的影响"]
        }
      ]
    },
    {
      "category": "communication_skills",
      "categoryName": "沟通技巧",
      "items": [
        {
          "issue": "技术解释过于复杂",
          "suggestion": "练习用简洁明了的语言解释复杂概念",
          "examples": ["可以用类比的方式", "先说结论再说原理"]
        }
      ]
    }
  ],
  "overallSummary": "候选人整体表现良好，技术基础扎实，但在深度理解和表达技巧上还有提升空间。建议重点关注技术原理的深入学习和沟通表达的优化。",
  "keyRecommendations": [
    "深入学习React原理和源码",
    "练习用更简洁的语言表达技术概念",
    "准备更多具体的项目案例和数据",
    "制定清晰的职业发展规划"
  ],
  "nextSteps": [
    "制定为期3个月的技术深度学习计划",
    "每周练习一次模拟面试，重点训练表达能力",
    "整理和优化个人项目案例库",
    "寻找mentor指导职业发展方向"
  ]
}

## 评价原则：
1. **基于实际表现**：仅基于候选人的实际回答内容进行评价
2. **结合背景信息**：参考候选人简历背景，给出符合其经验水平的建议
3. **参考标准答案**：对比AI生成的标准回答，指出候选人回答的优势和不足
4. **实用性导向**：重点提供可操作的、具体的改进建议
5. **发展性视角**：不仅指出问题，更要提供解决方案和发展路径
6. **个性化建议**：根据候选人的具体情况给出针对性建议

## 特别关注：
- 技术概念理解的深度和准确性
- 项目经验的表述逻辑和完整性
- 沟通表达的清晰度和专业性
- 问题解决思路的系统性和创新性
- 职业发展的规划性和现实性
- 学习能力和适应性的体现

请确保评价内容具体、客观、建设性，避免空泛的表述，多提供可执行的具体建议。`
  }

  const getPositionName = (type: InterviewType): string => {
    const positionMap: Record<InterviewType, string> = {
      frontend: '前端开发工程师',
      backend: '后端开发工程师',
      fullstack: '全栈开发工程师',
      algorithm: '算法工程师',
      'new-media-operation': '新媒体运营',
      'product-manager': '产品经理',
      'sales-specialist': '销售专员',
      marketing: '市场营销',
      'test-engineer': '测试工程师',
      'ui-designer': 'UI设计师',
      'project-manager': '项目经理',
      'customer-service': '客服专员',
      'human-resource-specialist': '人力资源专员',
    }
    return positionMap[type]
  }

  return {
    generateEvaluationPrompt,
    getPositionName,
  }
}
