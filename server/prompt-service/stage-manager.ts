import type { InterviewStage } from '~/types/interview'

const stageTransitionPrompt = (currentStage: InterviewStage) => {
  return `你是面试进度管理助手。请根据以下对话历史和当前阶段，判断是否需要进入下一阶段。
  
当前阶段: ${currentStage}

阶段说明:
introduction: 开场和自我介绍
technical: 技术能力考核
project: 项目经验考核
design: 系统设计考核
ending: 结束阶段
terminated: 终止阶段

判断标准:
1. introduction -> technical: 完成自我介绍，准备开始技术面试
2. technical -> project: 基础技术问题已充分考核
3. project -> design: 项目经验已充分了解
4. design -> ending: 设计能力考核完成
5. ending -> terminated: 面试结束
6. 特殊规则：如果用户在任何阶段明确表示想要结束面试，直接跳转到 terminated 阶段

请仅返回如下格式:
{
  "shouldTransition": true/false,
  "nextStage": "当前阶段或下一阶段",
  "reason": "简要原因"
}`
}

export const useStageManager = () => {
  return {
    stageTransitionPrompt,
  }
}
