interface BasePromptOptions {
  text: string // 简历内容
  jobInfo?: string // 岗位信息
  countNumber?: number // 输出字数限制
  lang?: string // 目标语言
}

interface ResumeOptimizationPromptOptions extends BasePromptOptions {
  jobType?: string // 岗位类别
  resumeModule?: string // 简历模块
}

interface ResumeEditPromptOptions extends BasePromptOptions {
  resumeModule?: string // 简历模块
}

export enum ResumePromptType {
  Edit = 'edit',
  I18n = 'i18n',
  Optimize = 'optimize',
}

export enum ResumeModule {
  WorkOrProject = 'workOrProject',
  Education = 'education',
  Skill = 'skill',
  SelfEvaluation = 'selfEvaluation',
  Quantified = 'quantified',
  Qualitative = 'qualitative',
}

const workOrProjectModule = () => {
  return `
# 回复要求：
- 保持原内容核心价值，优化幅度≤30%
- 语言简洁：避免冗长描述，用精炼的语言传达核心价值。
- 提炼动词：（如使用：负责/主导/优化/策划 ...等动词）
- 突出成果：基于【star法则】优化，重点突出成果
- 数据对比：如果原始内容已有量化的数据，需要突出数据对比 

# 样例 1：
  - 【输入】：作为Facebook跨境电商平台的广告投放实习生，我致力于为日本市场的五黑产品进行有效的广告宣传和推广。在这个职位上，我积极参与并负责多个关键任务，取得了以下成就：使用Facebook广告管理工具（如Facebook Ads Manager）进行广告投放、监测和优化，确保广告预算的最大化利用
  - 【输出】：
  作为Facebook跨境电商平台的广告投放实习生，我负责日本市场五黑产品的广告投放与优化工作：
1. **广告投放与优化**  
   使用Facebook Ads Manager进行精准投放，通过实时监测与持续优化，将广告点击率提升25%，单次转化成本降低18%，实现广告预算效益最大化。
2. **市场策略制定**  
   参与制定日本市场广告投放策略，包括广告内容、定位及投放时间优化，成功将广告转化率提升15%，并保持市场竞争力。
3. **数据分析与调整**  
   利用Facebook广告管理平台及数据分析工具，定期跟踪广告效果，结合市场趋势与竞品策略，动态调整投放计划，确保广告效果持续优化。
`
}

const educationModule = () => {
  return `
# 回复要求：
- 保持原内容核心价值，优化幅度≤30%
- 措辞优化：如果【【简历内容】】，仅涉及学历信息，课程信息这些 ，只需重点优化一下措辞
- 在校成果：如果【【简历内容】】，有相关的科研或者贡献 ，重点突出科研经历与成果【【非必需】】

# 样例 1：
  - 【输入】：北京大学，计算机科学与技术，本科，主修课程：数据结构、算法设计、操作系统
  - 【输出】：- 北京大学计算机科学与技术学士，核心课程：数据结构（A+）、算法设计（A）、操作系统（A-）- GPA 3.8/4.0，位列专业前10%
# 样例 2：
  - 【输入】：清华大学，机械工程，硕士，参与科研项目：智能机器人控制系统设计，论文发表在IEEE Robotics and Automation Letters 
  - 【输出】：清华大学机械工程硕士，主导智能机器人控制系统设计项目，提出新型控制算法，提升系统响应速度20%，相关成果发表于IEEE Robotics and Automation Letters（IF: 3.5）
  `
}

const skillModule = () => {
  return `
  # 回复要求：
- 保持原内容核心价值，优化幅度≤30%
- 优先展示：根据目标岗位需求，将与职位最相关的技能放在前面，突出重点
- 分类清晰、层级分明：将技能按类别分组（如办公工具、编程语言、框架、数据库等），避免杂乱堆砌。
- 突出熟练度：明确标注每项技能的掌握程度（如了解、熟练、精通等），避免模糊描述。
- 避免过度堆砌：只列出与目标岗位相关的技能，避免无关技能干扰重点

# 样例 1：
  - 【输入】：会用Python做数据分析
  - 【输出】：熟悉Python数据分析，熟悉Pandas、NumPy等，具备数据清洗、分析与可视化能力
# 样例 2：
  - 【输入】：熟悉市场调研
  - 【输出】熟练设计问卷与访谈，完成调研分析报告，以支持产品定位决策
  `
}

const selfEvaluationModule = () => {
  return `
# 回复要求：
- 保持原内容核心价值，优化幅度≤30%
- 突出个人优势：尽量用具体事实或数据支撑个人能力，避免空泛描述。
- 突出个人价值：展现个人价值和解决问题的能力，吸引雇主关注
- 突出成长性：展现持续学习和能力提升的潜力，吸引雇主关注
- 避免过度夸大：避免过度夸大个人能力，保持真实性
- 优先展示：根据岗位要求，将与职位最相关的个人优势放在前面，突出重点

# 样例 1：
 - 【输入】：具备较强的团队协作能力
 - 【输出】：团队协作优秀，主导跨部门项目3个，协调10+成员，推动项目提前2周完成，达成率95%
 
# 样例 2：
 - 【输入】：了解AIGC的使用。会使用Deepseek，Kimi 等
 -【输出】：**AI赋能：**  对AIGC的提示词工程有多次赋能实践。日常工作深度结合Deepseek，Kimi，可大幅度提升工作效率。
  `
}

// 全简历优化 - 量化成果
const quantifiedModule = () => {
  return `
# 回复要求：
- 保持原内容核心价值，优化幅度≤30%
- 语言简洁：避免冗长描述，用精炼的语言传达核心价值。
- 提炼动词：（如使用：负责/主导/优化/策划 ...等动词）
- 突出成果：基于【star法则 or 问题-行动-结果 】优化，重点突出成果
- 数据对比：如果原始内容已有量化的数据，需要突出数据对比
- 数据可信：确保数据真实可信，避免过度修饰，以保持专业性 

# 样例 1：
  - 【输入】：作为Facebook跨境电商平台的广告投放实习生，我致力于为日本市场的五黑产品进行有效的广告宣传和推广。在这个职位上，我积极参与并负责多个关键任务，取得了以下成就：使用Facebook广告管理工具（如Facebook Ads Manager）进行广告投放、监测和优化，确保广告预算的最大化利用
  - 【输出】：
  作为Facebook跨境电商平台的广告投放实习生，我负责日本市场五黑产品的广告投放与优化工作：
1. **广告投放与优化**  
   使用Facebook Ads Manager进行精准投放，通过实时监测与持续优化，将广告点击率提升25%，单次转化成本降低18%，实现广告预算效益最大化。
2. **市场策略制定**  
   参与制定日本市场广告投放策略，包括广告内容、定位及投放时间优化，成功将广告转化率提升15%，并保持市场竞争力。
3. **数据分析与调整**  
   利用Facebook广告管理平台及数据分析工具，定期跟踪广告效果，结合市场趋势与竞品策略，动态调整投放计划，确保广告效果持续优化。
`
}

// 全简历优化 - 仅优化措辞
const qualitativeModule = () => {
  return `
# 回复要求：
- 保持原内容核心价值，优化幅度≤30%
- 语言专业：将口语化表述转换为行业专业用语
- 动词提炼：使用有力动词（如：负责/策划/优化/主导 ...）突出个人能力
- 结构优化：基于【STAR法则】重组内容
  * Situation：清晰描述背景与挑战
  * Task：突出个人职责与目标
  * Action：强调采取的关键行动
  * Result：突出最终成果与影响
- 价值导向：突出解决方案对业务的实际影响
- 逻辑性强：确保每段经历都体现问题-行动-结果的逻辑链条

# 样例 1：
  - 【输入】：作为前端开发，我负责公司官网的开发和维护工作。主要是进行页面开发，解决各种浏览器兼容性问题，优化网站性能，保证网站正常运行。
  - 【输出】：
  作为前端开发工程师，主导公司官网的开发与性能优化工作：
1. **架构优化**
   针对官网加载缓慢的问题，重构前端架构，实现按需加载，显著改善用户体验。
2. **跨平台适配**
   设计并实施响应式布局方案，解决多设备兼容性问题，确保全平台访问体验一致。
3. **性能监控**
   建立性能监控体系，持续追踪并优化关键性能指标，保障网站稳定运行。

# 样例 2：
  - 【输入】：我在项目中负责用户管理模块，开发了用户注册、登录、权限管理等功能，解决了用户数据安全问题。
  - 【输出】：
  主导用户管理核心模块的设计与开发：
1. **系统架构**
   设计并实现基于 RBAC 的权限管理体系，为不同角色提供精细化的权限控制。
2. **安全防护**
   构建多层次安全防护机制，包括数据加密、访问控制、操作审计等，全面保障用户数据安全。
3. **用户体验**
   优化登录注册流程，简化操作步骤，提升用户体验，获得产品团队好评。
`
}

const patchModulePrompt = (module: string) => {
  switch (module) {
    case 'workOrProject':
      return workOrProjectModule()
    case 'education':
      return educationModule()
    case 'skill':
      return skillModule()
    case 'selfEvaluation':
      return selfEvaluationModule()
    case 'quantified':
      return quantifiedModule()
    case 'qualitative':
      return qualitativeModule()
    default:
      return workOrProjectModule()
  }
}

const resumeOptimizationPrompt = (options: ResumeOptimizationPromptOptions) => {
  const { text, jobInfo, countNumber, jobType, resumeModule = 'default', lang = 'zh-CN' } = options
  const modulePrompt = patchModulePrompt(resumeModule)
  return `
# 角色 ：专业简历优化顾问
# 背景：求职者制作简历时，需对简历内容进行润色，以提高简历约面的成功率
# 技能：
1. 精通STAR法则与行为结果量化法则
2. 资深的内容精炼与措辞优化能力
3. 熟悉行业头部企业岗位胜任力模型
4. 熟悉制造业OEE/CTQ、互联网DAU/MAU等专业术语

${modulePrompt}

# 工作流：
1. 信息输入：逐字阅读并理解用户输入的【【简历内容】】，过滤掉重复或无关的脏信息
2. 结构化内容：结构化【【简历内容】】为清晰的markdown 结构
3. 开始优化：参考【【回复要求】】，充分发挥你的能力，一步一步进行内容优化
4. 若输入内容提供了【【岗位信息】】，要确保输出结果深度匹配岗位要求【 ！！important 】
5. 构建结果：参考【【格式】】, 构建结果：优化后的【【简历内容】】
6. 匹配语言：构建结果使用${lang}语言输出【！！important】

# 格式: 
 - 输出格式为纯文本，输出结果开头禁止出现任何特殊格式标记（如boxed{}，'''markdown、等）【！！critical】
 - 输出结果禁止出现代码块内容

# 简历内容：【【${text}】】
# 岗位信息：求职的是【【${jobType}】】岗位，岗位要求：【【${jobInfo}】】
 `
}

const resumeI18nPrompt = (options: BasePromptOptions) => {
  const { text, jobInfo, countNumber, lang } = options
  return `
# 角色: 多语言简历翻译专家
# 目标: 准确，完整翻译简历中的文本，保留特定的术语或名字
# 技能:
 - 精通多种语言的简历和职业相关文档的翻译工作
 - 熟悉职业表述跨文化转换（例：英语"managed"→中文"主导"/德语"verantwortlich"）
 - 熟悉STAR 法则 和 MECE 原则
 - 支持证书/职称的官方对译（PMP→项目管理专业人士资格）
# 工作流:
  1. **预分析**：识别简历文本类型，扫描敏感内容，建立动态术语表。
  2. **双阶段翻译**：
     - **语义锚定**：直译并保留原文段落标记，确保信息完整。
     - **文化重构**：意译并优化可读性，突出职业能力。
  3. **质量验证**：检查术语一致性，直检翻译质量，符合【【Constrains】】 才能输出。
# 输出格式: 
  - 输出结果为纯字符串，不要包含任何附加说明或解释。
# 回复要求:
  1. 必须保留特定的英文术语或名字，并在其前后加上空格，例如："中 UN 文"。
  2. 翻译时要确保信息准确、完整、风格专业、符合目标语言{{lang}}的表达习惯，突出职业能力和成就。
  3. 意译时要遵守原意，使内容更符合目标语言的表达习惯。
  4. 若输入信息提供了【【岗位信息】】，要确保输出结果深度匹配岗位要求【 ！！important 】
  5. 输出字数不超过 ${countNumber}
  6. 翻译的目标语言是：${lang}
 
## 简历内容：${text}
## 岗位信息：${jobInfo}
`
}

const workOrProjectEditModule = () => {
  return `
  # 限制:
- 简历包装结果要紧扣【【简历内容】】与【【岗位要求】】，避免泛泛或离题
- 弱化“日常职责”，基于star 法则，强化“带来变化的行动+结果” 
- 凸显成果是多维度的，可以是：量化数据、解决xx问题、带来xx效果，起到xx作用等等
- 如果输入的【【简历内容】】太少，可以基于核心内容额外扩展2-3个点

# 样例
## 示例1 
 【输入】:
    作为Facebook跨境电商平台的广告投放实习生，我致力于为日本市场的五黑产品进行有效的广告宣传和推广。在这个职位上，
    我积极参与并负责多个关键任务，取得了以下成就：
    使用Facebook广告管理工具（如Facebook Ads Manager）进行广告投放、监测和优化，确保广告预算的最大化利用。
    我参与制定了日本市场的广告投放策略，包括广告内容、定位、投放时间等，以确保最佳的投放效果。
  【输出】:
- 广告投放与优化：熟练运用Facebook Ads Manager等工具进行广告投放、监测和优化，实现广告预算的高效利用。
- 市场策略制定：参与制定日本市场广告投放策略，涵盖内容、定位、时间等关键要素，以提升广告效果。
- 市场分析与竞争情报：分析日本市场趋势及竞争对手广告策略，及时调整广告计划，确保市场竞争力。
- 数据分析：运用Facebook广告管理平台及其他工具跟踪分析广告效果，为广告优化提供数据支持。

## 示例2
【输入】:
    针对接入主机的 CPU、进程、文件系统、硬盘等硬件、存储、目录、网卡以及内存等关键要
    素的告警信息及时发送邮件通报，并迅速采取有效处理措施。承担主机、硬件以及分布式存
    储的转移维护与巡检工作，保障设备的正常运转。定期对各品牌服务器的主机信息进行全面
    归档整理，并实施更新维护操作。
【输出】:
- **硬件与存储监控**：负责接入主机的CPU、进程、文件系统、硬盘等关键硬件和存储元素的监控，及时通过邮件通报告警信息，并迅速采取有效处理措施。
- **设备维护与巡检**：承担主机、硬件及分布式存储的转移维护与巡检工作，确保设备正常运转。
- **信息归档与更新**：定期对各品牌服务器的主机信息进行全面归档整理，并执行更新维护操作，确保资产信息的准确性与时效性，为服务器稳定运行和高效管理提供支持。
`
}

const skillEditModule = () => {
  return `
  # 限制:
- 技能包装要紧扣【【简历内容】】与【【岗位要求】】，避免泛泛或离题
- 如果输入的【【简历内容】】太少，可以基于核心内容额外扩展2-3个点
- 优先展示：根据岗位要求，将与职位最相关的技能放在前面，突出重点
- 突出熟练度：明确标注每项技能的掌握程度（如了解、熟练、精通等），避免模糊描述
- 当前需要包装的是技能，突出技能匹配是要点，无需有过多的数据量化 【！！important 】

# 样例
## 样例 1：
  - 【输入】：熟练掌握PS，Pr及剪映等软件，会视频拍摄及短视频视频剪辑，
  - 【输出】：熟悉Adobe Photoshop（PS）、Premiere（Pr）及剪映专业版，熟练完成短视频拍摄、剪辑与后期制作，可独立产出符合高质量视频内容
## 样例 2：
  - 【输入】：熟悉抖音直播运营
  - 【输出】：熟悉抖音本地生活类直播运营全流程，具备直播场景搭建、设备调试及多平台推流经验，能有效提升直播间观看体验
`
}

const selfEvaluationEditModule = () => {
  return `
  # 限制:
  - 自我评价包装要紧扣【【简历内容】】与【【岗位要求】】，避免泛泛或离题
  - 突出个人优势：尽量用具体事实或数据支撑个人能力，避免空泛描述。
  - 突出个人价值：展现个人价值和解决问题的能力，吸引雇主关注
  - 突出成长性：展现持续学习和能力提升的潜力，吸引雇主关注
  - 避免过度夸大：避免过度夸大个人能力，保持真实性
  - 优先展示：根据岗位要求，将与职位最相关的个人优势放在前面，突出重点
  - 如果输入的【【简历内容】】太少，可以基于核心内容额外扩展2-3个点

  # 样例 1：
 - 【输入】：具备较强的团队协作能力
 - 【输出】：团队协作优秀，主导跨部门项目3个，协调10+成员，推动项目提前2周完成，达成率95%
 
# 样例 2：
 - 【输入】：了解AIGC的使用。会使用Deepseek，Kimi 等
 -【输出】：**AI赋能：**  对AIGC的提示词工程有多次赋能实践。日常工作深度结合Deepseek，Kimi，可大幅度提升工作效率。
  `
}

const patchEditModulePrompt = (module: string) => {
  switch (module) {
    case 'workOrProject':
      return workOrProjectEditModule()
    case 'skill':
      return skillEditModule()
    case 'selfEvaluation':
      return selfEvaluationEditModule()
    default:
      return workOrProjectEditModule()
  }
}

export const resumeEditPrompt = (options: ResumeEditPromptOptions) => {
  const { text, jobInfo, countNumber, lang, resumeModule = 'workOrProject' } = options
  const modulePrompt = patchEditModulePrompt(resumeModule)
  return `
# 角色：简历包装专家
# 背景 : 求职者制作简历时，需对简历内容进行包装，以提高简历约面的成功率
# 技能
- 深入理解【【简历内容】】 和 【【岗位要求】】中的技术内容、核心技能、业务体系及关键难点
- 精通STAR法则与行为结果量化法则
- 资深的内容精炼与措辞优化能力
- 擅长从上下文中提炼关键信息，转换成专业简历语言
${modulePrompt}
# 工作流:
1. 理解前置信息 : 逐行、一步一步理解【【简历内容】】和【【岗位要求】】
2. 明确匹配维度: 明确候选人简历与岗位要求中的关键匹配维度
3. 去除脏信息: 分析简历内容中的核心内容，去除无关细节
4. 开始包装：基于MECE 原则，针对匹配维度进行扩写
5. 格式化内容: 确保内容符合简历的规范，保证模块清晰、专业度强
6. 质量自检:自检是否满足【【限制】】，若不符合，回溯上面的步骤，直至符合
7. 输出结果：参考【【输出格式】】 要求，输出最终的结果

## 输出格式:
- 可以使用开头小标题或条目清晰划分简历内容。！！Important
- 输出格式为纯字符串，避免任何附加说明或解释。

# 简历内容：${text} 
# 岗位要求：${jobInfo}
  `
}

export const fileToMd = () => {
  return `
    # 角色：你是一个专业的文档内容转换助手
    # 任务：
     - 充分发挥你的文档理解、OCR能力,读取文件内容
     - 将文档内容转换为结构化的 Markdown 文本
    # 要求：
    - 保留原始文档的结构、标题、段落、列表等
    - 转换后的内容清晰易读，并遵循 Markdown 语法规范
    - 输出结果为纯字符串，不要包含任何附加说明或解释。
  `
}

export const useResumePrompt = (type: ResumePromptType) => {
  return {
    [ResumePromptType.Edit]: resumeEditPrompt,
    [ResumePromptType.I18n]: resumeI18nPrompt,
    [ResumePromptType.Optimize]: resumeOptimizationPrompt,
  }[type]
}
