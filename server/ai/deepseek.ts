import { mode } from 'crypto-js'
import OpenAI from 'openai'
import { ChatBody, Event } from '~/server/type'
import { CommonCompletionStream } from '~/utils/stream/base'
export async function deepseekChat(opt: ChatBody): Promise<CommonCompletionStream> {
  const { message, model } = opt
  const deepSeekModel = new OpenAI({
    apiKey: model.apiKey,
    baseURL: model.baseURL,
  })
  let reasoningContent = ''
  let answerContent = ''
  let isAnswering = false
  const response = new CommonCompletionStream({
    model: model.model,
    inputMessages: message.map(item => ({
      type: 'text',
      text: item.content,
    })),
  })
  const id = Math.random().toString(36).slice(2)
  return new Promise(async (resolve, reject) => {
    try {
      resolve(response)
      const resp = await deepSeekModel.chat.completions.create({
        model: model.model,
        messages: message,
        stream: true,
        stream_options: {
          include_usage: true,
        },
      })

      for await (const chunk of resp) {
        if (chunk.choices[0]) {
          const delta = chunk.choices[0].delta as any
          const usage = chunk.usage
          if (usage) {
            logger.info(`提示词消耗: ${usage.prompt_tokens} tokens`)
            logger.info(`生成回复消耗: ${usage.completion_tokens} tokens`)
            logger.info(`总共消耗: ${usage.total_tokens} tokens`)
            response.emitUsage({
              promptTokens: usage.prompt_tokens,
              completionTokens: usage.completion_tokens,
              promptTokensInOpenAI: 0,
              completionTokensInOpenAI: 0,
            })
            // 如果有详细信息
            if (usage.completion_tokens_details) {
              logger.info('生成详情:', usage.completion_tokens_details)
            }
            if (usage.prompt_tokens_details) {
              logger.info('提示词详情:', usage.prompt_tokens_details)
            }
          }

          // 处理思考过程
          if (delta?.reasoning_content) {
            process.stdout.write(delta.reasoning_content)
            reasoningContent += delta.reasoning_content
            response.emitMessagePart({ type: 'text', text: delta.reasoning_content, id })
          }
          // 处理正式回复
          else if (delta?.content) {
            if (!isAnswering) {
              isAnswering = true
              // response.emitMessagePart({ type: 'function', name: 'mind finish', id })
            }
            process.stdout.write(delta.content)
            // response.emitMessagePart({ type: 'text', text: delta.content, id })
            response.emitMessagePart({ type: 'text', text: delta.content, id })
            answerContent += delta.content
          }
        }
      }
      response.end()
    } catch (error) {
      response.emitError(error instanceof Error ? error : new Error(String(error)))
      logger.error('debug error', error)
      reject(error)
    }
  })
}
