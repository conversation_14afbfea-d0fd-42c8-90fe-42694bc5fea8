import { config } from '~/server/config'

export type modelType = keyof typeof models

export type modelInfoType = (typeof models)[modelType]

export enum AiModel {
  Deepseek_R1 = 'Deepseek_R1',
  Deepseek_V3 = 'Deepseek_V3',
  Whisper_1 = 'Whisper_1',
}

//docs:https://openrouter.ai/
//本项目token（非第三方平台token）成本规范：¥0.008 /1k tokens
export const models = {
  Deepseek_R1: {
    key: 'Deepseek_R1',
    type: 'deepseek',
    apiKey: config.deepseek.apiKey || '',
    baseURL: config.deepseek.baseURL || '',
    model: 'deepseek/deepseek-r1',
    costOf1KRawInputTokens: 0.004, // ¥0.00323 / 1K tokens
    costOf1KRawOutputTokens: 0.016, // ¥0.01542 / 1K tokens
  },
  Deepseek_V3: {
    key: 'Deepseek_V3',
    type: 'deepseek',
    apiKey: config.deepseek.apiKey || '',
    baseURL: config.deepseek.baseURL || '',
    model: 'deepseek-chat',
    costOf1KRawInputTokens: 0.002, // ¥0.00201 / 1K tokens
    costOf1KRawOutputTokens: 0.007, // ¥0.00631 / 1K tokens
  },
  Whisper_1: {
    key: 'Whisper_1',
    type: 'gpt',
    apiKey: config.openai.apiKey || '',
    baseURL: config.openai.baseURL || '',
    model: 'whisper-1',
    costOf1KRawInputTokens: 999999, // not allow to use
    costOf1KRawOutputTokens: 999999, // not allow to use
  },
} as const

export type VoiceBillingConfig = {
  // 按时长计费配置
  durationBased?: {
    // 每秒token消耗
    costPerSecond: number
    // 最小计费单位(秒)
    minBillingUnit: number
  }
  // 按字符计费配置
  characterCountBased?: {
    // 每个字符token消耗
    costPerCharacter: number
    // 最小计费单位(字符)
    minBillingUnit: number
  }
  // 按次数计费配置
  countBased?: {
    // 每次消耗的token数
    costPerCount: number
  }
}

/**
 * 字符计算规则
 * - 汉字: 2个字符/字
 * - 英文字母: 1个字符/字
 * - 标点符号: 1个字符/个
 * - 空格: 1个字符/个
 */
export const ttsBillingConfigs = {
  xunfei: {
    countBased: {
      // 每次消耗500个token (调用成本约0.004元每次)
      costPerCount: 500,
    },
  },
  cosyvoice: {
    // 成本0.0002元/字符
    characterCountBased: {
      // 每个字符消耗25个token
      costPerCharacter: 25,
      // 最小计费单位: 10
      minBillingUnit: 10,
    },
  },
  //docs:https://www.minimaxi.com/price
  minimax: {
    models: {
      // 成本0.00035元/字符
      'speech-01-hd': {
        costPerCharacter: 44,
        minBillingUnit: 10,
      },
      'speech-02-hd': {
        costPerCharacter: 44,
        minBillingUnit: 10,
      },
      // 成本0.0002元/字符
      'speech-01-turbo': {
        costPerCharacter: 25,
        minBillingUnit: 10,
      },
      'speech-02-turbo': {
        costPerCharacter: 25,
        minBillingUnit: 10,
      },
      default: {
        costPerCharacter: 44,
        minBillingUnit: 10,
      },
    },
  },
} as const

/**
 * ASR 语音识别计费配置
 */
export const asrBillingConfigs = {
  //docs:https://www.xfyun.cn/services/speech_big_model?target=price
  xunfei: {
    // 按次数计费，每次识别消耗固定token
    countBased: {
      // 每次消耗288个token (调用成本约0.0023元每次)
      costPerCount: 288,
    },
  },
  sensevoice: {
    // 按时长计费
    durationBased: {
      // 每秒消耗13个token (成本约0.0001元/秒)
      costPerSecond: 13,
      // 最小计费单位: 1秒
      minBillingUnit: 1,
    },
  },
} as const
