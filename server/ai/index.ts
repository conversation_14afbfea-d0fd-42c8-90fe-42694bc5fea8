import { z } from 'zod'
import { userWallet } from '~/server/daos/user-wallet'
import { messages } from '~/server/type'
import { InsufficientBalanceError } from '~/utils/error'
import { CommonCompletionStream } from '~/utils/stream/base'
import { WalletType } from '../models/wallet.model'
import { updateTokenUsage } from '../user/wallet'
import { modelInfoType, models, modelType } from './constants'
import { deepseekChat } from './deepseek'

type ContextType = {
  userId: string
  modelInfo: modelInfoType
  messages: z.infer<typeof messages>
  wallet: WalletType | null
}
type PreHookFunction = (context: ContextType) => Promise<any> | any
type PostHookFunction = (context: ContextType) => Promise<any> | any

export class LLM {
  private messages: z.infer<typeof messages>
  private modelInfo: modelInfoType
  public stream: CommonCompletionStream | null = null
  private userId: string
  private context: ContextType | null = null
  private preHooks: PreHookFunction[] = []
  private postHooks: PostHookFunction[] = []
  constructor(model: modelType, message: z.infer<typeof messages>, userId: string) {
    this.messages = message
    this.modelInfo = models[model]
    this.userId = userId
  }

  /**
   * 创建实例
   * @param model - 使用模型
   * @param message - 消息内容
   * @param userId - 用户ID
   * @returns 返回当前实例
   */
  public static async create(model: modelType, message: z.infer<typeof messages>, userId: string): Promise<LLM> {
    const instance = new LLM(model, message, userId)
    await instance.init()
    return instance
  }

  private async init() {
    const wallet = await userWallet.findUserWallet(this.userId)
    if (!wallet) {
      throw new Error('user wallet not found')
    }
    this.context = {
      userId: this.userId,
      modelInfo: this.modelInfo,
      messages: this.messages,
      wallet,
    }
    this.addPreHook(context => {
      return userWallet.checkWalletBalance(context)
    })
    this.addPostHook(async context => {
      await this.collectUsageInfo(context.wallet)
    })
  }

  /**
   * 添加预处理钩子
   * @param hook - 预处理函数
   * @returns 返回当前实例
   */
  public addPreHook(hook: PreHookFunction): this {
    this.preHooks.push(hook)
    return this
  }

  /**
   * 添加后处理钩子
   * @param hook - 后处理函数
   * @returns 返回当前实例
   */
  public addPostHook(hook: PostHookFunction): this {
    this.postHooks.push(hook)
    return this
  }

  private async executeHooks(hooks: PreHookFunction[] | PostHookFunction[], context: ContextType) {
    try {
      for (const hook of hooks) {
        const result = await hook(context)
        if (typeof result === 'boolean' && !result) return false
      }
      return true
    } catch (error) {
      // 对于特定的错误类型（如余额不足），直接重新抛出让上层处理
      if (error instanceof InsufficientBalanceError) {
        throw error
      }
      logger.error('hooks error', error)
      return false
    }
  }

  private async deepseek() {
    this.stream = await deepseekChat({
      message: this.messages,
      model: this.modelInfo,
    })
    return this
  }

  private async collectUsageInfo(wallet: WalletType | null) {
    if (!wallet) {
      throw new Error('user wallet not found')
    }
    this.stream?.getTotalUsageInfo().then(async usageInfo => {
      const tokenUsage = await updateTokenUsage(
        this.userId,
        { inputTokenRatio: 1, outputTokenRatio: 1, model: this.modelInfo.key },
        usageInfo?.promptTokens ?? 0,
        usageInfo?.completionTokens ?? 0,
      )
      await userWallet.updateWalletUsage(this.userId, tokenUsage.tokenCount)
    })
  }

  /**
   * 检查用户执行该任务 credits 是否足够
   * @param checkTaskConsumeCreditFn - 检查任务消耗 credits，如果未提供则默认检查剩余token需大于1000
   * @returns 返回当前实例
   */
  public withCreditCheck(checkTaskConsumeCreditFn?: (context: ContextType) => Promise<boolean>) {
    this.addPreHook(async context => {
      if (checkTaskConsumeCreditFn) return await checkTaskConsumeCreditFn(context)

      // Default check: require at least 1000 remaining tokens
      const wallet = context.wallet
      if (!wallet || wallet.remainingTokens < 1000) {
        throw new InsufficientBalanceError('账户余额不足')
      }
      return true
    })
    return this
  }

  /**
   * 添加提示词
   * @param message - 提示词
   * @returns 返回当前实例
   */
  public usePrompt(message?: z.infer<typeof messages>) {
    this.messages = [...(message ?? []), ...this.messages]
    return this
  }

  /**
   * 使用模型
   * @returns 返回流数据
   */
  public async useModel() {
    if (!this.context) throw new Error('context not found')

    const canContinue = await this.executeHooks(this.preHooks, this.context)
    if (!canContinue) return null

    if (this.modelInfo.type === 'deepseek') {
      await this.deepseek()
    }

    this.stream?.transformToString().then(async textContent => {
      const titleStartIdx = textContent.indexOf('#')
      let title = titleStartIdx >= 0 ? textContent.slice(titleStartIdx) : (textContent.trim().split('\n')[0] ?? '')
      title = title.replace(/^#+/g, '').split('\n')[0].trim()
    })

    await this.executeHooks(this.postHooks, this.context)

    return this.stream
  }
}
