import { getServerSession } from '#auth'
import { H3Event } from 'h3'

// 不需要认证的公开路由
const PUBLIC_ROUTES = ['/api/auth', '/api/sms', '/api/payment', '/api/user/reset-password', '/api/file']

export default defineEventHandler(async (event: H3Event) => {
  const path = event.path || event.node.req.url

  // 跳过公开路由的认证
  if (PUBLIC_ROUTES.some(route => path?.startsWith(route))) {
    return
  }

  // 只对 API 路由进行认证
  if (!path?.startsWith('/api')) {
    return
  }
  const session = await getServerSession(event)
  if (!session) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized',
      message: 'Please login first',
    })
  }
})
