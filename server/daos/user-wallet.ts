import { z } from 'zod'
import { modelInfoType } from '~/server/ai/constants'
import { config } from '~/server/config'
import { Wallet, WalletType } from '~/server/models/wallet.model'
import { messages } from '~/server/type'
import { InsufficientBalanceError } from '~/utils/error'

type ContextType = {
  userId: string
  modelInfo: modelInfoType
  messages: z.infer<typeof messages>
  wallet: WalletType | null
}

export type PlanKey = keyof typeof config.sku

export class UserWallet {
  // 查找用户钱包
  async findUserWallet(userId: string): Promise<WalletType | null> {
    return await Wallet.findOne({ userId })
  }

  // 更新钱包使用情况
  async updateWalletUsage(userId: string, totalUsedTokens: number): Promise<WalletType | null> {
    return await Wallet.findOneAndUpdate(
      {
        userId,
        remainingTokens: { $gte: totalUsedTokens },
      },
      {
        $inc: {
          totalUsedTokens: totalUsedTokens,
          remainingTokens: -totalUsedTokens,
        },
      },
      {
        new: true,
        runValidators: true,
      },
    )
  }

  // 检查用户余额
  async checkWalletBalance(context: ContextType): Promise<boolean> {
    const userWallet = context?.wallet
    if (!userWallet) {
      throw new Error('user wallet not found')
    }
    if (userWallet.remainingTokens <= 0) {
      throw new InsufficientBalanceError('credit not enough')
    }
    return true
  }

  async addTokenByPlanKey(userId: string, planKey: PlanKey) {
    const plan = config.sku[planKey]
    if (!plan) {
      throw new Error('plan-key not valid')
    }
    const wallet = await Wallet.findOneAndUpdate({ userId }, { $inc: { remainingTokens: plan.token } }, { new: true })
    if (!wallet) {
      throw new Error(`Wallet not found for user: ${userId}`)
    }
    return wallet
  }
}

export const userWallet = new UserWallet()
