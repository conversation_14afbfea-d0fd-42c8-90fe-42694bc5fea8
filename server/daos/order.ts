import { type OrderType, Order, OrderStatus } from '~/server/models/order.model'

export class OrderDao {
  // 创建订单
  async createOrder(orderData: Omit<OrderType, '_id' | 'createdAt' | 'updatedAt'>): Promise<OrderType> {
    return await Order.create(orderData)
  }

  // 根据 outTradeNo 查找订单
  async findOrderByOutTradeNo(outTradeNo: string): Promise<OrderType | null> {
    return await Order.findOne({ outTradeNo })
  }

  // 更新订单状态
  async updateOrderStatus(outTradeNo: string, status: OrderStatus): Promise<OrderType | null> {
    return await Order.findOneAndUpdate({ outTradeNo }, { $set: { status } }, { new: true })
  }

  // 获取用户订单列表
  async getUserOrders(userId: string, page: number = 1, limit: number = 10): Promise<{ orders: OrderType[]; total: number }> {
    const skip = (page - 1) * limit
    const [orders, total] = await Promise.all([Order.find({ userId }).sort({ createdAt: -1 }).skip(skip).limit(limit), Order.countDocuments({ userId })])
    return { orders, total }
  }
}

export const orderDao = new OrderDao()
