import { type OrderType, Order, OrderStatus } from '~/server/models/order.model'

export class UserOrder {
  // 创建订单
  async createOrder(orderData: Omit<OrderType, '_id' | 'createdAt' | 'updatedAt'>): Promise<OrderType> {
    try {
      // 数据验证
      if (!orderData.outTradeNo || !orderData.userId || !orderData.planKey || !orderData.amount || !orderData.paymentType) {
        throw new Error('缺少必要的订单信息')
      }

      // 业务逻辑验证
      if (orderData.amount <= 0) {
        throw new Error('订单金额必须大于0')
      }

      // 记录日志
      logger.info('创建订单', {
        outTradeNo: orderData.outTradeNo,
        userId: orderData.userId,
        planKey: orderData.planKey,
        amount: orderData.amount,
      })

      // 创建订单
      const order = await Order.create(orderData)

      // 记录成功日志
      logger.info('订单创建成功', { orderId: order._id })

      return order
    } catch (error) {
      // 错误处理
      logger.error('创建订单失败', {
        error: error instanceof Error ? error.message : '未知错误',
        orderData,
      })

      // 重新抛出错误，让调用者处理
      throw error
    }
  }

  // 根据 outTradeNo 查找订单
  async findOrderByOutTradeNo(outTradeNo: string): Promise<OrderType | null> {
    return await Order.findOne({ outTradeNo })
  }

  // 根据 tradeNo 查找订单
  async findOrderByTradeNo(tradeNo: string): Promise<OrderType | null> {
    return await Order.findOne({ tradeNo })
  }

  // 更新订单状态
  async updateOrderStatus(outTradeNo: string, status: OrderStatus, tradeNo?: string): Promise<OrderType | null> {
    const updateData: any = { status }
    if (tradeNo) {
      updateData.tradeNo = tradeNo
    }
    return await Order.findOneAndUpdate({ outTradeNo }, { $set: updateData }, { new: true })
  }

  // 获取用户订单列表
  async getUserOrders(userId: string, page: number = 1, limit: number = 10): Promise<{ orders: OrderType[]; total: number }> {
    const skip = (page - 1) * limit
    const [orders, total] = await Promise.all([Order.find({ userId }).sort({ createdAt: -1 }).skip(skip).limit(limit), Order.countDocuments({ userId })])
    return { orders, total }
  }
}

export const userOrder = new UserOrder()
