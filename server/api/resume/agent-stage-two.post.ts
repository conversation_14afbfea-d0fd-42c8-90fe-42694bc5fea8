import { H3Event, sendStream } from 'h3'
import { z } from 'zod'
import { LLM } from '~/server/ai'
import { resume_prompt } from '~/server/prompt-service/resume-agent'
import { defineRouteHand<PERSON> } from '~/server/utils/handler'
import { patchModel } from '~/server/utils/LLM'
import { respError, respSuccess } from '~/server/utils/response'
import { ErrorCode } from '~/utils/error-code'

const modelSchema = ['standard', 'professional'] as const

// 定义阶段一结果的schema
const stageOneResultSchema = z.object({
  analyzer_sop: z.any().optional(),
  diagnosis_sop: z.any().optional(), 
  suggestions_sop: z.any().optional(),
})

const schema = z.object({
  resumeText: z.string().min(1, '简历内容不能为空'),
  jobDescription: z.string().min(1, 'JD信息不能为空'),
  stageOneResult: stageOneResultSchema,
  model: z.enum(modelSchema).default('standard'),
})

export default defineRouteHandler({ auth: true, bodyRules: schema }, async (event: H3Event, { session, body }) => {
  const { resumeText, jobDescription, stageOneResult, model } = body
  const userId = session?.user?.id

  try {
    const llmModel = patchModel(model)

    // 准备阶段一的结果作为context
    const analyzer_sop_content = JSON.stringify(stageOneResult.analyzer_sop || {}, null, 2)
    const diagnosis_sop_content = JSON.stringify(stageOneResult.diagnosis_sop || {}, null, 2)  
    const suggestions_sop_content = JSON.stringify(stageOneResult.suggestions_sop || {}, null, 2)

    // 执行简历生成SOP
    console.log('开始执行简历生成SOP...')
    const resumeGenerationPrompt = resume_prompt({ 
      resumeText, 
      jobDescription,
      analyzer_sop_content,
      diagnosis_sop_content,
      suggestions_sop_content
    })
    
    const resumeLLM = await LLM.create(
      llmModel,
      [{ role: 'user', content: resumeGenerationPrompt }],
      userId,
    )

    const resumeResponse = await resumeLLM
      .withCreditCheck(async context => {
        const wallet = context.wallet
        if (wallet && wallet.remainingTokens >= 1500) {
          return true
        }
        return false
      })
      .usePrompt()
      .useModel()

    if (!resumeResponse) {
      throw new Error('简历生成SOP调用失败')
    }

    if (!resumeResponse) {
      throw new Error('简历生成SOP调用失败')
    }

    // 返回流式响应
    const rs = resumeResponse.toReadableStream()
    return sendStream(event, rs)

  } catch (error) {
    console.error('阶段二处理失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '未知错误',
      code: 500,
      data: {}
    }
  }
}) 