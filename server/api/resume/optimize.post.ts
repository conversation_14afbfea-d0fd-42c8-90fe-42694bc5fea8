import { H3Event, sendStream } from 'h3'
import { z } from 'zod'
import { LLM } from '~/server/ai'
import { ResumePromptType, useResumePrompt } from '~/server/prompt-service/resume'
import { handleApiError } from '~/server/utils/error-handler'
import { patchModel } from '~/server/utils/LLM'
import { ErrorCode } from '~/utils/error-code'

const resumeModuleSchema = ['default', 'workOrProject', 'education', 'skill', 'selfEvaluation', 'quantified', 'qualitative'] as const
const modelSchema = ['standard', 'professional'] as const
const languageSchema = ['zh-CN', 'zh-TW', 'en'] as const
const jobTypeSchema = ['general', 'tech', 'product', 'design', 'sales', 'marketing', 'operations', 'hr', 'finance', 'consulting', 'others'] as const

const schema = z.object({
  text: z.string(),
  model: z.enum(modelSchema).default('standard'), // 优化模式：标准优化还是专业优化
  resumeModule: z.enum(resumeModuleSchema).default('default'), // 选择优化的简历模块
  language: z.enum(languageSchema).default('zh-CN'), //输出语言
  jobType: z.enum(jobTypeSchema).default('general'), // 岗位类别
  jobInfo: z.string().optional(), // JD信息
})

export default defineRouteHandler({ auth: true, bodyRules: schema }, async (event: H3Event, { session, body }) => {
  const { text, model, resumeModule, language, jobType, jobInfo = '' } = body
  const userId = session?.user?.id
  
  try {
    const basePromptOptions = { text, jobInfo, countNumber: 500, lang: language, resumeModule, jobType }
    const finalPrompt = useResumePrompt(ResumePromptType.Optimize)(basePromptOptions)
    const llm = await LLM.create(
      patchModel(model),
      [
        {
          role: 'user',
          content: finalPrompt,
        },
      ],
      userId,
    )
    const stream = await llm.usePrompt().withCreditCheck().useModel()
    if (!stream) {
      throw new Error('Model invocation failed')
    }
    const rs = stream.toReadableStream()
    return sendStream(event, rs)
  } catch (error) {
    logger.error('Resume optimization failed', error)
    return handleApiError(event, error, ErrorCode.INTERNAL_SERVER_ERROR)
  }
})
