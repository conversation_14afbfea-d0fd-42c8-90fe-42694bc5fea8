import { H3Event } from 'h3'
import { z } from 'zod'
import { LLM } from '~/server/ai'
import { analyzer_sop, diagnosis_sop, suggestion_sop } from '~/server/prompt-service/resume-agent'
import { defineRouteHand<PERSON> } from '~/server/utils/handler'
import { patchModel } from '~/server/utils/LLM'
import { respError, respSuccess } from '~/server/utils/response'
import { ErrorCode } from '~/utils/error-code'

const modelSchema = ['standard', 'professional'] as const

const schema = z.object({
  resumeText: z.string().min(1, '简历内容不能为空'),
  jobDescription: z.string().min(1, 'JD信息不能为空'),
  model: z.enum(modelSchema).default('standard'),
})

interface SOPResult {
  analyzer_sop?: any
  diagnosis_sop?: any
  suggestions_sop?: any
}

export default defineRouteHandler({ auth: true, bodyRules: schema }, async (event: H3Event, { session, body }) => {
  const { resumeText, jobDescription, model } = body
  const userId = session?.user?.id

  try {
    const llmModel = patchModel(model)
    const result: SOPResult = {}

    // 阶段1: 执行分析SOP
    console.log('开始执行分析SOP...')
    const analyzerPrompt = analyzer_sop({ resumeText, jobDescription })
    
    const analyzerLLM = await LLM.create(
      llmModel,
      [{ role: 'system', content: analyzerPrompt }],
      userId,
    )

    const analyzerResponse = await analyzerLLM
      .withCreditCheck(async context => {
        const wallet = context.wallet
        if (wallet && wallet.remainingTokens >= 1000) {
          return true
        }
        return false
      })
      .usePrompt()
      .useModel()

    if (!analyzerResponse) {
      throw new Error('分析SOP调用失败')
    }

    const analyzerText = await analyzerResponse.transformToString()
    console.log('分析SOP结果:', analyzerText)
    
    // 尝试解析JSON响应
    let analyzerResult
    try {
      const jsonMatch = analyzerText.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        analyzerResult = JSON.parse(jsonMatch[0])
        result.analyzer_sop = analyzerResult.analyzer_sop || analyzerResult
      } else {
        // 如果无法解析JSON，使用原始文本
        result.analyzer_sop = { raw_response: analyzerText }
      }
    } catch (parseError) {
      console.warn('分析SOP JSON解析失败，使用原始响应:', parseError)
      result.analyzer_sop = { raw_response: analyzerText }
    }

    // 阶段2: 执行诊断SOP
    console.log('开始执行诊断SOP...')
    const diagnosisPrompt = diagnosis_sop({ 
      resumeText, 
      jobDescription, 
      analyzer_sop_content: analyzerText 
    })
    
    const diagnosisLLM = await LLM.create(
      llmModel,
      [{ role: 'system', content: diagnosisPrompt }],
      userId,
    )

    const diagnosisResponse = await diagnosisLLM
      .withCreditCheck(async context => {
        const wallet = context.wallet
        if (wallet && wallet.remainingTokens >= 1000) {
          return true
        }
        return false
      })
      .usePrompt()
      .useModel()

    if (!diagnosisResponse) {
      throw new Error('诊断SOP调用失败')
    }

    const diagnosisText = await diagnosisResponse.transformToString()
    console.log('诊断SOP结果:', diagnosisText)
    
    // 尝试解析JSON响应
    let diagnosisResult
    try {
      const jsonMatch = diagnosisText.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        diagnosisResult = JSON.parse(jsonMatch[0])
        result.diagnosis_sop = diagnosisResult.diagnosis_sop || diagnosisResult
      } else {
        result.diagnosis_sop = { raw_response: diagnosisText }
      }
    } catch (parseError) {
      console.warn('诊断SOP JSON解析失败，使用原始响应:', parseError)
      result.diagnosis_sop = { raw_response: diagnosisText }
    }

    // 阶段3: 执行建议SOP
    console.log('开始执行建议SOP...')
    const suggestionPrompt = suggestion_sop({ 
      resumeText, 
      jobDescription, 
      analyzer_sop_content: analyzerText,
      diagnosis_sop_content: diagnosisText
    })
    
    const suggestionLLM = await LLM.create(
      llmModel,
      [{ role: 'system', content: suggestionPrompt }],
      userId,
    )

    const suggestionResponse = await suggestionLLM
      .withCreditCheck(async context => {
        const wallet = context.wallet
        if (wallet && wallet.remainingTokens >= 1000) {
          return true
        }
        return false
      })
      .usePrompt()
      .useModel()

    if (!suggestionResponse) {
      throw new Error('建议SOP调用失败')
    }

    const suggestionText = await suggestionResponse.transformToString()
    console.log('建议SOP结果:', suggestionText)
    
    // 尝试解析JSON响应
    let suggestionResult
    try {
      const jsonMatch = suggestionText.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        suggestionResult = JSON.parse(jsonMatch[0])
        result.suggestions_sop = suggestionResult.suggestions_sop || suggestionResult
      } else {
        result.suggestions_sop = { raw_response: suggestionText }
      }
    } catch (parseError) {
      console.warn('建议SOP JSON解析失败，使用原始响应:', parseError)
      result.suggestions_sop = { raw_response: suggestionText }
    }

    // 返回符合 sop-res.json 中的格式
    return {
      success: true,
      message: 'success',
      code: 200,
      data: result
    }

  } catch (error) {
    console.error('阶段一处理失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '未知错误',
      code: 500,
      data: {}
    }
  }
}) 