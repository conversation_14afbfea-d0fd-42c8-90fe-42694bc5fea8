import { H3Event, sendStream } from 'h3'
import { z } from 'zod'
import { LLM } from '~/server/ai'
import { AiModel } from '~/server/ai/constants'
import { ResumePromptType, useResumePrompt } from '~/server/prompt-service/resume'
import { patchModel } from '~/server/utils/LLM'
import { respError } from '~/server/utils/response'
import { ErrorCode } from '~/utils/error-code'

const modelSchema = ['standard', 'professional'] as const
const languageSchema = ['zh-CN', 'zh-TW', 'en'] as const

const schema = z.object({
  text: z.string(), // 原文内容
  targetLang: z.enum(languageSchema).default('zh-CN'), // 目标语言
  model: z.enum(modelSchema).default('standard'), // 翻译模式
  jobInfo: z.string().optional(), // 可选的岗位信息，用于上下文参考
  countNumber: z.number().optional().default(500), // 输出字数限制
})

export default defineRouteHandler({ auth: true, bodyRules: schema }, async (event: H3Event, { session, body }) => {
  const { text, targetLang, model, jobInfo = '', countNumber } = body
  const userId = session.user.id
  try {
    const basePromptOptions = {
      text,
      jobInfo,
      countNumber,
      lang: targetLang,
    }
    const finalPrompt = useResumePrompt(ResumePromptType.I18n)(basePromptOptions)
    const llm = await LLM.create(
      patchModel(model),
      [
        {
          role: 'user',
          content: finalPrompt,
        },
      ],
      userId,
    )
    const stream = await llm.usePrompt().withCreditCheck().useModel()
    if (!stream) {
      throw new Error('翻译服务调用失败')
    }
    const rs = stream.toReadableStream()
    return sendStream(event, rs)
  } catch (error) {
    return respError(ErrorCode.BAD_REQUEST.code, ErrorCode.BAD_REQUEST.message, {
      error: error instanceof Error ? error.message : '翻译服务调用失败',
    })
  }
})
