import OpenAI from 'openai'
import { H3Event } from 'h3'
import logger from '~/server/utils/logger'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
})

const simulateDelay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export default defineEventHandler(async (event: H3Event) => {
  try {
    const formData = await readMultipartFormData(event)
    if (!formData?.[0]) {
      throw createError({
        statusCode: 400,
        message: '请上传音频文件',
      })
    }

    const audioFile = formData[0]
    if (!audioFile.type?.startsWith('audio/')) {
      throw createError({
        statusCode: 400,
        message: '请上传有效的音频文件',
      })
    }

    const file = new File([audioFile.data], 'audio.mp3', {
      type: audioFile.type,
    })

    let useMockTranscription = process.env.MOCK_TRANSCRIPTION === 'true'
    useMockTranscription = true
    
    if (useMockTranscription) {
      logger.debug('使用模拟转写模式')
      await simulateDelay(2000 + Math.random() * 2000)
      return {
        success: true,
        text: '这是一段模拟的转写文本。这段文本用于测试音频转写功能。',
      }
    }

    logger.info('开始音频转写', {
      fileSize: audioFile.data.length,
      fileType: audioFile.type,
    })

    const transcription = await openai.audio.transcriptions.create({
      file,
      model: 'whisper-1',
      language: 'zh',
      prompt: '请将音频内容转换为清晰的文字。要求:1)忽略背景噪音、语气词和口头禅;2)准确识别并保留所有专业术语和技术词汇;3)根据说话人的语气和停顿合理分段;4)保持语言表达的流畅性和逻辑性;5)如遇到英文单词和数字,保持原有形式不翻译',
    })

    logger.info('音频转写完成', {
      textLength: transcription.text.length,
    })

    return {
      success: true,
      text: transcription.text,
    }
  } catch (error: any) {
    logger.error('音频转写失败', {
      error: error.message,
      statusCode: error.statusCode,
    })
    
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || '音频转文字失败',
    })
  }
})
