import { defineEvent<PERSON>and<PERSON> } from 'h3'

export default defineEventHandler(async event => {
  try {
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
      },
      pid: process.pid,
    }
    return healthStatus
  } catch (error) {
    throw createError({
      statusCode: 500,
      statusMessage: 'Health check failed',
      data: {
        error: error instanceof Error ? error.message : 'Unknown error',
      },
    })
  }
})
