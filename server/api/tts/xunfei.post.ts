import { H3Event } from 'h3'
import { z } from 'zod'
import { ttsBillingConfigs } from '~/server/ai/constants'
import { config } from '~/server/config'
import { synthesizeSpeech, type XunfeiTtsBusinessParams } from '~/server/services/tts/xunfei.service'
import { voiceBillingService } from '~/server/services/voice-billing.service'
import { handleApiError } from '~/server/utils/error-handler'
import { defineRouteHandler } from '~/server/utils/handler'
import logger from '~/server/utils/logger'
import { ErrorCode } from '~/utils/error-code'

const tteEnumValues = ['GB2312', 'GBK', 'BIG5', 'UNICODE', 'GB18030', 'UTF8'] as const

const XunfeiTtsRequestSchema = z.object({
  text: z.string().min(1),
  speed: z.number().int().min(0).max(100).optional(),
  volume: z.number().int().min(0).max(100).optional(),
  pitch: z.number().int().min(0).max(100).optional(),
  aue: z.string().optional(),
  tte: z.enum(tteEnumValues).optional(),
})

export default defineRouteHandler(
  {
    bodyRules: XunfeiTtsRequestSchema,
    auth: true,
  },
  async (event: H3Event, { body, session }) => {
    try {
      await voiceBillingService.deductBalance({
        userId: session.user.id,
        count: 1,
        billingConfig: ttsBillingConfigs.xunfei,
      })

      const businessParams: XunfeiTtsBusinessParams = {
        aue: body.aue || 'lame',
        auf: body.aue === 'raw' ? 'audio/L16;rate=16000' : undefined,
        vcn: config.xunfeiTts.defaultVcn,
        speed: body.speed ?? 50,
        volume: body.volume ?? 50,
        pitch: body.pitch ?? 50,
        tte: body.tte || 'UTF8',
      }

      if (businessParams.aue === 'lame') {
        businessParams.sfl = 1
      }

      const audioBuffer = await synthesizeSpeech(body.text, businessParams)

      let contentType = 'application/octet-stream'
      if (businessParams.aue === 'lame') {
        contentType = 'audio/mpeg'
      } else if (businessParams.aue === 'raw') {
        contentType = 'audio/pcm'
      }

      event.node.res.setHeader('Content-Type', contentType)
      event.node.res.setHeader('Content-Disposition', `attachment; filename="speech.${businessParams.aue === 'lame' ? 'mp3' : 'pcm'}"`)

      return audioBuffer
    } catch (error: any) {
      logger.error('讯飞 TTS API 接口错误', {
        errorMessage: error.message,
        errorCode: error.code,
        stack: error.stack,
        requestBody: body,
      })
      return handleApiError(event, error, ErrorCode.AI_MODEL_CALL_FAILED)
    }
  },
)
