import { consola } from 'consola'
import type { H3Event } from 'h3'
import { z } from 'zod'
import { ttsBillingConfigs } from '~/server/ai/constants'
import { MiniMaxTtsService } from '~/server/services/tts/minimax.service'
import { voiceBillingService } from '~/server/services/voice-billing.service'
import { calculateCharacterCount } from '~/server/utils/character-counter'
import { handleApiError } from '~/server/utils/error-handler'
import type { AudioFormat, Bitrate, Emotion, LanguageBoost, PronunciationDict, SampleRate, SystemVoiceId, T2AModel } from '~/types/minimax'
import { ErrorCode } from '~/utils/error-code'

const logger = consola.withTag('api:tts:minimax')

// 系统音色列表
const minimaxVoices = [
  'male-qn-qingse', // 青涩青年音色
  'male-qn-jingying', // 精英青年音色
  'male-qn-badao', // 霸道青年音色
  'male-qn-daxuesheng', // 青年大学生音色
  'female-shaonv', // 少女音色
  'female-yujie', // 御姐音色
  'female-chengshu', // 成熟女性音色
  'female-tianmei', // 甜美女性音色
  'presenter_male', // 男性主持人
  'presenter_female', // 女性主持人
  'audiobook_male_1', // 男性有声书1
  'audiobook_male_2', // 男性有声书2
  'audiobook_female_1', // 女性有声书1
  'audiobook_female_2', // 女性有声书2
  'male-qn-qingse-jingpin', // 青涩青年音色-beta
  'male-qn-jingying-jingpin', // 精英青年音色-beta
  'male-qn-badao-jingpin', // 霸道青年音色-beta
  'male-qn-daxuesheng-jingpin', // 青年大学生音色-beta
  'female-shaonv-jingpin', // 少女音色-beta
  'female-yujie-jingpin', // 御姐音色-beta
  'female-chengshu-jingpin', // 成熟女性音色-beta
  'female-tianmei-jingpin', // 甜美女性音色-beta
  'clever_boy', // 聪明男童
  'cute_boy', // 可爱男童
  'lovely_girl', // 萌萌女童
  'cartoon_pig', // 卡通猪小琪
  'bingjiao_didi', // 病娇弟弟
  'junlang_nanyou', // 俊朗男友
  'chunzhen_xuedi', // 纯真学弟
  'lengdan_xiongzhang', // 冷淡学长
  'badao_shaoye', // 霸道少爷
  'tianxin_xiaoling', // 甜心小玲
  'qiaopi_mengmei', // 俏皮萌妹
  'wumei_yujie', // 妩媚御姐
  'diadia_xuemei', // 嗲嗲学妹
  'danya_xuejie', // 淡雅学姐
  'Santa_Claus', // Santa Claus
  'Grinch', // Grinch
  'Rudolph', // Rudolph
  'Arnold', // Arnold
  'Charming_Santa', // Charming Santa
  'Charming_Lady', // Charming Lady
  'Sweet_Girl', // Sweet Girl
  'Cute_Elf', // Cute Elf
  'Attractive_Girl', // Attractive Girl
  'Serene_Woman', // Serene Woman
] as const

// 模型列表
const minimaxModels = ['speech-02-hd', 'speech-02-turbo', 'speech-01-hd', 'speech-01-turbo', 'speech-01-240228', 'speech-01-turbo-240228'] as const

// 音频格式
const minimaxFormats = ['mp3', 'pcm', 'flac', 'wav'] as const

// 语言增强
const minimaxLanguageBoost = [
  'Chinese',
  'Chinese,Yue',
  'English',
  'Arabic',
  'Russian',
  'Spanish',
  'French',
  'Portuguese',
  'German',
  'Turkish',
  'Dutch',
  'Ukrainian',
  'Vietnamese',
  'Indonesian',
  'Japanese',
  'Italian',
  'Korean',
  'Thai',
  'Polish',
  'Romanian',
  'Greek',
  'Czech',
  'Finnish',
  'Hindi',
  'auto',
] as const

// 情绪类型
const minimaxEmotions = ['happy', 'sad', 'angry', 'fearful', 'disgusted', 'surprised', 'neutral'] as const

const minimaxRequestBodySchema = z.object({
  text: z.string().min(1, { message: 'Text 不能为空' }).max(10000, { message: 'Text 长度不能超过 10000 字符' }),
  model: z.enum(minimaxModels).optional(),
  voice_id: z.enum(minimaxVoices).optional(),
  format: z.enum(minimaxFormats).optional().default('mp3'),
  sample_rate: z
    .union([z.literal(8000), z.literal(16000), z.literal(22050), z.literal(24000), z.literal(32000), z.literal(44100)])
    .optional()
    .default(32000),
  bitrate: z
    .union([z.literal(32000), z.literal(64000), z.literal(128000), z.literal(256000)])
    .optional()
    .default(128000),
  channel: z
    .union([z.literal(1), z.literal(2)])
    .optional()
    .default(1),
  speed: z.number().min(0.5).max(2.0).optional().default(1.0),
  volume: z.number().min(0.1).max(10).optional().default(1.0),
  pitch: z.number().min(-12).max(12).optional().default(0),
  emotion: z.enum(minimaxEmotions).optional().default('happy'),
  language_boost: z.enum(minimaxLanguageBoost).optional().default('auto'),
  latex_read: z.boolean().optional().default(false),
  english_normalization: z.boolean().optional().default(false),
  subtitle_enable: z.boolean().optional().default(false),
  pronunciation_dict: z
    .object({
      tone: z.array(z.string()).optional(),
    })
    .optional(),
})

export default defineRouteHandler(
  {
    auth: true,
    bodyRules: minimaxRequestBodySchema,
  },
  async (event: H3Event, { body, session }) => {
    const {
      text,
      model,
      voice_id,
      format,
      sample_rate,
      bitrate,
      channel,
      speed,
      volume,
      pitch,
      emotion,
      language_boost,
      latex_read,
      english_normalization,
      subtitle_enable,
      pronunciation_dict,
    } = body

    const minimaxService = new MiniMaxTtsService()

    try {
      // 计算字符数并扣除余额
      const characterCount = calculateCharacterCount(text)
      const modelName = (model || minimaxService['defaultModel']) as keyof typeof ttsBillingConfigs.minimax.models
      const billingConfig = {
        characterCountBased: ttsBillingConfigs.minimax.models[modelName] || ttsBillingConfigs.minimax.models.default,
      }
      await voiceBillingService.deductBalance({
        userId: session.user.id,
        characterCount,
        billingConfig,
      })

      logger.info(
        `MiniMax 合成语音，模型: ${model || minimaxService['defaultModel']}, ` +
          `音色: ${voice_id || minimaxService['defaultVoiceId']}, ` +
          `格式: ${format}, ` +
          `采样率: ${sample_rate}, ` +
          `比特率: ${bitrate}, ` +
          `声道: ${channel}, ` +
          `语速: ${speed}, ` +
          `音量: ${volume}, ` +
          `音高: ${pitch}, ` +
          `情感: ${emotion}, ` +
          `语言增强: ${language_boost}, ` +
          `LaTeX 支持: ${latex_read}, ` +
          `英语规范化: ${english_normalization}, ` +
          `字幕: ${subtitle_enable}`,
      )

      const audioBuffer = await minimaxService.synthesizeSpeech(text, {
        model: model as T2AModel,
        voiceId: voice_id as SystemVoiceId,
        format: format as AudioFormat,
        sampleRate: sample_rate as SampleRate,
        bitrate: bitrate as Bitrate,
        channel,
        speed,
        volume,
        pitch,
        emotion: emotion as Emotion,
        languageBoost: language_boost as LanguageBoost,
        latexRead: latex_read,
        englishNormalization: english_normalization,
        subtitleEnable: subtitle_enable,
        pronunciationDict: pronunciation_dict as PronunciationDict,
      })

      let contentType = 'application/octet-stream'
      if (format === 'mp3') contentType = 'audio/mpeg'
      else if (format === 'wav') contentType = 'audio/wav'
      else if (format === 'pcm') contentType = 'audio/pcm'
      else if (format === 'flac') contentType = 'audio/flac'

      setResponseHeader(event, 'Content-Type', contentType)
      setResponseHeader(event, 'Content-Disposition', `attachment; filename="speech.${format}"`)

      return audioBuffer
    } catch (error: any) {
      logger.error(`MiniMax 语音合成失败: ${error.message}`, error)
      return handleApiError(event, error, ErrorCode.AI_MODEL_CALL_FAILED)
    }
  },
)
