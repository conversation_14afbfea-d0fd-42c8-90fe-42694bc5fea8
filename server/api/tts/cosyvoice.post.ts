import { consola } from 'consola'
import type { H3Event } from 'h3'
import { z } from 'zod'
import { ttsBillingConfigs } from '~/server/ai/constants'
import { CosyVoiceService } from '~/server/services/tts/cosyVoice.service'
import { voiceBillingService } from '~/server/services/voice-billing.service'
import { calculateCharacterCount } from '~/server/utils/character-counter'
import { handleApiError } from '~/server/utils/error-handler'
import type { SynthesisParameters } from '~/types/cosyvoice'
import { ErrorCode } from '~/utils/error-code'

const logger = consola.withTag('api:tts:cosyvoice')

const cosyVoiceV1Voices = [
  'longwan',
  'longcheng',
  'longhua',
  'longxiaochun',
  'longxiaoxia',
  'longxiaocheng',
  'longxiaobai',
  'longlaotie',
  'longshu',
  'longshuo',
  'longjing',
  'longmiao',
  'longyue',
  'longyuan',
  'longfei',
  'longjielidou',
  'longtong',
  'longxiang',
  'loongstella',
  'loongbella',
] as const

const cosyVoiceV2Voices = ['longcheng_v2', 'longhua_v2', 'longshu_v2', 'loongbella_v2', 'longwan_v2', 'longxiaochun_v2', 'longxiaoxia_v2'] as const

const allCosyVoices = [...cosyVoiceV1Voices, ...cosyVoiceV2Voices] as const
const sampleRateLiterals = [z.literal(8000), z.literal(16000), z.literal(22050), z.literal(24000), z.literal(44100), z.literal(48000)] as const

const cosyVoiceRequestBodySchema = z.object({
  texts: z.union([
    z.string().min(1, { message: 'Text cannot be empty' }),
    z.array(z.string().min(1, { message: 'Text in array cannot be empty' })).min(1, { message: 'Texts array cannot be empty' }),
  ]),
  model: z.enum(['cosyvoice-v1', 'cosyvoice-v2']).optional(),
  voice: z.enum(allCosyVoices),
  format: z.enum(['pcm', 'wav', 'mp3']).optional().default('mp3'),
  sample_rate: z.union(sampleRateLiterals).optional().default(22050),
  volume: z.number().min(0).max(100).optional().default(50),
  rate: z.number().min(0.5).max(2.0).optional().default(1.0),
  pitch: z.number().min(0.5).max(2.0).optional().default(1.0),
})

export default defineRouteHandler(
  {
    auth: true,
    bodyRules: cosyVoiceRequestBodySchema,
  },
  async (event: H3Event, { body, session }) => {
    const { texts, model, voice, format, sample_rate, volume, rate, pitch } = body
    const cosyVoiceService = new CosyVoiceService()

    try {
      const characterCount = Array.isArray(texts) ? texts.reduce((sum, text) => sum + calculateCharacterCount(text), 0) : calculateCharacterCount(texts)

      await voiceBillingService.deductBalance({
        userId: session.user.id,
        characterCount,
        billingConfig: ttsBillingConfigs.cosyvoice,
      })

      const synthesisParams: SynthesisParameters = {
        voice,
        format,
        sample_rate,
        volume,
        rate,
        pitch,
      }

      logger.info(`正在合成语音，模型: ${model || 'cosyvoice-v1 (默认)'}, 音色: ${voice}, 格式: ${format}`)

      const audioBuffer = await cosyVoiceService.synthesizeSpeech(texts, {
        model,
        parameters: synthesisParams,
      })

      let contentType = 'application/octet-stream'
      if (format === 'mp3') {
        contentType = 'audio/mpeg'
      } else if (format === 'wav') {
        contentType = 'audio/wav'
      } else if (format === 'pcm') {
        contentType = 'audio/pcm'
      }
      setResponseHeader(event, 'Content-Type', contentType)
      setResponseHeader(event, 'Content-Disposition', `attachment; filename="speech.${format}"`)

      return audioBuffer
    } catch (error: any) {
      logger.error(`语音合成失败，音色 ${voice}: ${error.message}`, error)
      return handleApiError(event, error, ErrorCode.AI_MODEL_CALL_FAILED)
    } finally {
      cosyVoiceService.close()
      logger.debug(`CosyVoiceService 已关闭，对应音色 ${voice} 的请求`)
    }
  },
)
