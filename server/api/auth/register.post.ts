import { ZodError } from 'zod'
import { registerUser } from '~/server/services/auth.service'
import { AppError } from '~/server/utils/error-handler'
import { defineRouteHandler } from '~/server/utils/handler'
import { respError, respSuccess } from '~/server/utils/response'
import { registerSchema, type RegisterInput } from '~/server/validators/auth.validator'
import { ErrorCode } from '~/utils/error-code'

export default defineRouteHandler(async event => {
  try {
    const body = await readBody(event)
    const validatedData: RegisterInput = registerSchema.parse(body)
    const user = await registerUser(validatedData)
    return respSuccess(user)
  } catch (error) {
    if (error instanceof ZodError) {
      return respError(ErrorCode.UNPROCESSABLE_ENTITY.code, '输入数据验证失败', { details: error.errors })
    }
    if (error instanceof AppError) {
      return respError(error.statusCode, error.message)
    }
    logger.error('注册过程中发生未知错误:', error)
    return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, ErrorCode.INTERNAL_SERVER_ERROR.message)
  }
})
