import { loginUser } from '~/server/services/auth.service'
import { respError, respSuccess } from '~/server/utils/response'
import { loginSchema } from '~/server/validators/auth.validator'
import { ErrorCode } from '~/utils/error-code'
import logger from '../../utils/logger'

export default defineRouteHandler({ auth: false, bodyRules: loginSchema }, async (event, { body }) => {
  try {
    const user = await loginUser(body)
    return respSuccess(user)
  } catch (error: any) {
    logger.error('登录失败', {
      error,
      ip: getRequestIP(event),
    })
    return respError(error.statusCode || ErrorCode.INTERNAL_SERVER_ERROR.code, error.message || ErrorCode.INTERNAL_SERVER_ERROR.displayMessage)
  }
})
