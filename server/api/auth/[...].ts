import { Nuxt<PERSON><PERSON><PERSON><PERSON><PERSON> } from '#auth'
import { User } from '~/server/models/user.model'
import { generateUsernameFromEmail } from '~/server/services/auth.service'

declare module '@sidebase/nuxt-auth' {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
    }
  }
}

interface Credentials {
  email: string
  password: string
}

export default NuxtAuthHandler({
  secret: process.env.NUXT_AUTH_SECRET || useRuntimeConfig().auth.secret,
  providers: [
    {
      id: 'credentials',
      name: 'Credentials',
      type: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials: any) {
        try {
          const user = await User.findOne({ email: credentials.email })
          if (!user) return null

          const isValid = await user.comparePassword(credentials.password)
          if (!isValid) return null

          return {
            id: user._id.toString(),
            name: user.name || user.username,
            email: user.email,
            image: user.image,
          }
        } catch (error) {
          console.error('Auth error:', error)
          return null
        }
      },
    },
    {
      id: 'github',
      name: '<PERSON>it<PERSON><PERSON>',
      type: 'oauth',
      authorization: {
        url: 'https://github.com/login/oauth/authorize',
        params: { scope: 'read:user user:email' },
      },
      token: 'https://github.com/login/oauth/access_token',
      userinfo: 'https://api.github.com/user',
      clientId: process.env.GITHUB_CLIENT_ID,
      clientSecret: process.env.GITHUB_CLIENT_SECRET,
      async profile(
        profile: {
          id: number
          login: string
          name?: string
          avatar_url: string
          email?: string
        },
        tokens: { access_token: string },
      ) {
        // 获取用户邮箱
        const emailResponse = await fetch('https://api.github.com/user/emails', {
          headers: {
            Authorization: `Bearer ${tokens.access_token}`,
          },
        })
        const emails = await emailResponse.json()
        const primaryEmail = emails.find((email: any) => email.primary)?.email || profile.email

        // 查找或创建用户
        let user = await User.findOne({ githubId: profile.id.toString() })

        if (!user) {
          // 检查邮箱是否已被使用
          user = await User.findOne({ email: primaryEmail })

          if (user) {
            // 如果用户存在但没有 githubId，更新用户信息
            user.githubId = profile.id.toString()
            user.name = profile.name || profile.login
            user.image = profile.avatar_url
            await user.save()
          } else {
            // 创建新用户
            user = await User.create({
              email: primaryEmail,
              username: generateUsernameFromEmail(primaryEmail),
              githubId: profile.id.toString(),
              name: profile.name || profile.login,
              image: profile.avatar_url,
            })
          }
        }

        return {
          id: user._id.toString(),
          name: user.name || user.username,
          email: user.email,
          image: user.image,
        }
      },
    },
  ],
  pages: {
    signIn: '/login',
  },
  callbacks: {
    async jwt({ token, user, account }: { token: any, user: any, account: any }) {
      if (user && account) {
        token.userId = user.id
      }
      return token
    },
    async session({ session, token }: { session: any, token: any }) {
      if (session.user && token.userId) {
        ;(session.user as any).id = token.userId as string
      }
      return session
    },
  },
})
