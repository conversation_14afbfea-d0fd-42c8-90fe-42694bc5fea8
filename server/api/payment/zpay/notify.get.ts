import { config } from '~/server/config'
import { userOrder } from '~/server/daos/user-order'
import { userWallet } from '~/server/daos/user-wallet'
import { respError, respSuccess } from '~/server/utils/response'
import { generateSign, getVerifyParams, parseAttachParams } from '~/server/utils/zpay'
import { ErrorCode } from '~/utils/error-code'

export default defineEventHandler(async event => {
  try {
    const query = getQuery(event)
    const { money, name, pid, out_trade_no, trade_no, trade_status, type, param, sign, sign_type } = query
    logger.info('z-pay notify: query params', query)
    const verifyParams = { pid, type, out_trade_no, trade_no, name, money, param, trade_status, sign_type }
    const { planKey, userId } = parseAttachParams(param as string) as { planKey: PlanKey; userId: string }
    const signStr = getVerifyParams(verifyParams)
    const generatedSign = generateSign(signStr, config.zpay.key)

    if (generatedSign !== sign) {
      logger.warn('z-pay notify: sign verify failed', { receivedSign: sign, generatedSign, signStr })
      return respError(ErrorCode.BAD_REQUEST.code, 'sign verify failed')
    }

    logger.info('z-pay notify: sign verify success', { out_trade_no, trade_no, trade_status })

    if (!planKey) return respError(ErrorCode.BAD_REQUEST.code, 'plan-key not defined')
    if (!userId) return respError(ErrorCode.BAD_REQUEST.code, 'user-id not defined')

    // 查找订单
    const order = await userOrder.findOrderByOutTradeNo(out_trade_no as string)
    if (!order) {
      logger.error('z-pay notify: order not found', { out_trade_no })
      return respError(ErrorCode.BAD_REQUEST.code, 'order not found')
    }
    // 如果订单已经是成功状态，直接返回成功
    if (order.status === 'SUCCESS') {
      logger.info('z-pay notify: order already processed', { out_trade_no })
      return respSuccess({
        message: 'This order is already handled successfully',
      })
    }

    // 更新订单状态
    await userOrder.updateOrderStatus(out_trade_no as string, 'SUCCESS')

    // 处理支付成功逻辑
    if (trade_status === 'TRADE_SUCCESS') {
      await userWallet.addTokenByPlanKey(userId, planKey)
    }

    return respSuccess({
      message: 'Payment successful',
    })
  } catch (error) {
    logger.error('z-pay notify: error', error)
    return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, 'handle payment notify error')
  }
})
