import { z } from 'zod'
import { config } from '~/server/config'
import { userOrder } from '~/server/daos/user-order'
import { respError, respSuccess } from '~/server/utils/response'
import { generateOutTradeNo, generateSign, getVerifyParams } from '~/server/utils/zpay'
import { ErrorCode } from '~/utils/error-code'

const CREATE_ORDER_URL = config.zpay.createOrderUrl
const NOTIFY_URL = config.zpay.notifyUrl

const planKeySchema = ['basic-credit', 'pro-credit', 'max-credit'] as const
const schema = z.object({
  planKey: z.enum(planKeySchema),
  userId: z.string().optional(),
})

const fetchSKUByPlanKey = (planKey: PlanKey) => {
  const sku = config.sku[planKey]
  if (!sku) return {}
  return {
    description: sku.name,
    name: sku.name,
    price: sku.price.toString(),
  }
}

export default defineRouteHandler({ auth: true, bodyRules: schema }, async (_, { session, body }) => {
  const { planKey } = body
  const userId = session.user.id
  const outTradeNo = generateOutTradeNo(userId)
  const { description, name, price } = fetchSKUByPlanKey(planKey)
  if (!description || !name || !price) {
    return respError(ErrorCode.BAD_REQUEST.code, 'invalid plan key')
  }
  logger.info(`description: ${description}, name: ${name}, price: ${price}`)
  const param = `planKey=${planKey}&userId=${userId}` //附加信息，回调时原样返回 planKey,uid
  const sign_type = 'MD5' //签名方式，仅支持MD5
  const type = 'wxpay' //支付方式，目前仅支持wxpay,后续会支持alipay
  const params = {
    pid: config.zpay.pid,
    out_trade_no: outTradeNo,
    description,
    notify_url: NOTIFY_URL,
    name,
    money: price,
    param,
    sign_type,
    type,
  }

  const signStr = getVerifyParams(params)
  const sign = generateSign(signStr, config.zpay.key)

  // 添加签名到参数中，确保所有值都是字符串
  const requestParams: Record<string, any> = { ...params, sign }
  try {
    const response = await $fetch(CREATE_ORDER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      // 将参数转换为 x-www-form-urlencoded 格式
      body: new URLSearchParams(requestParams).toString(),
    })
    if (!response) {
      return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, 'create payment order failed')
    }

    const responseData = typeof response === 'string' ? JSON.parse(response) : response
    // 创建系统订单记录
    await userOrder.createOrder({
      outTradeNo,
      userId,
      planKey,
      amount: Number(price) * 1000, // 单位为分
      status: 'PENDING',
      paymentType: type,
    })
    return respSuccess(responseData)
  } catch (error) {
    logger.error('create payment order failed:', error)
    return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, 'create payment order failed')
  }
})
