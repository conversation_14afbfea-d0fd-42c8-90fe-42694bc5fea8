import { z } from 'zod'
import { config } from '~/server/config'
import { ErrorCode } from '~/utils/error-code'

const schema = z.object({
  outTradeNo: z.string().min(1),
  trade_no: z.string().min(1),
  money: z.string().min(1),
})

const REFUND_API_URL = 'https://zpayz.cn/api.php?act=refund'

export default defineRouteHandler({ auth: false, bodyRules: schema }, async (event, { body }) => {
  try {
    const { outTradeNo, money, trade_no } = body
    logger.info(`申请退款: 订单号 = ${outTradeNo}, 金额 = ${money}`)
    // 构建退款请求参数
    const refundParams = {
      pid: config.zpay.pid,
      key: config.zpay.key,
      out_trade_no: outTradeNo,
      trade_no: trade_no,
      money: money.toString(),
    }
    const response = await $fetch(REFUND_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams(refundParams).toString(),
    })
    logger.info(`退款申请结果:`, response)
    try {
      const parsedResponse = typeof response === 'string' ? JSON.parse(response) : response
      return respSuccess(parsedResponse)
    } catch (parseError) {
      return respSuccess(response)
    }
  } catch (error) {
    logger.error('退款申请失败:', error)
    return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, ErrorCode.INTERNAL_SERVER_ERROR.displayMessage)
  }
})
