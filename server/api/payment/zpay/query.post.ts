import { H3Event } from 'h3'
import { z } from 'zod'
import { config } from '~/server/config'
import { userOrder } from '~/server/daos/user-order'
import { userWallet, type PlanKey } from '~/server/daos/user-wallet'
import { respError, respSuccess } from '~/server/utils/response'
import { parseAttachParams } from '~/server/utils/zpay'
import { ErrorCode } from '~/utils/error-code'

const schema = z.object({
  outTradeNo: z.string().min(1),
})

// z-pay query pay successful code status
const SUCCESS_CODE = '1'
const QUERY_API_URL = config.zpay.queryUrl

export default defineRouteHandler({ auth: false, bodyRules: schema }, async (event: H3Event, { body }) => {
  try {
    const { outTradeNo } = body
    // 先查询系统中的订单状态
    const order = await userOrder.findOrderByOutTradeNo(outTradeNo)
    logger.info('z-pay query: order message', { order })
    if (!order) {
      return respError(ErrorCode.BAD_REQUEST.code, 'order not found')
    }
    // 如果订单已经是成功状态，直接返回
    if (order.status === 'SUCCESS') {
      return respSuccess({
        out_trade_no: order.outTradeNo,
        message: 'This order is already handled successfully',
      })
    }
    // 否则查询第三方支付平台
    const queryParams = {
      act: 'order',
      pid: config.zpay.pid,
      key: config.zpay.key,
      out_trade_no: outTradeNo,
    }
    const queryUrl = `${QUERY_API_URL}?${new URLSearchParams(queryParams).toString()}`
    const response = await $fetch(queryUrl, { method: 'GET', headers: { 'Content-Type': 'application/json' } })

    if (!response) {
      return respError(ErrorCode.BAD_REQUEST.code, 'order query failed, please try again later')
    }

    logger.info('z-pay query: raw response', { response })
    const parsedResponse = typeof response === 'string' ? JSON.parse(response) : {}
    const { param } = parsedResponse

    if (!param) {
      logger.error('z-pay query: param is missing or invalid', { parsedResponse })
      return respError(ErrorCode.BAD_REQUEST.code, 'Invalid response from payment platform')
    }

    const { userId, planKey } = parseAttachParams(param) as { planKey: PlanKey; userId: string }

    // 如果支付成功，更新订单状态
    if (parsedResponse.code === SUCCESS_CODE && parsedResponse.status === SUCCESS_CODE) {
      await userWallet.addTokenByPlanKey(userId, planKey)
      await userOrder.updateOrderStatus(outTradeNo, 'SUCCESS', parsedResponse.trade_no)
      return respSuccess({
        out_trade_no: order.outTradeNo,
        message: 'This order is already handled successfully',
      })
    }
    return respError(ErrorCode.BAD_REQUEST.code, 'order query failed, please try again later')
  } catch (error) {
    logger.error('z-pay query: order query failed', error)
    return respError(ErrorCode.BAD_REQUEST.code, error instanceof Error ? error.message : 'order query failed, please try again later')
  }
})
