import { randomUUID } from 'crypto'
import fs from 'fs'
import { createError } from 'h3'
import OpenAI from 'openai'
import os from 'os'
import path from 'path'
import { fileToMd } from '~/server/prompt-service/resume'
import { ErrorCode } from '~/utils/error-code'
export default defineEventHandler(async event => {
  try {
    // const moonshotApiKey =
    const moonshotApiKey = 'sk-nxOUna32YSbfHqatVCeu1nLtx705pOCoFI1CYc77EkPDoG1P'

    if (!moonshotApiKey) {
      throw createError({ statusCode: 500, statusMessage: 'Moonshot API Key 未配置' })
    }

    // 创建 OpenAI 客户端
    const client = new OpenAI({
      apiKey: moonshotApiKey,
      baseURL: 'https://api.moonshot.cn/v1',
    })

    // 限制请求大小为2MB
    const bodyLimit = 2 * 1024 * 1024 // 2MB

    // 解析formData
    const formData = await readMultipartFormData(event)

    if (!formData || formData.length === 0) {
      throw createError({ statusCode: 400, statusMessage: '未检测到上传文件' })
    }

    // 获取文件部分
    const fileData = formData.find(
      part =>
        part.name === 'file' &&
        ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(part.type || ''),
    )

    if (!fileData || !fileData.data) {
      throw createError({ statusCode: 400, statusMessage: '请上传有效的PDF或Word文档' })
    }

    // 检查文件大小
    if (fileData.data.length > bodyLimit) {
      throw createError({ statusCode: 413, statusMessage: '文件大小超过2MB限制' })
    }

    // 创建临时文件 - 解决类型问题
    const tempFilePath = path.join(os.tmpdir(), `${randomUUID()}.pdf`)
    fs.writeFileSync(tempFilePath, fileData.data)

    try {
      // 使用fs.createReadStream创建符合Uploadable类型的文件流
      const fileStream = fs.createReadStream(tempFilePath)

      // 上传文件到 Moonshot
      const fileObject = await client.files.create({
        file: fileStream,
        purpose: 'file-extract' as any,
      })

      // 获取文件内容
      const fileContent = await (await client.files.content(fileObject.id)).text()

      // 使用 AI 将内容转换为 Markdown
      const completion = await client.chat.completions.create({
        model: 'moonshot-v1-32k',
        messages: [
          {
            role: 'system',
            content: fileToMd(),
          },
          {
            role: 'user',
            content: fileContent,
          },
        ],
        temperature: 0.3,
      })

      return {
        success: true,
        markdown: completion.choices[0].message.content,
      }
    } finally {
      // 清理临时文件
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath)
      }
    }
  } catch (error: any) {
    console.error('PDF 解析错误:', error)
    return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, ErrorCode.INTERNAL_SERVER_ERROR.displayMessage)
  }
})
