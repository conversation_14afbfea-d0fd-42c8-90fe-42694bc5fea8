import { randomUUID } from 'crypto'
import { createError } from 'h3'
import OpenAI from 'openai'
import { BUCKETS, ensureBucketExists, getPublicUrl, uploadFile } from '~/server/s3'
import { ErrorCode } from '~/utils/error-code'

export default defineEventHandler(async event => {
  try {
    const openRouterApiKey = process.env.DEEPSEEK_API_KEY

    if (!openRouterApiKey) {
      throw createError({ statusCode: 500, statusMessage: 'OpenRouter API Key 未配置' })
    }

    // 创建 OpenAI 客户端
    const client = new OpenAI({
      apiKey: openRouterApiKey,
      baseURL: 'https://openrouter.ai/api/v1',
      defaultHeaders: {
        'HTTP-Referer': process.env.APP_URL || 'http://localhost:3000',
        'X-Title': 'Hi-Offer App',
      },
    })

    // 限制请求大小为5MB
    const bodyLimit = 5 * 1024 * 1024

    // 解析formData
    const formData = await readMultipartFormData(event)

    if (!formData || formData.length === 0) {
      throw createError({ statusCode: 400, statusMessage: '未检测到上传文件' })
    }

    // 获取文件部分
    const fileData = formData.find(
      part => part.name === 'file' && ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'].includes(part.type || ''),
    )

    if (!fileData || !fileData.data) {
      throw createError({ statusCode: 400, statusMessage: '请上传有效的图片文件' })
    }

    // 检查文件大小
    if (fileData.data.length > bodyLimit) {
      throw createError({ statusCode: 413, statusMessage: '文件大小超过5MB限制' })
    }

    // 确保桶是公开访问的
    await ensureBucketExists(BUCKETS.RESUMES, true)

    // 生成唯一文件名和文件扩展名
    const fileType = fileData.type || 'image/jpeg'
    const fileExt = fileType.split('/')[1]
    const fileName = `${randomUUID()}.${fileExt}`

    // 上传文件到 Supabase Storage
    // const { error: uploadError, data: uploadData } = await uploadFile(BUCKETS.RESUMES, fileName, fileData.data, fileType)

    // if (uploadError) {
    //   throw createError({
    //     statusCode: 500,
    //     statusMessage: `上传文件到 Supabase 失败: ${uploadError.message}`,
    //   })
    // }

    // 获取图片的公共 URL
    // const { data: publicUrlData } = getPublicUrl(BUCKETS.RESUMES, fileName)

    // if (!publicUrlData || !publicUrlData.publicUrl) {
    //   throw createError({
    //     statusCode: 500,
    //     statusMessage: '获取公共 URL 失败',
    //   })
    // }

    // const imageUrl = publicUrlData.publicUrl

    const base64Image = `data:${fileType};base64,${fileData.data.toString('base64')}`

    const prompt = `
# 角色：你是一个专业的图片内容识别与转换助手
# 任务：
- 充分发挥你的图像识别、OCR能力，分析图片内容
- 将图片中的文本和视觉内容转换为结构化的 Markdown 文本
# 要求：
- 识别图片中的文本内容，并保留其结构、标题、段落等
- 描述图片中的重要视觉元素（如图表、示意图等）
- 分析图片内容的上下文关系，提取关键信息
- 转换后的内容清晰易读，并遵循 Markdown 语法规范
- 输出结果为纯字符串，不要包含任何附加说明或解释
`

    // 使用 Qwen2.5 VL 3B 模型处理图片内容并转换为 Markdown
    const completion = await client.chat.completions.create({
      model: 'opengvlab/internvl3-14b:free',
      messages: [
        {
          role: 'system',
          content: prompt,
        },
        {
          role: 'user',
          content: [
            { type: 'text', text: '请分析这张图片并将内容转换为 Markdown 格式' },
            { type: 'image_url', image_url: { url: base64Image } },
          ],
        },
      ],
      temperature: 0.3,
    })

    return {
      success: true,
      markdown: completion.choices[0].message.content,
    }
  } catch (error: any) {
    return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, ErrorCode.INTERNAL_SERVER_ERROR.displayMessage)
  }
})
