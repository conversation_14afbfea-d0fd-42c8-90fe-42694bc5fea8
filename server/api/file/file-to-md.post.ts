import pdf2md from '@opendocsg/pdf2md'
import { createError, H3Event } from 'h3'
import { ErrorCode } from '~/utils/error-code'

export default defineRouteHandler({ auth: false }, async (event: H3Event) => {
  try {
    const formData = await readMultipartFormData(event)
    if (!formData || formData.length === 0) {
      throw createError({ statusCode: 400, statusMessage: '未检测到上传文件' })
    }
    const fileData = formData.find(
      part =>
        part.name === 'file' &&
        ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(part.type || ''),
    )
    if (!fileData || !fileData.data) {
      throw createError({ statusCode: 400, statusMessage: 'please upload a valid PDF or Word document' })
    }

    if (fileData.data.length > 5 * 1024 * 1024) {
      throw createError({ statusCode: 413, statusMessage: 'file size exceeds 5MB limit' })
    }
    let fileBuffer: Buffer | ArrayBuffer = fileData.data
    const callbacks = {
      pageParsed: (pages: any[]) => {
        logger.info(`already parsed ${pages.length} pages`)
      },
      documentParsed: (document: any, pages: any[]) => {
        logger.info(`document parsed, ${pages.length} pages`)
      },
    }
    // 使用pdf2md库将PDF转换为Markdown , LLM 处理太慢
    const markdown = await pdf2md(fileBuffer, callbacks)
    return respSuccess({
      message: 'parse file to markdown successfully',
      markdown,
    })
  } catch (error: any) {
    logger.error('parse file to markdown error:', error?.message)
    return respError(ErrorCode.BAD_REQUEST.code, error?.message || ErrorCode.BAD_REQUEST.message, {
      errorStack: error,
    })
  }
})
