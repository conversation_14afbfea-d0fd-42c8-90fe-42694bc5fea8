import { randomUUID } from 'crypto'
import { BUCKETS, getSignedUrl, removeFile, uploadFile } from '~/server/s3'
import { ErrorCode } from '~/utils/error-code'

export default defineEventHandler(async event => {
  try {
    const bodyLimit = 5 * 1024 * 1024 // 5MB
    const formData = await readMultipartFormData(event)

    if (!formData || formData.length === 0) {
      return respError(ErrorCode.BAD_REQUEST.code, ErrorCode.BAD_REQUEST.displayMessage)
    }

    const fileData = formData.find(
      part =>
        part.name === 'file' &&
        ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(part.type || ''),
    )

    if (!fileData || !fileData.data) {
      return respError(ErrorCode.BAD_REQUEST.code, ErrorCode.BAD_REQUEST.displayMessage)
    }

    // 检查文件大小
    if (fileData.data.length > bodyLimit) {
      return respError(413, 'File size exceeds limit')
    }

    // 生成唯一文件名和文件扩展名
    const fileExt = fileData.type === 'application/pdf' ? 'pdf' : 'docx'
    const fileName = `${randomUUID()}.${fileExt}`

    // 上传文件到 Supabase Storage - 直接使用 Buffer/Uint8Array 数据
    const { error: uploadError } = await uploadFile(BUCKETS.RESUMES, fileName, fileData.data, fileData.type || 'application/pdf')
    if (uploadError) {
      return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, ErrorCode.INTERNAL_SERVER_ERROR.displayMessage)
    }

    // 创建临时签名 URL 以便从 Supabase 获取文件
    const signedUrlResult = await getSignedUrl(BUCKETS.RESUMES, fileName)

    if (!signedUrlResult.data?.signedUrl) {
      return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, ErrorCode.INTERNAL_SERVER_ERROR.displayMessage)
    }

    return respSuccess({
      message: 'file uploaded successfully',
      signedUrl: signedUrlResult.data.signedUrl,
    })
  } catch (error: any) {
    return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, ErrorCode.INTERNAL_SERVER_ERROR.displayMessage)
  }
})
