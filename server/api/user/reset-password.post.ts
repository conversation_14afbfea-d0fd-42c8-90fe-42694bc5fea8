import { H3Event } from 'h3'
import { z } from 'zod'
import { User } from '~/server/models/user.model'
import { getMailCodeRecord, removeMailCodeRecord } from '~/server/redis/email'

const schema = z.object({
  email: z.string().email('email format is incorrect'),
  code: z.string().length(6, 'verification code must be 6 digits'),
  password: z.string().min(6, 'password must be at least 6 digits'),
})

export default defineRouteHandler({ auth: false, bodyRules: schema }, async (event: H3Event, { body }) => {
  const { email, code, password } = body

  try {
    // 验证验证码
    const record = await getMailCodeRecord(email, code)
    if (!record) {
      return respError(400, 'verification code invalid or expired')
    }

    // 查找用户
    const user = await User.findOne({ email: email.toLowerCase() })

    if (!user) {
      return respError(404, 'user not found')
    }

    // 更新密码
    // 注意：直接设置 password 属性会触发 userSchema.pre('save') 中间件
    // 该中间件会自动使用 bcrypt 对密码进行加密
    user.password = password

    // 保存用户信息，此时会触发密码加密中间件
    await user.save()

    // 验证成功后删除验证码
    await removeMailCodeRecord(email)

    return respSuccess({
      message: 'password reset successfully',
    })
  } catch (error) {
    logger.error('Reset password error:', error)
    // 检查错误类型，提供更具体的错误信息
    if (error instanceof Error) {
      if (error.message.includes('No document found for query')) {
        return respError(404, 'user not found, please check your email')
      }
    }

    return respError(500, error instanceof Error ? error.message : 'password reset failed')
  }
})
