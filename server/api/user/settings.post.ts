import { H3Event, readBody } from 'h3'
import { User } from '~/server/models/user.model'
import { defineRouteHand<PERSON> } from '~/server/utils/handler'
import logger from '~/server/utils/logger'
import { respError, respSuccess } from '~/server/utils/response'
import { updateUserSettingsSchema } from '~/server/validators/user.validator'
import { ErrorCode } from '~/utils/error-code'

export default defineRouteHandler(
  { auth: true },
  async (event: H3Event, { session }) => {
    const body = await readBody(event)
    const userId = session?.user?.id

    const result = updateUserSettingsSchema.safeParse(body)

    if (!result.success) {
      logger.warn('用户设置更新数据验证失败', {
        userId,
        validationError: result.error.errors,
      })
      return respError(ErrorCode.UNPROCESSABLE_ENTITY.code, ErrorCode.UNPROCESSABLE_ENTITY.message)
    }

    const validatedData = result.data
    const updatedUser = await User.findByIdAndUpdate(userId, { $set: validatedData }, { new: true, runValidators: true }).select('-password')

    if (!updatedUser) {
      logger.error('用户设置更新失败：用户不存在', { userId })
      return respError(ErrorCode.USER_NOT_FOUND.code, ErrorCode.USER_NOT_FOUND.message)
    }

    return respSuccess(updatedUser)
  }
)
