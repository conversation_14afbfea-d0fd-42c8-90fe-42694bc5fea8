import { H3Event } from 'h3'
import { User } from '~/server/models/user.model'
import { defineRouteHandler } from '~/server/utils/handler'
import logger from '~/server/utils/logger'
import { respError, respSuccess } from '~/server/utils/response'
import { ErrorCode } from '~/utils/error-code'

export default defineRouteHandler(
  { auth: true },
  async (ev: H3Event, { session }) => {
    const userId = session?.user?.id
    const user = await User.findById(userId).select('-password')
    if (!user) {
      logger.error('获取用户信息失败：用户不存在', { userId })
      return respError(ErrorCode.USER_NOT_FOUND.code, ErrorCode.USER_NOT_FOUND.message)
    }
    return respSuccess(user)
  }
)