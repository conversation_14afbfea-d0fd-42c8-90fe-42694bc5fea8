import { H3Event } from 'h3'
import { Wallet } from '~/server/models/wallet.model'
import { respError, respSuccess } from '~/server/utils/response'
import { CREDIT_UNIT } from '~/server/config'
import { ErrorCode } from '~/utils/error-code'
export default defineRouteHandler({ auth: true }, async (event: H3Event, { session }) => {
  const userId = session?.user?.id
  const wallet = await Wallet.findOne({ userId })
  if (!wallet) {
    throw respError(ErrorCode.NOT_FOUND.code, ErrorCode.NOT_FOUND.message, {
      error: 'can not find wallet info',
    })
  }
  const { remainingTokens, totalUsedTokens } = wallet
  const remainingCredits = Math.floor(remainingTokens / CREDIT_UNIT)
  const result = { remainingCredits }
  return respSuccess(result)
})
