import { H3Event } from 'h3'
import { z } from 'zod'
import { LLM } from '~/server/ai'
import { AiModel } from '~/server/ai/constants'
import { useInterviewEvaluation } from '~/server/prompt-service/interview-evaluation'
import { handleApiError } from '~/server/utils/error-handler'
import { respError, respSuccess } from '~/server/utils/response'
import type { InterviewType } from '~/types/interview'
import { ErrorCode } from '~/utils/error-code'

const schema = z.object({
  messages: z.array(
    z.object({
      role: z.enum(['user', 'assistant', 'interviewer']),
      content: z.string(),
    }),
  ),
  type: z.enum([
    'frontend',
    'backend',
    'fullstack',
    'algorithm',
    'new-media-operation',
    'product-manager',
    'sales-specialist',
    'marketing',
    'test-engineer',
    'ui-designer',
    'project-manager',
    'customer-service',
    'human-resource-specialist',
  ]) as z.ZodType<InterviewType>,
  resumeText: z.string().optional(),
  jobInfo: z.string().optional(),
})

export default defineRouteHandler({ auth: true, bodyRules: schema }, async (event: H3Event, { session, body }) => {
  const { messages, type, resumeText = '', jobInfo = '' } = body
  const userId = session.user.id

  try {
    const { generateEvaluationPrompt } = useInterviewEvaluation()
    const prompt = generateEvaluationPrompt(type)

    // 过滤空消息
    const messagesArray = messages.filter(msg => msg.content.trim() !== '')

    // 分离用户回答和AI回答（面试官问题和AI建议）
    const userMessages = messagesArray.filter(msg => msg.role === 'user')
    const interviewerMessages = messagesArray.filter(msg => msg.role === 'interviewer')
    const aiSuggestionMessages = messagesArray.filter(msg => msg.role === 'assistant')

    // 构建评价上下文
    const evaluationContext = {
      interview_conversation: messagesArray,
      user_answers: userMessages,
      interviewer_questions: interviewerMessages,
      ai_suggestions: aiSuggestionMessages,
      resume_background: resumeText,
      job_requirements: jobInfo,
      position_type: type,
    }

    const llm = await LLM.create(
      AiModel.Deepseek_V3,
      [
        {
          role: 'user',
          content: prompt,
        },
      ],
      userId,
    )

    const contextContent = JSON.stringify(evaluationContext, null, 2)

    const stream = await llm
      .usePrompt([
        {
          role: 'user',
          content: `请基于以下面试数据生成评价：\n\n${contextContent}`,
        },
      ])
      .withCreditCheck()
      .useModel()

    if (!stream) {
      return respError(ErrorCode.AI_MODEL_CALL_FAILED.code, ErrorCode.AI_MODEL_CALL_FAILED.displayMessage)
    }

    const textContent = await stream.transformToString()

    let evaluation
    try {
      // 清理可能的markdown格式包装
      const cleanedContent = textContent
        .trim()
        .replace(/^```json\s*/, '')
        .replace(/\s*```$/, '')
      evaluation = JSON.parse(cleanedContent)
    } catch (parseError) {
      logger.error('Failed to parse evaluation JSON:', parseError)
      logger.error('Raw content:', textContent)
      return respError(ErrorCode.EVALUATION_GENERATION_FAILED.code, '评价结果解析失败')
    }

    return respSuccess({
      message: 'interview evaluation generated successfully',
      evaluation,
    })
  } catch (error) {
    logger.error('generate evaluation failed:', error)
    return handleApiError(event, error, ErrorCode.EVALUATION_GENERATION_FAILED)
  }
})
