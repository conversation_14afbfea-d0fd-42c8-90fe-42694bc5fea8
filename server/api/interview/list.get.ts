import { H3Event } from 'h3'
import { Interview } from '~/server/models/interview.model'
import { define<PERSON><PERSON>e<PERSON><PERSON><PERSON> } from '~/server/utils/handler'
import { respSuccess } from '~/server/utils/response'
import { getInterviewListSchema } from '~/server/validators/interview.validator'

const PROJECTION = {
  type: 1,
  interviewer: 1,
  duration: 1,
  date: 1,
  'evaluation.overallScore': 1,
  createdAt: 1,
}

export default defineRouteHandler({ auth: true }, async (ev: H3Event, { session }) => {
  const { page = 1, limit = 10 } = await getValidatedQuery(ev, (query: any) =>
    getInterviewListSchema.parse({
      page: Number(query.page) || 1,
      limit: Number(query.limit) || 10,
    }),
  )

  const [result] = await Interview.aggregate([
    { $match: { userId: session.user.id } },
    { $sort: { createdAt: -1 } },
    {
      $facet: {
        paginatedResults: [{ $skip: (page - 1) * limit }, { $limit: limit }, { $project: PROJECTION }],
        totalCount: [{ $count: 'total' }],
      },
    },
  ])

  return respSuccess({
    list: result.paginatedResults,
    pagination: {
      page,
      limit,
      total: result.totalCount[0]?.total || 0,
    },
  })
})
