import { Interview } from '~/server/models/interview.model'
import { handleError } from '~/server/utils/error-handler'

const POSITION_COLORS = {
  frontend: 'bg-indigo-500',
  backend: 'bg-emerald-500',
  fullstack: 'bg-violet-500',
  algorithm: 'bg-amber-500',
}

const POSITION_NAMES = {
  frontend: '前端开发',
  backend: '后端开发',
  fullstack: '全栈开发',
  algorithm: '算法开发',
}

export default defineRouteHandler({ auth: true }, async (_, { session }) => {
  try {
    const userId = session.user.id

    // 获取所有面试记录
    const interviews = await Interview.find({ userId }).lean()

    // 计算今日面试次数
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000)

    const todayInterviews = interviews.filter(interview => {
      const interviewDate = new Date(interview.date)
      return interviewDate >= startOfDay && interviewDate < endOfDay
    })

    // 计算基础统计数据
    const totalInterviews = interviews.length
    const todayInterviewsCount = todayInterviews.length
    const averageScore = interviews.length ? Math.round(interviews.reduce((acc, curr) => acc + (curr.evaluation?.overallScore || 0), 0) / interviews.length) : 0

    // 计算岗位分布
    const positionCounts = interviews.reduce(
      (acc, curr) => {
        acc[curr.type] = (acc[curr.type] || 0) + 1
        return acc
      },
      {} as Record<string, number>,
    )

    const positionDistribution = Object.entries(positionCounts).map(([type, count]) => ({
      name: POSITION_NAMES[type as keyof typeof POSITION_NAMES] || type,
      percentage: Math.round((count / totalInterviews) * 100),
      color: POSITION_COLORS[type as keyof typeof POSITION_COLORS] || 'bg-gray-500',
    }))

    // 计算最近6个月的得分趋势
    const now = new Date()
    const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 5, 1)

    const monthlyScores = interviews
      .filter(interview => new Date(interview.date) >= sixMonthsAgo)
      .reduce(
        (acc, interview) => {
          const date = new Date(interview.date)
          const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`
          if (!acc[monthKey]) {
            acc[monthKey] = { total: 0, count: 0 }
          }
          acc[monthKey].total += interview.evaluation?.overallScore || 0
          acc[monthKey].count++
          return acc
        },
        {} as Record<string, { total: number; count: number }>,
      )

    const scoresTrend = {
      labels: [] as string[],
      data: [] as number[],
    }

    for (let i = 0; i < 6; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`
      const monthName = date.toLocaleString('zh-CN', { month: 'long' })

      scoresTrend.labels.unshift(monthName)
      scoresTrend.data.unshift(monthlyScores[monthKey] ? Math.round(monthlyScores[monthKey].total / monthlyScores[monthKey].count) : 0)
    }

    return respSuccess({
      totalInterviews,
      todayInterviews: todayInterviewsCount,
      averageScore,
      positionDistribution,
      scoresTrend,
    })
  } catch (error) {
    return handleError(error)
  }
})
