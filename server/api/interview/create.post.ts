import { Interview } from '~/server/models/interview.model'
import { handleError } from '~/server/utils/error-handler'
import logger from '~/server/utils/logger'
import { createInterviewSchema } from '~/server/validators/interview.validator'

export default defineRouteHandler({ auth: true, bodyRules: createInterviewSchema }, async (event, { body, session }) => {
  try {
    // 创建面试记录
    const interview = await Interview.create({
      userId: session.user.id,
      ...body,
    })

    logger.info('创建面试记录成功', {
      userId: session.user.id,
      interviewId: interview._id,
      type: interview.type,
    })

    return respSuccess(interview)
  } catch (error: any) {
    logger.error('创建面试记录失败', {
      error: error.message,
      userId: session.user.id,
      timestamp: new Date().toISOString(),
      path: event.path,
    })

    return handleError(error)
  }
})
