import { Interview } from '~/server/models/interview.model'
import { handleError } from '~/server/utils/error-handler'
import { getInterviewDetailSchema } from '~/server/validators/interview.validator'
import { ErrorCode } from '~/utils/error-code'

export default defineRouteHandler({ auth: true, queryRules: getInterviewDetailSchema }, async (_, { query, session }) => {
  try {
    const interview = await Interview.findOne({
      _id: query.id,
      userId: session.user.id,
    }).lean()

    if (!interview) {
      return respError(ErrorCode.NOT_FOUND.code, ErrorCode.NOT_FOUND.displayMessage)
    }

    return respSuccess(interview)
  } catch (error) {
    return handleError(error)
  }
})
