import { sendStream } from 'h3'
import { z } from 'zod'
import { LLM } from '~/server/ai'
import { AiModel } from '~/server/ai/constants'
import { usePrompts } from '~/server/prompt-service/interview'
import { define<PERSON>out<PERSON><PERSON><PERSON><PERSON> } from '~/server/utils/handler'
import type { InterviewStage, InterviewType } from '~/types/interview'
import { ErrorCode } from '~/utils/error-code'

const typeSchema = z.enum(['frontend', 'backend', 'fullstack', 'algorithm']) as z.ZodType<InterviewType>
const stageSchema = z.enum(['introduction', 'technical', 'project', 'design', 'ending', 'terminated']) as z.ZodType<InterviewStage>

const bodySchema = z.object({
  messages: z.array(
    z.object({
      role: z.enum(['user', 'assistant']),
      content: z.string(),
    }),
  ),
  type: typeSchema,
  stage: stageSchema,
  interviewerName: z.string(),
})

export default defineRouteHandler({ auth: true, bodyRules: bodySchema }, async (event, { session, body }) => {
  try {
    const userId = session.user.id

    const { messages, type, stage, interviewerName } = body

    const messagesArray = messages.filter((msg: { role: string; content: string }) => msg.content.trim() !== '')

    const { generatePrompt } = usePrompts()
    const promptContent = generatePrompt(interviewerName, type, stage)
    const llm = await LLM.create(AiModel.Deepseek_V3, messagesArray, userId)

    const stream = await llm
      .usePrompt([
        {
          role: 'user',
          content: promptContent,
        },
      ])
      .withCreditCheck()
      .useModel()

    if (!stream) {
      return respError(ErrorCode.AI_MODEL_CALL_FAILED.code, ErrorCode.AI_MODEL_CALL_FAILED.displayMessage)
    }

    const rs = stream.toReadableStream()
    return sendStream(event, rs)
  } catch (error) {
    logger.error('AI response error:', error)
    return respError(ErrorCode.AI_RESPONSE_ERROR.code, error instanceof Error ? error.message : ErrorCode.AI_RESPONSE_ERROR.displayMessage)
  }
})
