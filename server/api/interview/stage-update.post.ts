import { H3Event } from 'h3'
import { z } from 'zod'
import { LLM } from '~/server/ai'
import { AiModel } from '~/server/ai/constants'
import { useStageManager } from '~/server/prompt-service/stage-manager'
import { respError, respSuccess } from '~/server/utils/response'
import { InterviewStage } from '~/types/interview'
import { ErrorCode } from '~/utils/error-code'

const schema = z.object({
  messages: z.array(
    z.object({
      role: z.enum(['user', 'interviewer']),
      content: z.string(),
    }),
  ),
  currentStage: z.enum(['introduction', 'technical', 'project', 'design', 'ending', 'terminated']) as z.ZodType<InterviewStage>,
})

export default defineRouteHandler({ auth: true, bodyRules: schema }, async (event: H3Event, { session, body }) => {
  const { messages, currentStage } = body
  const userId = session?.user?.id

  try {
    const { stageTransitionPrompt } = useStageManager()
    const prompt = stageTransitionPrompt(currentStage)

    const llm = await LLM.create(
      AiModel.Deepseek_V3,
      [
        {
          role: 'user',
          content: prompt,
        },
      ],
      userId,
    )

    const messagesContent = JSON.stringify({
      messages,
      currentStage,
    })

    const stream = await llm
      .usePrompt([
        {
          role: 'user',
          content: messagesContent,
        },
      ])
      .withCreditCheck()
      .useModel()

    if (!stream) {
      return respError(ErrorCode.AI_MODEL_CALL_FAILED.code, ErrorCode.AI_MODEL_CALL_FAILED.displayMessage)
    }

    const textContent = await stream.transformToString()
    const result = JSON.parse(textContent.trim() || '{}')

    return respSuccess({
      message: 'Stage transition check completed',
      result,
    })
  } catch (error) {
    logger.error('面试阶段转换检查错误:', error)
    return respError(ErrorCode.AI_RESPONSE_ERROR.code, error instanceof Error ? error.message : ErrorCode.AI_RESPONSE_ERROR.displayMessage)
  }
})
