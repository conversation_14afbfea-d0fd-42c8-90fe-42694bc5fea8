import { H3Event, sendStream } from 'h3'
import { z } from 'zod'
import { LLM } from '~/server/ai'
import { AiModel } from '~/server/ai/constants'
import { usePrompts } from '~/server/prompt-service/interview'
import { handleApiError } from '~/server/utils/error-handler'
import { defineRouteHandler } from '~/server/utils/handler'
import { respError } from '~/server/utils/response'
import type { InterviewStage, InterviewType } from '~/types/interview'
import { ErrorCode } from '~/utils/error-code'

const interviewTypeEnum = ['frontend', 'backend', 'fullstack', 'algorithm'] as const

const messageRoleEnum = ['user', 'assistant', 'interviewer'] as const

const messageSchema = z.object({
  role: z.enum(messageRoleEnum),
  content: z.string(),
})

const initialSchema = z.object({
  scenario: z.literal('initial'),
  interviewType: z.enum(interviewTypeEnum) as z.ZodType<InterviewType>,
  resumeText: z.string(),
  jobInfo: z.string(),
  majorQuestions: z.string().optional(),
})

const followupSchema = z.object({
  scenario: z.literal('followup'),
  interviewType: z.enum(interviewTypeEnum) as z.ZodType<InterviewType>,
  stage: z.enum(['introduction', 'technical', 'project', 'design', 'ending', 'terminated']) as z.ZodType<InterviewStage>,
  messages: z.array(messageSchema).min(1, 'Messages are required for followup.'),
  resumeText: z.string(),
  jobInfo: z.string(),
  majorQuestions: z.string().optional(),
})

const analysisSchema = z.object({
  scenario: z.literal('analysis'),
  interviewType: z.enum(interviewTypeEnum) as z.ZodType<InterviewType>,
  messages: z.array(messageSchema).min(1, 'Messages are required for analysis.'),
  resumeText: z.string(),
  jobInfo: z.string(),
  majorQuestions: z.string().optional(),
})

const suggestionSchema = z.object({
  scenario: z.literal('suggestion'),
  interviewType: z.enum(interviewTypeEnum) as z.ZodType<InterviewType>,
  currentQuestionContent: z.string().min(1, 'Current question content cannot be empty.'),
  messages: z.array(messageSchema).optional(),
  resumeText: z.string(),
  jobInfo: z.string(),
  majorQuestions: z.string().optional(),
})

const combinedSchema = z.discriminatedUnion('scenario', [initialSchema, followupSchema, analysisSchema, suggestionSchema])

export default defineRouteHandler({ auth: true, bodyRules: combinedSchema }, async (event: H3Event, { session, body }) => {
  const userId = session.user.id
  let prompt: string
  let requiredTokens = 200

  const { getAnalysisPrompt, getSuggestionPrompt, generateOptimizedInterviewerPrompt } = usePrompts()

  const INTERVIEWER_NAME = 'AI面试官'

  try {
    const { interviewType, resumeText, jobInfo, majorQuestions = '' } = body

    switch (body.scenario) {
      case 'initial':
        {
          const stage: InterviewStage = 'introduction'
          prompt = generateOptimizedInterviewerPrompt(INTERVIEWER_NAME, interviewType, stage, undefined, {
            resumeText,
            jobInfo,
            majorQuestions,
          })
          console.log('Initial prompt:', prompt)
        }
        break

      case 'followup':
        {
          const stage: InterviewStage = 'technical'
          prompt = generateOptimizedInterviewerPrompt(INTERVIEWER_NAME, interviewType, stage, body.messages, {
            resumeText,
            jobInfo,
            majorQuestions,
          })
          console.log('Followup prompt with history (generated by service):', prompt)
        }
        break

      case 'analysis':
        requiredTokens = 300
        const latestInterviewerMessage = body.messages.filter(m => m.role === 'user').pop()
        if (!latestInterviewerMessage) {
          return respError(ErrorCode.BAD_REQUEST.code, 'No user message found for analysis.')
        }
        const interviewerQuestion = latestInterviewerMessage.content
        prompt = getAnalysisPrompt(interviewType, {
          resumeText,
          jobInfo,
          interviewerQuestion,
        })
        break

      case 'suggestion':
        requiredTokens = 300
        const formattedHistoryForSuggestion =
          body.messages && body.messages.length > 0 ? body.messages.map(m => `${m.role}: ${m.content}`).join('\n') : '无可用对话历史。'
        prompt = getSuggestionPrompt(interviewType, body.currentQuestionContent, formattedHistoryForSuggestion, {
          resumeText,
          jobInfo,
          interviewerQuestion: body.currentQuestionContent,
        })
        break

      default:
        // @ts-expect-error: body should be a never type here due to discriminated union exhaustion
        logger.error('Invalid scenario:', body.scenario)
        return respError(ErrorCode.BAD_REQUEST.code, 'Invalid scenario provided.')
    }

    const llm = await LLM.create(AiModel.Deepseek_V3, [{ role: 'user', content: prompt }], userId)

    const streamInstance = await llm.usePrompt().withCreditCheck().useModel()

    if (!streamInstance) {
      return respError(ErrorCode.AI_MODEL_CALL_FAILED.code, ErrorCode.AI_MODEL_CALL_FAILED.displayMessage)
    }

    event.node.res.setHeader('Content-Type', 'text/plain; charset=utf-8')
    return sendStream(event, streamInstance.toReadableStream())
  } catch (error) {
    logger.error(`生成内容失败：${(body as any)?.scenario || 'unknown'}`, error)
    return handleApiError(event, error, ErrorCode.INTERNAL_SERVER_ERROR)
  }
})
