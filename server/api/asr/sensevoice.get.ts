import { consola } from 'consola'
import type { H3Event } from 'h3'
import { z } from 'zod'
import { senseVoiceService } from '~/server/services/asr/sensevoice.service'
import { respError, respSuccess } from '~/server/utils/response'
import { ErrorCode } from '~/utils/error-code'

const logger = consola.withTag('api:asr:sensevoice-query')

const senseVoiceQuerySchema = z.object({
  taskId: z.string().min(1, { message: 'taskId 不能为空' }),
})

export default defineRouteHandler({ auth: true }, async (event: H3Event) => {
  const { taskId } = await getValidatedQuery(event, (query: any) =>
    senseVoiceQuerySchema.parse({
      taskId: query.taskId,
    }),
  )
  return respSuccess({
    message: 'coming soon...',
  })
  try {
    logger.info('SenseVoice 查询任务', { taskId })
    const result = await senseVoiceService.queryTask(taskId)
    return respSuccess({
      ...result,
    })
  } catch (error: any) {
    logger.error('SenseVoice 查询接口异常', error)
    setResponseStatus(event, ErrorCode.AI_MODEL_CALL_FAILED.code)
    return respError(ErrorCode.AI_MODEL_CALL_FAILED.code, error.message || ErrorCode.AI_MODEL_CALL_FAILED.displayMessage, {})
  }
})
