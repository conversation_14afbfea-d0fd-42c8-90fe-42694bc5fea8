import { consola } from 'consola'
import type { H3Event } from 'h3'
import { asrBillingConfigs } from '~/server/ai/constants'
import { recognizeSpeech, type XunfeiSttAudioMeta, type XunfeiSttParams } from '~/server/services/asr/xunfei.service'
import { voiceBillingService } from '~/server/services/voice-billing.service'
import { handleApiError } from '~/server/utils/error-handler'
import { defineRouteHandler } from '~/server/utils/handler'
import { respSuccess } from '~/server/utils/response'
import { ErrorCode } from '~/utils/error-code'

const logger = consola.withTag('api:asr:xunfei')

// 支持的音频格式
const SUPPORTED_AUDIO_TYPES = [
  'audio/mpeg', // mp3
  'audio/mp3', // mp3
  'audio/wav', // wav (PCM)
  'audio/x-wav', // wav
  'audio/pcm', // pcm
  'audio/webm', // webm
  'audio/ogg', // ogg
  'audio/m4a', // m4a
  'audio/aac', // aac
]

// 最大文件大小 (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024

/**
 * 讯飞语音识别接口
 * 接收音频文件，调用讯飞大模型中英识别API进行语音转文字
 */
export default defineRouteHandler({ auth: true }, async (event: H3Event, { session }) => {
  try {
    logger.info('收到讯飞语音识别请求')
    return respSuccess({
      text: 'coming soon',
    })
    // 解析 multipart/form-data
    const formData = await readMultipartFormData(event)
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'please upload a audio file',
      })
    }

    // 查找音频文件
    const audioFile = formData.find(part => part.name === 'audio' && part.type && SUPPORTED_AUDIO_TYPES.includes(part.type))

    if (!audioFile || !audioFile.data) {
      throw createError({
        statusCode: 400,
        statusMessage: 'please upload a audio file (supported formats: MP3, WAV, PCM, WebM, OGG, M4A, AAC)',
      })
    }

    // 检查文件大小
    if (audioFile.data.length > MAX_FILE_SIZE) {
      throw createError({
        statusCode: 413,
        statusMessage: `audio file size must be less than ${MAX_FILE_SIZE / 1024 / 1024}MB`,
      })
    }

    logger.info('音频文件信息', {
      fileName: audioFile.filename,
      fileType: audioFile.type,
      fileSize: audioFile.data.length,
    })

    // 确定音频编码格式
    let encoding: 'raw' | 'lame' = 'raw'
    if (audioFile.type === 'audio/mpeg' || audioFile.type === 'audio/mp3') {
      encoding = 'lame' // MP3格式
    }

    // 设置音频元数据
    const audioMeta: XunfeiSttAudioMeta = {
      encoding,
      sample_rate: 16000, // 默认采样率
      channels: 1, // 单声道
      bit_depth: 16, // 位深
    }

    // 设置识别参数
    const sttParams: XunfeiSttParams = {
      domain: 'slm', // 大模型中文语音识别
      language: 'zh_cn', // 中文
      accent: 'mandarin', // 普通话
      eos: 6000, // 静音6秒后停止识别
      vinfo: 1, // 句子级别帧对齐
      dwa: 'wpgs', // 流式识别PGS，返回速度更快
    }

    // 转换为Buffer
    const audioBuffer = Buffer.from(audioFile.data)

    // 计费：按次数计费，每次识别消耗固定token
    const billingConfig = asrBillingConfigs.xunfei
    await voiceBillingService.deductBalance({
      userId: session.user.id,
      count: 1, // 每次识别计费一次
      billingConfig,
    })

    logger.info('开始调用讯飞语音识别服务')

    // 调用讯飞语音识别服务
    const recognizedText = await recognizeSpeech(
      audioBuffer,
      sttParams,
      audioMeta,
      60000, // 60秒超时
    )

    logger.info('讯飞语音识别完成', {
      textLength: recognizedText.length,
      text: recognizedText.substring(0, 100) + (recognizedText.length > 100 ? '...' : ''),
    })

    return respSuccess({
      text: recognizedText,
      audioInfo: {
        fileName: audioFile.filename,
        fileType: audioFile.type,
        fileSize: audioFile.data.length,
        encoding: audioMeta.encoding,
        sampleRate: audioMeta.sample_rate,
      },
      message: '语音识别成功',
    })
  } catch (error: any) {
    logger.error('讯飞语音识别失败', {
      error: error.message,
      statusCode: error.statusCode,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    })

    // 处理不同类型的错误
    if (error.statusCode) {
      // H3Error (createError 创建的错误)
      throw error
    }

    if (error.message?.includes('timeout')) {
      throw createError({
        statusCode: 408,
        statusMessage: '语音识别超时，请确保音频文件时长不超过60秒',
      })
    }

    if (error.message?.includes('config error') || error.message?.includes('app id error')) {
      return handleApiError(event, error, ErrorCode.INTERNAL_SERVER_ERROR)
    }

    if (error.message?.includes('api error')) {
      return handleApiError(event, error, ErrorCode.AI_MODEL_CALL_FAILED)
    }

    // 其他未知错误，包括 InsufficientBalanceError
    return handleApiError(event, error, ErrorCode.INTERNAL_SERVER_ERROR)
  }
})
