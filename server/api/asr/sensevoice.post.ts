import { consola } from 'consola'
import type { H3Event } from 'h3'
import { z } from 'zod'
import { senseVoiceService } from '~/server/services/asr/sensevoice.service'
import { handleApiError } from '~/server/utils/error-handler'
import { respSuccess } from '~/server/utils/response'
import { ErrorCode } from '~/utils/error-code'

const logger = consola.withTag('api:asr:sensevoice')

const senseVoiceRequestBodySchema = z.object({
  fileUrls: z.array(z.string().url()).min(1, { message: 'fileUrls 不能为空' }),
  channelId: z.array(z.number().int().min(0)).optional(),
  disfluencyRemovalEnabled: z.boolean().optional(),
  languageHints: z.array(z.string().min(1)).max(1).optional(),
})

export default defineRouteHandler(
  {
    auth: true,
    bodyRules: senseVoiceRequestBodySchema,
  },
  async (event: H3Event, { body, session }) => {
    try {
      return respSuccess({
        message: 'coming soon...',
      })
      logger.info('SenseVoice 识别请求', body)
      const submitRes = await senseVoiceService.submitTask(body)
      return respSuccess({
        message: 'task submitted',
        ...submitRes,
      })
    } catch (error: any) {
      logger.error('SenseVoice 识别接口异常', error)
      return handleApiError(event, error, ErrorCode.AI_MODEL_CALL_FAILED)
    }
  },
)
