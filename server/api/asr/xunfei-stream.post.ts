import { consola } from 'consola'
import type { H3Event } from 'h3'
import { asrBillingConfigs } from '~/server/ai/constants'
import { recognizeSpeechStream, type StreamEventData, type XunfeiSttAudioMeta, type XunfeiSttParams } from '~/server/services/asr/xunfei.service'
import { voiceBillingService } from '~/server/services/voice-billing.service'
import { defineRouteHandler } from '~/server/utils/handler'

const logger = consola.withTag('api:asr:xunfei-stream')

// 支持的音频格式
const SUPPORTED_AUDIO_TYPES = [
  'audio/mpeg', // MP3
  'audio/wav', // WAV
  'audio/x-wav', // WAV (alternative MIME type)
  'audio/wave', // WAV (alternative MIME type)
  'audio/webm', // WebM
  'audio/ogg', // OGG
  'audio/mp4', // M4A
  'audio/aac', // AAC
  'audio/x-m4a', // M4A (alternative MIME type)
  'application/octet-stream', // PCM raw data
]

/**
 * 讯飞语音识别流式接口
 * 接收音频文件，返回 Server-Sent Events 流式识别结果
 */
export default defineRouteHandler({ auth: true }, async (event: H3Event, { session }) => {
  try {
    logger.info('收到讯飞流式语音识别请求')

    // 设置 SSE 响应头
    setHeader(event, 'Content-Type', 'text/event-stream; charset=utf-8')
    setHeader(event, 'Cache-Control', 'no-cache, no-store, must-revalidate')
    setHeader(event, 'Connection', 'keep-alive')
    setHeader(event, 'Access-Control-Allow-Origin', '*')
    setHeader(event, 'Access-Control-Allow-Headers', 'Cache-Control')
    setHeader(event, 'X-Accel-Buffering', 'no') // 禁用 Nginx 缓冲

    // 发送初始连接确认
    const response = event.node.res
    response.write('data: {"type":"connected"}\n\n')

    // 解析 multipart/form-data
    const formData = await readMultipartFormData(event)
    if (!formData || formData.length === 0) {
      const errorData: StreamEventData = {
        type: 'error',
        data: { error: 'please upload a audio file' },
      }
      return sendStreamEvent(event, errorData)
    }

    // 查找音频文件
    const audioFile = formData.find(part => part.name === 'audio' && part.type && SUPPORTED_AUDIO_TYPES.includes(part.type))

    if (!audioFile || !audioFile.data) {
      const errorData: StreamEventData = {
        type: 'error',
        data: { error: 'please upload a audio file (supported formats: MP3, WAV, PCM, WebM, OGG, M4A, AAC)' },
      }
      return sendStreamEvent(event, errorData)
    }

    logger.info('音频文件信息', {
      fileName: audioFile.filename,
      fileType: audioFile.type,
      fileSize: audioFile.data.length,
    })

    // 根据文件类型设置音频元数据
    const audioMeta: XunfeiSttAudioMeta = {
      encoding: audioFile.type === 'audio/mpeg' ? 'lame' : 'raw',
      sample_rate: 16000,
      channels: 1,
      bit_depth: 16,
    }

    // 设置识别参数
    const sttParams: XunfeiSttParams = {
      domain: 'slm', // 大模型中文语音识别
      language: 'zh_cn', // 中文
      accent: 'mandarin', // 普通话
      eos: 6000, // 静音6秒后停止识别
      vinfo: 1, // 句子级别帧对齐
      dwa: 'wpgs', // 流式识别PGS，返回速度更快
    }

    // 转换为Buffer
    const audioBuffer = Buffer.from(audioFile.data)

    // 计费：按次数计费，每次识别消耗固定token
    const billingConfig = asrBillingConfigs.xunfei
    await voiceBillingService.deductBalance({
      userId: session.user.id,
      count: 1, // 每次识别计费一次
      billingConfig,
    })

    logger.info('开始调用讯飞流式语音识别服务')

    // 发送开始事件
    const startData: StreamEventData = {
      type: 'progress',
      data: {
        text: '',
        isPartial: true,
        timestamp: Date.now(),
        audioInfo: {
          fileName: audioFile.filename,
          fileType: audioFile.type,
          fileSize: audioFile.data.length,
          encoding: audioMeta.encoding,
          sampleRate: audioMeta.sample_rate,
        },
      },
    }
    await sendStreamEvent(event, startData)

    // 调用流式识别服务
    await recognizeSpeechStream(
      audioBuffer,
      sttParams,
      audioMeta,
      60000, // 60秒超时
      async streamData => {
        await sendStreamEvent(event, streamData)
      },
    )

    logger.info('讯飞流式语音识别完成')
  } catch (error: any) {
    logger.error('讯飞流式语音识别失败', {
      error: error.message,
      statusCode: error.statusCode,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    })

    // 发送错误事件
    const errorData: StreamEventData = {
      type: 'error',
      data: {
        error: error.message || '语音识别失败',
      },
    }
    await sendStreamEvent(event, errorData)
  }
})

/**
 * 发送 SSE 事件到客户端
 */
async function sendStreamEvent(event: H3Event, data: StreamEventData) {
  const eventData = `data: ${JSON.stringify(data)}\n\n`
  const response = event.node.res

  try {
    if (!response.destroyed && !response.writableEnded) {
      response.write(eventData)

      // 确保数据立即发送
      if ('flush' in response && typeof response.flush === 'function') {
        response.flush()
      }
    }
  } catch (error) {
    console.error('[SSE] 发送事件失败:', error)
  }

  // 如果是完成或错误事件，结束连接
  if (data.type === 'complete' || data.type === 'error') {
    setTimeout(() => {
      if (!response.destroyed && !response.writableEnded) {
        response.end()
      }
    }, 100) // 延迟100ms确保最后的数据发送完成
  }
}
