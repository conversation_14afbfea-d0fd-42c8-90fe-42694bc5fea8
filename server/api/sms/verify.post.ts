import { H3Event } from 'h3'
import { z } from 'zod'
import { getMailCodeRecord, removeMailCodeRecord } from '~/server/redis/email'
import { ErrorCode } from '~/utils/error-code'

const schema = z.object({
  email: z.string().email('Invalid email format'),
  code: z.string().length(6, 'Verification code must be 6 digits'),
})

export default defineRouteHandler({ auth: false, bodyRules: schema }, async (event: H3Event, { body }) => {
  const { email, code } = body
  try {
    const record = await getMailCodeRecord(email, code)
    if (!record) {
      return respError(ErrorCode.BAD_REQUEST.code, ErrorCode.BAD_REQUEST.displayMessage)
    }
    // 验证成功后删除验证码
    await removeMailCodeRecord(email)
    return respSuccess({
      message: 'Verification successful',
    })
  } catch (error) {
    logger.error('Verify code error:', error)
    return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, ErrorCode.INTERNAL_SERVER_ERROR.displayMessage)
  }
})
