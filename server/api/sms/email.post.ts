import { H3Event } from 'h3'
import { EmailParams, MailerSend, Recipient, Sender } from 'mailersend'
import { z } from 'zod'
import { config } from '~/server/config'
import { addMailCodeRecord, isLocked } from '~/server/redis/email'
import { ErrorCode } from '~/utils/error-code'

const schema = z.object({
  email: z.string().email('Invalid email format'),
})

// 生成6位数字验证码
const generateVerificationCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

export default defineRouteHandler({ auth: false, bodyRules: schema }, async (event: H3Event, { body }) => {
  const { email } = body
  try {
    // 首先检查是否被锁定（防止频繁发送）
    const locked = await isLocked(email)
    if (locked) {
      return respError(ErrorCode.TOO_MANY_REQUESTS.code, ErrorCode.TOO_MANY_REQUESTS.displayMessage)
    }
    const verificationCode = generateVerificationCode()
    const mailerSend = new MailerSend({
      apiKey: config.mailersend.apiKey,
    })
    // 配置发件人信息
    const sentFrom = new Sender(config.mailersend.fromAddress, config.mailersend.fromName)
    // 配置收件人
    const recipients = [new Recipient(email)]
    // 配置邮件内容
    const emailParams = new EmailParams()
      .setFrom(sentFrom)
      .setTo(recipients)
      .setReplyTo(sentFrom)
      .setSubject('Hi-Offer - 验证码')
      .setHtml(
        `
        <div style="font-family: Arial, sans-serif; padding: 20px;">
          <h2>您的验证码</h2>
          <p>您的验证码是：<strong style="font-size: 24px;">${verificationCode}</strong></p>
          <p>验证码有效期为 5 分钟，请勿将验证码泄露给他人。</p>
        </div>
      `,
      )
      .setText(`您的验证码是：${verificationCode}\n验证码有效期为 5 分钟，请勿将验证码泄露给他人。`)
    // 发送邮件
    await mailerSend.email.send(emailParams)
    // 将验证码存储到缓存中
    await addMailCodeRecord(email, verificationCode)
    return respSuccess({
      message: 'Verification code sent',
    })
  } catch (error) {
    logger.error('Send verification code error:', error)
    return respError(ErrorCode.INTERNAL_SERVER_ERROR.code, ErrorCode.INTERNAL_SERVER_ERROR.displayMessage)
  }
})
