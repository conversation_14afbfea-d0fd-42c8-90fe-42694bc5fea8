import { Schema, model } from 'mongoose'
import type { Evaluation } from '~/types/evaluation'
import type { InterviewType } from '~/types/interview'

export interface IInterview {
  _id: Schema.Types.ObjectId
  userId: Schema.Types.ObjectId
  type: InterviewType
  interviewer: string
  duration: number
  date: Date
  messages: Array<{
    role: string
    content: string
    timestamp: Date
  }>
  evaluation: Evaluation
  createdAt: Date
  updatedAt: Date
}

const interviewSchema = new Schema<IInterview>(
  {
    userId: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: [
        'frontend',
        'backend',
        'fullstack',
        'algorithm',
        'new-media-operation',
        'product-manager',
        'sales-specialist',
        'marketing',
        'test-engineer',
        'ui-designer',
        'project-manager',
        'customer-service',
        'human-resource-specialist',
      ],
      required: true,
    },
    interviewer: {
      type: String,
      required: true,
    },
    duration: {
      type: Number, // 以秒为单位
      required: true,
    },
    date: {
      type: Date,
      required: true,
    },
    messages: [
      {
        role: {
          type: String,
          required: true,
        },
        content: {
          type: String,
          required: true,
        },
        timestamp: {
          type: Date,
          required: true,
        },
      },
    ],
    evaluation: {
      stageInsights: [
        {
          stage: {
            type: String,
            required: true,
          },
          stageName: {
            type: String,
            required: true,
          },
          summary: {
            type: String,
            required: true,
          },
          highlights: [
            {
              type: String,
            },
          ],
          improvements: [
            {
              type: String,
            },
          ],
          specificAdvice: [
            {
              type: String,
            },
          ],
        },
      ],
      technicalInsights: {
        knowledgeGaps: [
          {
            type: String,
          },
        ],
        practicalSkills: [
          {
            type: String,
          },
        ],
        problemSolvingApproach: [
          {
            type: String,
          },
        ],
        technicalCommunication: [
          {
            type: String,
          },
        ],
        improvements: [
          {
            type: String,
          },
        ],
      },
      communicationInsights: {
        strengths: [
          {
            type: String,
          },
        ],
        areasForImprovement: [
          {
            type: String,
          },
        ],
        expressionTips: [
          {
            type: String,
          },
        ],
        interactionStyle: [
          {
            type: String,
          },
        ],
      },
      careerInsights: {
        motivation: [
          {
            type: String,
          },
        ],
        careerPlanning: [
          {
            type: String,
          },
        ],
        roleAlignment: [
          {
            type: String,
          },
        ],
        developmentSuggestions: [
          {
            type: String,
          },
        ],
      },
      practicalAdvice: [
        {
          category: {
            type: String,
            required: true,
          },
          categoryName: {
            type: String,
            required: true,
          },
          items: [
            {
              issue: {
                type: String,
                required: true,
              },
              suggestion: {
                type: String,
                required: true,
              },
              examples: [
                {
                  type: String,
                },
              ],
            },
          ],
        },
      ],
      overallSummary: {
        type: String,
        required: true,
      },
      keyRecommendations: [
        {
          type: String,
          required: true,
        },
      ],
      nextSteps: [
        {
          type: String,
          required: true,
        },
      ],
    },
  },
  {
    timestamps: true,
  },
)

export const Interview = model<IInterview>('Interview', interviewSchema)
