import mongoose from 'mongoose'

export type OrderStatus = 'PENDING' | 'SUCCESS' | 'FAILED' | 'CLOSED'

const orderSchema = new mongoose.Schema(
  {
    outTradeNo: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    tradeNo: {
      type: String,
      sparse: true,
      index: true,
    },
    userId: {
      type: String,
      required: true,
      index: true,
    },
    planKey: {
      type: String,
      required: true,
    },
    amount: {
      type: Number, // 金额单位为分
      required: true,
    },
    status: {
      type: String,
      enum: ['PENDING', 'SUCCESS', 'FAILED', 'CLOSED'],
      default: 'PENDING',
      required: true,
    },
    paymentType: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
  },
)

// 创建复合索引
orderSchema.index({ userId: 1, createdAt: -1 })

export type OrderType = mongoose.InferSchemaType<typeof orderSchema>
export const Order = mongoose.model<OrderType>('Order', orderSchema)
