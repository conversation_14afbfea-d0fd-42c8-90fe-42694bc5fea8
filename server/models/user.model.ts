import bcrypt from 'bcryptjs'
import mongoose from 'mongoose'

export interface IUser {
  _id: string
  email: string
  password?: string
  username: string
  githubId?: string
  name?: string
  image?: string
  bio?: string
  createdAt: Date
  updatedAt: Date
  wallet: {
    credit: number
    tokenUsed: number
  }
  comparePassword(candidatePassword: string): Promise<boolean>
}

const userSchema = new mongoose.Schema<IUser>(
  {
    _id: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },
    password: {
      type: String,
      required: false,
      minlength: 6,
    },
    username: {
      type: String,
      required: true,
      trim: true,
    },
    githubId: {
      type: String,
      unique: true,
      sparse: true,
    },
    name: {
      type: String,
      trim: true,
    },
    image: {
      type: String,
    },
    bio: {
      type: String,
      trim: true,
      maxlength: 500,
    },
    wallet: {
      tokenUsed: {
        type: Number,
        default: 0,
      },
    },
  },
  {
    timestamps: true,
  },
)

// 密码加密中间件
userSchema.pre('save', async function (next) {
  if (!this.isModified('password') || !this.password) return next()

  try {
    const salt = await bcrypt.genSalt(10)
    this.password = await bcrypt.hash(this.password, salt)
    next()
  } catch (error) {
    next(error as Error)
  }
})

userSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  if (!this.password) return false
  return bcrypt.compare(candidatePassword, this.password)
}

export const User = mongoose.model<IUser>('User', userSchema)
