import { env } from '~/utils/env'

export const CREDIT_UNIT = 1000
export const skuConfig = {
  free: {
    key: 'free',
    name: '免费额度',
    originPrice: 0,
    price: 0,
    credit: 10,
    token: 10 * CREDIT_UNIT ,
  },
  'basic-credit': {
    key: 'basic-credit',
    name: '基础 credit 权益',
    originPrice: 12.8,
    price: 9.8,
    credit: 100,
    token: 100 * CREDIT_UNIT,
  },
  'pro-credit': {
    key: 'pro-credit',
    name: '超值 credit 权益',
    originPrice: 26.8,
    price: 19.8,
    credit: 220,
    token: 220 * CREDIT_UNIT,
  },
  'max-credit': {
    key: 'max-credit',
    name: '尊享 credit 权益',
    originPrice: 38.8,
    price: 29.8,
    credit: 320,
    token: 360 * CREDIT_UNIT,
  },
} as const


const creditConfig = {
  creditUnit: 1000, // 1000 token = 1 credit
} as const

export const mongoConfig = {
  uri: env.MONGODB_URI,
  // options: {
  //   connectTimeoutMS: 30000,
  //   retryWrites: true,
  //   retryReads: true,
  //   autoIndex: env.NODE_ENV !== 'production',
  //   maxIdleTimeMS: 30000,
  //   waitQueueTimeoutMS: 30000,
  //   heartbeatFrequencyMS: 10000,
  // },
} as const

export const redisConfig = {
  host: env.REDIS_HOST,
  port: env.REDIS_PORT,
  password: env.REDIS_PASSWORD,
  db: env.REDIS_DB,
  keyPrefix: env.REDIS_KEY_PREFIX,
} as const

export const openaiConfig = {
  apiKey: env.OPENAI_API_KEY,
  baseURL: env.OPENAI_BASE_URL,
  defaultModel: 'whisper-1',
} as const

export const deepseekConfig = {
  apiKey: env.DEEPSEEK_API_KEY,
  baseURL: env.DEEPSEEK_BASE_URL,
  defaultModel: env.DEFAULT_CHAT_MODEL,
} as const

export const moonshotConfig = {
  apiKey: env.MOONSOT_API_KEY,
  baseURL: env.MOONSOT_BASE_URL,
  defaultModel: 'moonshot-v1-32k',
} as const

export const xunfeiAsrConfig = {
  appId: env.XUNFEI_ASR_APP_ID,
  apiKey: env.XUNFEI_ASR_API_KEY,
  apiSecret: env.XUNFEI_ASR_API_SECRET,
} as const

export const xunfeiTtsConfig = {
  appId: env.XUNFEI_TTS_APP_ID,
  apiKey: env.XUNFEI_TTS_API_KEY,
  apiSecret: env.XUNFEI_TTS_API_SECRET,
  defaultVcn: env.DEFAULT_XUNFEI_TTS_VCN,
} as const

export const aliyunConfig = {
  apiKey: env.ALIYUN_API_KEY,
} as const

export const mailersendConfig = {
  apiKey: 'mlsn.25d6998547220f43ab8ef6ffc4d1c6df7b05d98c952271fd55fee52854fc6fe2',
  fromAddress: '<EMAIL>',
  fromName: 'Hi-Offer',
  mailExpireTime: 60 * 5, // mail code expire time
  mailSendLockTime: 60, // max frequency of sending mail
} as const

export const zpayConfig = {
  pid: env.ZPAY_PID,
  key: env.ZPAY_SECRET_KEY,
  createOrderUrl: 'https://zpayz.cn/mapi.php',
  notifyUrl: 'https://460a-36-235-134-121.ngrok-free.app/api/payment/zpay/notify',
  queryUrl: 'https://zpayz.cn/api.php',
} as const

export const minimaxConfig = {
  apiKey: env.MINIMAX_API_KEY,
  groupId: env.MINIMAX_GROUP_ID,
  tts: {
    model: env.MINIMAX_TTS_MODEL,
    defaultVoiceId: env.MINIMAX_TTS_VOICE_ID,
  },
} as const

export const config = {
  openai: openaiConfig,
  deepseek: deepseekConfig,
  moonshot: moonshotConfig,
  xunfeiAsr: xunfeiAsrConfig,
  xunfeiTts: xunfeiTtsConfig,
  minimax: minimaxConfig,
  mongo: mongoConfig,
  redis: redisConfig,
  mailersend: mailersendConfig,
  zpay: zpayConfig,
  aliyun: aliyunConfig,
  sku: skuConfig,
  credit: creditConfig,
} as const
