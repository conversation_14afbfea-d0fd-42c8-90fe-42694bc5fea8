import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://bmuexlxbhomhtcmegbza.supabase.co'
const supabaseKey =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJtdWV4bHhiaG9taHRjbWVnYnphIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDYzNDAyNSwiZXhwIjoyMDU2MjEwMDI1fQ.uScaPQm0oVpJxnMCmLL6tdeb71mBzS5pTME7lLiyrjk'
const supabase = createClient(supabaseUrl, supabaseKey)

// 文件存储桶名称
const BUCKETS = {
  RESUMES: 'resume-file',
} as const

/**
 * 确保存储桶存在
 * @param bucketName 存储桶名称
 * @param isPublic 是否公开访问
 */
export const ensureBucketExists = async (bucketName: string, isPublic = false) => {
  const { data: buckets } = await supabase.storage.listBuckets()
  if (!buckets?.find(bucket => bucket.name === bucketName)) {
    await supabase.storage.createBucket(bucketName, {
      public: isPublic,
    })
  }
  return true
}

/**
 * 上传文件到 Supabase 存储
 * @param bucketName 存储桶名称
 * @param fileName 文件名
 * @param fileData 文件数据
 * @param contentType 文件MIME类型
 * @returns 上传结果
 */
export const uploadFile = async (bucketName: string, fileName: string, fileData: Uint8Array | Blob | ArrayBuffer | Buffer, contentType?: string) => {
  // 确保桶存在
  await ensureBucketExists(bucketName)

  // 确保数据是 Blob 或原始的 Uint8Array
  let processedData: Blob | Uint8Array

  if (fileData instanceof Blob) {
    processedData = fileData
  } else if (fileData instanceof ArrayBuffer) {
    processedData = new Uint8Array(fileData)
  } else if (Buffer.isBuffer(fileData)) {
    processedData = new Uint8Array(fileData)
  } else {
    // 假设它已经是 Uint8Array
    processedData = fileData as Uint8Array
  }

  // 上传文件
  return await supabase.storage.from(bucketName).upload(fileName, processedData, {
    contentType: contentType || 'application/octet-stream',
    upsert: true,
  })
}

/**
 * 获取文件的签名URL
 * @param bucketName 存储桶名称
 * @param fileName 文件名
 * @param expiresIn 过期时间(秒)
 * @returns 签名URL
 */
export const getSignedUrl = async (
  bucketName: string,
  fileName: string,
  expiresIn = 60 * 5, // 默认5分钟
) => {
  return await supabase.storage.from(bucketName).createSignedUrl(fileName, expiresIn)
}

/**
 * 从存储中删除文件
 * @param bucketName 存储桶名称
 * @param filePath 文件路径
 * @returns 删除结果
 */
export const removeFile = async (bucketName: string, filePath: string | string[]) => {
  const paths = Array.isArray(filePath) ? filePath : [filePath]
  return await supabase.storage.from(bucketName).remove(paths)
}

/**
 * 获取存储桶的公共URL
 * @param bucketName 存储桶名称
 * @param filePath 文件路径
 * @returns 公共URL
 */
export const getPublicUrl = (bucketName: string, filePath: string) => {
  return supabase.storage.from(bucketName).getPublicUrl(filePath)
}

export { BUCKETS, supabase }
