import { createClient, RedisClientOptions, RedisClientType, SetOptions } from 'redis'
import { config } from '../config'

export class RedisManager {
  client: ReturnType<typeof createClient>

  constructor(public options: RedisClientOptions) {
    this.client = createClient(options)
    this.init()
  }

  async init() {
    console.log('init redis =>', new Date().toISOString(), this.options)

    this.client.on('connect', () => {
      console.log('redis connected =>', new Date().toISOString())
    })

    this.client.on('error', (err: Error) => {
      console.error('redis error =>', err)
    })

    await this.client.connect()
  }

  del(key: string) {
    return this.client.del(key)
  }

  get(key: string) {
    return this.client.get(key)
  }

  set(key: string, value: string, options: SetOptions = {}) {
    return this.client.set(key, value, options)
  }

  hSet(...args: Parameters<RedisClientType['hSet']>) {
    return this.client.hSet(...args)
  }

  hDel(...args: Parameters<RedisClientType['hDel']>) {
    return this.client.hDel(...args)
  }

  hGet(...args: Parameters<RedisClientType['hGet']>) {
    return this.client.hGet(...args)
  }

  hGetAll(...args: Parameters<RedisClientType['hGetAll']>) {
    return this.client.hGetAll(...args)
  }

  zAdd(...args: Parameters<RedisClientType['zAdd']>) {
    return this.client.zAdd(...args)
  }

  zRangeByScore(...args: Parameters<RedisClientType['zRangeByScore']>) {
    return this.client.zRangeByScore(...args)
  }

  zRem(...args: Parameters<RedisClientType['zRem']>) {
    return this.client.zRem(...args)
  }

  lTrim(...args: Parameters<RedisClientType['lTrim']>) {
    return this.client.lTrim(...args)
  }

  lPush(...args: Parameters<RedisClientType['lPush']>) {
    return this.client.lPush(...args)
  }

  lRange(...args: Parameters<RedisClientType['lRange']>) {
    return this.client.lRange(...args)
  }

  exists(key: string) {
    return this.client.exists(key)
  }

  ttl(key: string) {
    return this.client.ttl(key)
  }

  expire(key: string, seconds: number) {
    return this.client.expire(key, seconds)
  }

  subscribe(...args: Parameters<RedisClientType['subscribe']>) {
    return this.client.subscribe(...args)
  }

  publish(...args: Parameters<RedisClientType['publish']>) {
    return this.client.publish(...args)
  }

  private getLockKey(id: string) {
    return `GLOBAL#LOCK+${id}`
  }

  async lock(id: string, lockExpire: number = 10) {
    id = this.getLockKey(id)
    const r = await this.set(id, '1', { NX: true, EX: lockExpire })
    if (!r) {
      return
    }
    const unlock = async () => {
      await this.del(id)
    }
    return { unlock, id }
  }

  async blockingLock(id: string, lockExpire: number = 10, timeout: number = 2000, retryDelay: number = 500) {
    id = this.getLockKey(id)
    let startTime = Date.now()
    while (true) {
      const lock = await this.lock(id, lockExpire)
      if (lock) return lock
      if (Date.now() - startTime > timeout) {
        throw new Error('lock timeout')
      }
      await new Promise(resolve => setTimeout(resolve, retryDelay))
    }
  }

  async getJSON<T>(key: string): Promise<T | undefined> {
    const result = await this.client.get(key)
    if (!result) return undefined
    try {
      return JSON.parse(result) as T
    } catch (err) {
      logger.error('redis getJSON failed => %s %o', key, err)
      return undefined
    }
  }

  async setJSON(key: string, value: any, options: SetOptions = {}) {
    return this.client.set(key, JSON.stringify(value), options)
  }
}

export const redis = new RedisManager({
  url: `redis://${config.redis.host}:${config.redis.port}`,
})
