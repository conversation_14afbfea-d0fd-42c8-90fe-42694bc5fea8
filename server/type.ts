import { z } from 'zod'
export const messages = z.array(
  z.object({
    role: z.enum(['user', 'assistant', 'system']),
    content: z.string(),
  }),
)

const modelSchema = z.object({
  key: z.enum(['Deepseek_R1', 'Deepseek_V3', 'Whisper_1']),
  type: z.enum(['deepseek', 'gpt']),
  apiKey: z.string(),
  baseURL: z.string(),
  model: z.string(),
  costOf1KRawInputTokens: z.number(),
  costOf1KRawOutputTokens: z.number(),
})

export const modelBodySchema = z.object({
  message: messages,
  model: modelSchema,
  outputLanguage: z.string().optional(),
})

export type ChatBody = z.infer<typeof modelBodySchema>

export interface TextEvent {
  type: 'text'
  text: string
}
export interface UsageEvent {
  type: 'usage'
  promptTokens: number
  completionTokens: number
}
export interface ErrorEvent {
  type: 'error'
  error: any
}

export type Event = TextEvent | UsageEvent | ErrorEvent
