import { consola } from 'consola'
import { CREDIT_UNIT } from '~/server/config'
import { InsufficientBalanceError } from '~/utils/error'
import { VoiceBillingConfig } from '../ai/constants'
import { userWallet } from '../daos/user-wallet'

const logger = consola.withTag('VoiceBillingService')

export type VoiceBillingInput = {
  userId: string
  // 时长(秒)
  duration?: number
  // 字符数
  characterCount?: number
  // 次数
  count?: number
  // 计费配置
  billingConfig: VoiceBillingConfig
}

export type BillingResult = {
  cost: number
  remainingTokens: number
}

export class VoiceBillingService {
  /**
   * 计算语音费用
   * @param input - 计费输入参数
   * @returns 计算得出的费用
   * @throws {Error} 当输入参数无效时抛出错误
   */
  async calculateVoiceCost(input: VoiceBillingInput): Promise<number> {
    const { duration, characterCount, count, billingConfig } = input

    this.validateBillingInput(duration, characterCount, count, billingConfig)

    let cost = 0

    if (duration && billingConfig.durationBased) {
      cost += this.calculateDurationCost(duration, billingConfig.durationBased)
    }

    if (characterCount && billingConfig.characterCountBased) {
      cost += this.calculateCharacterCountCost(characterCount, billingConfig.characterCountBased)
    }

    if (count && billingConfig.countBased) {
      cost += this.calculateCountCost(count, billingConfig.countBased)
    }

    return cost
  }

  /**
   * 扣除用户余额
   * @param input - 计费输入参数
   * @returns 计费结果，包含费用和剩余余额
   * @throws {Error} 当用户钱包不存在或余额不足时抛出错误
   */
  async deductBalance(input: VoiceBillingInput): Promise<BillingResult> {
    const { userId } = input
    const cost = await this.calculateVoiceCost(input)

    const wallet = await userWallet.findUserWallet(userId)
    if (!wallet) {
      const errorMessage = `User wallet not found for userId: ${userId}`
      logger.error(errorMessage)
      throw new Error(errorMessage)
    }

    const updatedWallet = await userWallet.updateWalletUsage(userId, cost)

    if (!updatedWallet) {
      const errorMessage = `Insufficient balance for user ${userId}. Required: ${cost}, available: ${wallet.remainingTokens}.`
      logger.error(errorMessage)
      throw new InsufficientBalanceError(errorMessage)
    }

    logger.info(`用户 ${userId} 已扣除 ${cost / CREDIT_UNIT} 个credits，剩余credits: ${updatedWallet.remainingTokens / CREDIT_UNIT}`)

    return {
      cost,
      remainingTokens: updatedWallet.remainingTokens,
    }
  }

  private validateBillingInput(
    duration: number | undefined,
    characterCount: number | undefined,
    count: number | undefined,
    billingConfig: VoiceBillingConfig,
  ): void {
    const billingInputs = [
      { value: duration, config: billingConfig.durationBased, name: 'duration' },
      { value: characterCount, config: billingConfig.characterCountBased, name: 'characterCount' },
      { value: count, config: billingConfig.countBased, name: 'count' },
    ]

    const hasValidInput = billingInputs.some(({ value }) => value != null)
    if (!hasValidInput) {
      throw new Error('At least one of duration, characterCount, or count must be provided')
    }

    billingInputs.forEach(({ value, config, name }) => {
      if (value != null && !config) {
        throw new Error(`Billing configuration missing for provided ${name}`)
      }
    })
  }

  private calculateDurationCost(duration: number, config: NonNullable<VoiceBillingConfig['durationBased']>): number {
    const { costPerSecond, minBillingUnit } = config
    const billingSeconds = Math.ceil(duration / minBillingUnit) * minBillingUnit
    return billingSeconds * costPerSecond
  }

  private calculateCharacterCountCost(characterCount: number, config: NonNullable<VoiceBillingConfig['characterCountBased']>): number {
    const { costPerCharacter, minBillingUnit } = config
    const billingCharacters = Math.ceil(characterCount / minBillingUnit) * minBillingUnit
    return billingCharacters * costPerCharacter
  }

  private calculateCountCost(count: number, config: NonNullable<VoiceBillingConfig['countBased']>): number {
    const { costPerCount } = config
    return count * costPerCount
  }
}

export const voiceBillingService = new VoiceBillingService()
