import { Buffer } from 'buffer'
import crypto from 'crypto'
import WebSocket from 'ws'
import { config } from '~/server/config'
import { toBase64 } from '~/server/utils/encoding'

// API 常量
const TTS_API_HOST = 'tts-api.xfyun.cn'
const TTS_API_PATH = '/v2/tts'
const TTS_WEBSOCKET_URL = `wss://${TTS_API_HOST}${TTS_API_PATH}`
const DEFAULT_TIMEOUT_MS = 30000 // 默认超时时间 30 秒

/**
 * 讯飞 TTS 业务参数类型
 */
export type XunfeiTtsBusinessParams = {
  aue: 'raw' | 'lame' | 'opus' | 'opus-wb' | string // 音频编码，raw=pcm, lame=mp3
  auf?: string // 音频采样率，如 'audio/L16;rate=16000'
  vcn: string // 发音人
  speed?: number // 语速 [0-100]，默认 50
  volume?: number // 音量 [0-100]，默认 50
  pitch?: number // 音高 [0-100]，默认 50
  bgs?: 0 | 1 // 是否有背景音，0=无，1=有
  tte?: 'GB2312' | 'GBK' | 'BIG5' | 'UNICODE' | 'GB18030' | 'UTF8' // 文本编码格式
  sfl?: 1 // 流式返回 mp3 时需设置
  reg?: '0' | '1' | '2' // 英文发音方式
  rdn?: '0' | '1' | '2' | '3' // 数字发音方式
}

/**
 * 讯飞 TTS API 请求体结构
 */
type XunfeiTtsRequestPayload = {
  common: {
    app_id: string
  }
  business: XunfeiTtsBusinessParams
  data: {
    text: string // Base64 编码后的文本
    status: 2 // 数据状态，固定为2，表示文本全部发送
  }
}

/**
 * 讯飞 TTS API 响应数据中的 data 结构
 */
type XunfeiTtsResponseData = {
  audio: string // Base64 编码的音频片段
  ced: string // 合成进度
  status: 1 | 2 // 当前音频流状态，1=合成中，2=合成结束
}

/**
 * 讯飞 TTS API WebSocket 消息结构
 */
type XunfeiTtsResponseMessage = {
  code: number // 返回码，0 表示成功
  message: string // 描述信息
  sid?: string // 本次会话 ID，仅在第一帧返回
  data?: XunfeiTtsResponseData
}

/**
 * 生成讯飞 TTS API 的鉴权 URL
 * @returns {string} 鉴权后的 WebSocket URL
 * @throws {Error} 如果缺少必要的环境变量
 */
function getAuthenticatedUrl(): string {
  const { apiKey, apiSecret } = config.xunfeiTts

  if (!apiKey || !apiSecret) {
    throw new Error(`讯飞TTS环境变量 XUNFEI_TTS_API_KEY 或 XUNFEI_TTS_API_SECRET 未设置。`)
  }

  const date = new Date().toUTCString() // RFC1123 格式的 GMT 时间
  const signatureOrigin = `host: ${TTS_API_HOST}\ndate: ${date}\nGET ${TTS_API_PATH} HTTP/1.1`

  const signatureSha = crypto.createHmac('sha256', apiSecret).update(signatureOrigin).digest() // 返回 Buffer

  const signature = signatureSha.toString('base64')

  const authorizationOrigin = `api_key="${apiKey}", algorithm="hmac-sha256", headers="host date request-line", signature="${signature}"`
  const authorization = Buffer.from(authorizationOrigin).toString('base64')

  const params = new URLSearchParams({
    authorization,
    date,
    host: TTS_API_HOST,
  })

  return `${TTS_WEBSOCKET_URL}?${params.toString()}`
}

/**
 * 根据指定的讯飞文本编码参数 (tte) 对文本进行 Base64 编码。
 * 此函数本身仍保留，因为它包含了根据讯飞 'tte' 参数选择特定编码的逻辑。
 * @param {string} text 要编码的文本
 * @param {XunfeiTtsBusinessParams['tte']} tte 目标文本编码 (讯飞特定)
 * @returns {string} Base64 编码后的文本
 */
function encodeText(text: string, tte: XunfeiTtsBusinessParams['tte']): string {
  if (tte === 'UNICODE') {
    // 对于 UNICODE，讯飞示例代码行为类似 UTF-16LE，讯飞 API 会以此方式解析
    return toBase64(text, 'utf16le')
  }
  // 对于其他编码（如 UTF8, GBK 等），文档推荐小语种使用UTF8。
  // 我们统一使用 UTF-8 字节进行 Base64 编码，API侧通过tte参数识别原始编码。
  return toBase64(text, 'utf-8')
}

/**
 * 调用讯飞语音合成服务
 * @param {string} text 要合成的文本
 * @param {XunfeiTtsBusinessParams} businessParams 业务参数
 * @param {number} [timeout=DEFAULT_TIMEOUT_MS] 操作超时时间（毫秒）
 * @returns {Promise<Buffer>} 解析为包含合成音频PCM数据的 Buffer
 * @throws {Error} 如果环境变量未设置或 API 调用失败
 */
export async function synthesizeSpeech(text: string, businessParams: XunfeiTtsBusinessParams, timeout: number = DEFAULT_TIMEOUT_MS): Promise<Buffer> {
  const { appId } = config.xunfeiTts
  if (!appId) {
    throw new Error(`讯飞TTS环境变量 XUNFEI_TTS_APP_ID 未设置。`)
  }

  const authenticatedUrl = getAuthenticatedUrl()
  const encodedText = encodeText(text, businessParams.tte)

  const payload: XunfeiTtsRequestPayload = {
    common: {
      app_id: appId,
    },
    business: businessParams,
    data: {
      text: encodedText,
      status: 2, // 表示本次发送的是全部文本
    },
  }

  return new Promise((resolve, reject) => {
    const ttsWs = new WebSocket(authenticatedUrl)
    const audioChunks: Buffer[] = []
    let operationTimedOut = false

    const timeoutId = setTimeout(() => {
      operationTimedOut = true
      ttsWs.close(1006, 'Operation timed out') // 1006 Abnormal Closure
      reject(new Error(`讯飞 TTS 操作超时 (${timeout}ms)。`))
    }, timeout)

    const cleanup = () => {
      clearTimeout(timeoutId)
    }

    ttsWs.onopen = () => {
      if (operationTimedOut) return
      ttsWs.send(JSON.stringify(payload))
    }

    ttsWs.onmessage = event => {
      if (operationTimedOut) return
      try {
        const message = JSON.parse(event.data.toString()) as XunfeiTtsResponseMessage

        if (message.code !== 0) {
          const errorMessage = `讯飞 TTS API 错误: ${message.message} (Code: ${message.code})`
          console.error(errorMessage)
          ttsWs.close()
          cleanup()
          reject(new Error(errorMessage))
          return
        }

        if (message.data?.audio) {
          audioChunks.push(Buffer.from(message.data.audio, 'base64'))
        }

        if (message.data?.status === 2) {
          // 合成完成
          ttsWs.close(1000, 'Normal synthesis completion') // 1000 Normal Closure
          const completeAudio = Buffer.concat(audioChunks)
          cleanup()
          resolve(completeAudio)
        }
      } catch (error) {
        const errorMessage = '处理讯飞 TTS 消息时发生错误。'
        console.error(errorMessage, error)
        if (!operationTimedOut) {
          // 避免在超时后再次 reject
          ttsWs.close()
          cleanup()
          reject(new Error(errorMessage + (error instanceof Error ? ` Details: ${error.message}` : '')))
        }
      }
    }

    ttsWs.onerror = errorEvent => {
      if (operationTimedOut) return
      const errorMessage = `讯飞 TTS WebSocket 错误: ${errorEvent.message}`
      console.error(errorMessage, errorEvent.error)
      cleanup()
      reject(new Error(errorMessage))
    }

    ttsWs.onclose = event => {
      // 如果连接在未完成或未出错的情况下意外关闭，
      // 并且不是由超时触发的关闭，则可能需要处理。
      // 大部分情况应该由 onmessage 中的 API 错误或 onerror 处理。
      // event.wasClean 可以用来判断是否是正常关闭
      if (!operationTimedOut && event.code !== 1000 && audioChunks.length === 0) {
        // 这里可以添加逻辑，但通常 onerror 或 onmessage 中的错误处理会先捕获问题
        // console.warn(`讯飞 TTS WebSocket 意外关闭。Code: ${event.code}, Reason: ${event.reason}`);
      }
      cleanup() // 确保超时总被清理
    }
  })
}
