import { consola } from 'consola'
import { config } from '~/server/config'
import type {
  AudioFormat,
  Bitrate,
  Emotion,
  LanguageBoost,
  PronunciationDict,
  SampleRate,
  SystemVoiceId,
  T2AModel,
  T2ARequest,
  T2AResponse,
} from '~/types/minimax'

const logger = consola.withTag('MiniMaxTtsService')

// API 常量
const TTS_API_URL = 'https://api.minimax.chat/v1/t2a_v2'
const DEFAULT_TIMEOUT_MS = 30000 // 默认超时时间 30 秒

/**
 * MiniMax TTS 服务类
 * 用于调用 MiniMax T2A v2 API 进行文本到语音的转换
 */
export class MiniMaxTtsService {
  private readonly apiKey: string
  private readonly groupId: string
  private readonly defaultModel: string
  private readonly defaultVoiceId: string

  constructor(apiKey?: string, groupId?: string) {
    this.apiKey = apiKey || config.minimax.apiKey
    this.groupId = groupId || config.minimax.groupId
    this.defaultModel = config.minimax.tts.model
    this.defaultVoiceId = config.minimax.tts.defaultVoiceId

    if (!this.apiKey) {
      logger.error('缺少 MiniMax API Key。请设置环境变量或传递给构造函数。')
      throw new Error('minimax.apiKey is required')
    }

    if (!this.groupId) {
      logger.error('缺少 MiniMax Group ID。请设置环境变量或传递给构造函数。')
      throw new Error('minimax.groupId is required')
    }

    logger.info('MiniMaxTtsService 初始化完成。')
  }

  /**
   * 将十六进制音频数据转换为 Buffer
   * @param hexAudio 十六进制音频数据
   * @returns Buffer
   */
  private hexToBuffer(hexAudio: string): Buffer {
    return Buffer.from(hexAudio, 'hex')
  }

  /**
   * 文本转语音
   * @param text 要转换的文本
   * @param options 可选配置项
   * @returns 音频数据 Buffer
   */
  async synthesizeSpeech(
    text: string,
    options: {
      model?: T2AModel
      voiceId?: SystemVoiceId | string
      speed?: number
      volume?: number
      pitch?: number
      emotion?: Emotion
      format?: AudioFormat
      languageBoost?: LanguageBoost
      sampleRate?: SampleRate
      bitrate?: Bitrate
      channel?: 1 | 2
      latexRead?: boolean
      englishNormalization?: boolean
      pronunciationDict?: PronunciationDict
      subtitleEnable?: boolean
      timeout?: number
    } = {},
  ): Promise<Buffer> {
    const {
      model = this.defaultModel as T2AModel,
      voiceId = this.defaultVoiceId,
      speed = 1,
      volume = 1,
      pitch = 0,
      emotion = 'happy',
      format = 'mp3',
      languageBoost = 'auto',
      sampleRate = 32000,
      bitrate = 128000,
      channel = 1,
      latexRead = false,
      englishNormalization = false,
      pronunciationDict,
      subtitleEnable = false,
      timeout = DEFAULT_TIMEOUT_MS,
    } = options

    const request: T2ARequest = {
      model,
      text,
      stream: false,
      language_boost: languageBoost,
      output_format: 'hex',
      voice_setting: {
        voice_id: voiceId,
        speed,
        vol: volume,
        pitch,
        emotion,
        latex_read: latexRead,
        english_normalization: englishNormalization,
      },
      audio_setting: {
        sample_rate: sampleRate,
        bitrate,
        format,
        channel,
      },
      pronunciation_dict: pronunciationDict,
      subtitle_enable: subtitleEnable,
    }

    try {
      logger.info(`开始合成语音，文本长度: ${text.length} 字符`)
      const response = await $fetch<T2AResponse>(`${TTS_API_URL}?GroupId=${this.groupId}`, {
        method: 'POST',
        body: request,
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        timeout,
      })

      if (response.base_resp.status_code !== 0) {
        throw new Error(`MiniMax TTS API 错误: ${response.base_resp.status_msg}`)
      }

      const audioBuffer = this.hexToBuffer(response.data.audio)
      logger.info(`语音合成完成，音频大小: ${audioBuffer.length} 字节`)
      return audioBuffer
    } catch (error) {
      if (error instanceof Error) {
        const errorMessage = error.message
        logger.error(`MiniMax TTS API 调用失败: ${errorMessage}`)
        throw new Error(`MiniMax TTS API 调用失败: ${errorMessage}`)
      }
      throw error
    }
  }
}
