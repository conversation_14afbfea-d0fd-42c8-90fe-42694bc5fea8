import { consola } from 'consola'
import fs from 'fs/promises'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'
import type { Data as WebSocketDataType } from 'ws'
import { aliyunConfig } from '~/server/config'
import { AliyunWebSocketClient } from '~/server/utils/aliyunWebSocket'
import type { ClientMessage, ServerMessage, SynthesisParameters, TaskContext } from '~/types/cosyvoice'

const logger = consola.withTag('CosyVoiceService')

const WS_URL = 'wss://dashscope.aliyuncs.com/api-ws/v1/inference'
const DEFAULT_MODEL = 'cosyvoice-v1'

/**
 * CosyVoiceService 类通过 WebSocket 与阿里云 CosyVoice 语音合成服务进行交互。
 *
 * 主要功能：
 * - 建立和管理 WebSocket 连接。
 * - 发送语音合成指令（run-task, continue-task, finish-task）。
 * -接收服务端事件（task-started, result-generated, task-finished, task-failed）和二进制音频流。
 * - 处理任务生命周期，包括文本分块、超时控制和错误处理。
 *
 * 使用流程：
 * 1. 实例化 CosyVoiceService。
 * 2. 调用 `synthesizeSpeech` 方法发起语音合成请求。
 * 3. 服务内部会自动处理 WebSocket 连接、指令发送和事件接收。
 * 4. 合成完成或失败后，`synthesizeSpeech` 返回的 Promise 会被 resolve 或 reject。
 * 5. 可选：调用 `close` 方法手动关闭 WebSocket 连接。
 *
 * 注意事项：
 * - API Key 必须配置正确。
 * - 文本长度和编码格式需符合 API 要求。
 * - 遵循指令发送时序。
 */
export class CosyVoiceService {
  private wsClient: AliyunWebSocketClient
  private activeTasks = new Map<string, TaskContext>()

  constructor(apiKey?: string) {
    const effectiveApiKey = apiKey || aliyunConfig.apiKey
    if (!effectiveApiKey) {
      logger.error('缺少 DashScope API Key。请设置环境变量或传递给构造函数。')
      throw new Error('缺少 DashScope API Key. 请设置环境变量或传递给构造函数.')
    }

    this.wsClient = new AliyunWebSocketClient({
      url: WS_URL,
      apiKey: effectiveApiKey,
      dataInspection: true, // Corresponds to 'X-DashScope-DataInspection': 'enable'
    })

    this.setupWebSocketListeners()
    logger.info('CosyVoiceService 初始化完成。')
  }

  private setupWebSocketListeners(): void {
    this.wsClient.onOpen(() => {
      logger.info('WebSocket 连接通过 AliyunWebSocketClient 建立成功。')
      // Messages are queued and sent by AliyunWebSocketClient on open
    })

    this.wsClient.onMessage((data, isBinary) => {
      this.handleServerMessage(data, isBinary)
    })

    this.wsClient.onError(error => {
      logger.error('AliyunWebSocketClient 报告错误:', error.message)
      this.cleanupActiveTasks(new Error(`WebSocket 错误: ${error.message}`))
    })

    this.wsClient.onClose((code, reason) => {
      logger.warn(`AliyunWebSocketClient 报告连接关闭。代码: ${code}, 原因: ${reason}`)
      // If the close was unexpected, active tasks should be failed.
      // The AliyunWebSocketClient itself doesn't know about "explicit" vs "unexpected" in the
      // same way this service does, so we base it on active tasks.
      if (this.activeTasks.size > 0) {
        this.cleanupActiveTasks(new Error(`WebSocket 连接关闭。代码: ${code}, 原因: ${reason}`))
      }
    })
  }

  /**
   * 确保 WebSocket 连接已建立。
   * 委托给 AliyunWebSocketClient.connect()。
   */
  private async ensureConnected(): Promise<void> {
    if (!this.wsClient.isOpen) {
      logger.info('WebSocket 未连接，尝试通过 AliyunWebSocketClient 连接...')
      await this.wsClient.connect()
    } else {
      logger.debug('WebSocket 连接已由 AliyunWebSocketClient 维护。')
    }
  }

  /**
   * 清理所有活动任务，通常在连接错误或关闭时调用。
   * @param errorToRejectTasksWith 错误对象，用于拒绝任务。
   */
  private cleanupActiveTasks(errorToRejectTasksWith: Error): void {
    logger.warn(`由于错误，正在拒绝所有活动任务: ${errorToRejectTasksWith.message}`)
    this.activeTasks.forEach(task => {
      if (task.taskStartedTimeoutId) clearTimeout(task.taskStartedTimeoutId)
      task.reject(errorToRejectTasksWith)
    })
    this.activeTasks.clear()
    logger.debug('活动任务清理完成。')
  }

  /**
   * 发送 WebSocket 消息。
   * 委托给 AliyunWebSocketClient.send()。
   * @param message 要发送的客户端指令。
   */
  private sendServiceMessage(message: ClientMessage): void {
    const taskId = message.header.task_id
    logger.debug(`发送服务消息，任务 ${taskId}, 操作: ${message.header.action}:`, JSON.stringify(message))
    try {
      this.wsClient.send(message)
    } catch (err: any) {
      logger.error(`发送任务 ${taskId} (${message.header.action}) 消息时 AliyunWebSocketClient 抛出错误: ${err.message}`)
      const taskCtx = this.activeTasks.get(taskId)
      if (taskCtx) {
        taskCtx.reject(new Error(`发送任务 ${taskId} (${message.header.action}) 消息失败: ${err.message}`))
        this.activeTasks.delete(taskId)
      }
    }
  }

  /**
   * 尝试从JSON字符串消息中提取 task_id。
   * 用于调试或在某些情况下路由消息，但主要依赖于activeTasks的task_id。
   */
  private _tryExtractTaskId(messageString: string): string | null {
    try {
      const msg = JSON.parse(messageString)
      return msg?.header?.task_id || null
    } catch {
      return null
    }
  }

  private handleServerMessage(data: WebSocketDataType, isBinary: boolean): void {
    if (isBinary) {
      if (Buffer.isBuffer(data)) {
        let audioRouted = false
        for (const task of this.activeTasks.values()) {
          if (task.isTaskStarted) {
            logger.debug(`接收到任务 ${task.taskId} 的二进制音频块，大小: ${data.length} 字节。`)
            task.audioChunks.push(data)
            audioRouted = true
            break
          }
        }
        if (!audioRouted && data.length > 0) {
          logger.warn(`接收到二进制音频块 (大小: ${data.length})，但没有活动任务准备接收 (isTaskStarted=true)。正在丢弃。`)
        }
      } else {
        logger.warn('接收到非 Buffer 的二进制数据。忽略。')
      }
      return
    }

    const rawMessageString = Buffer.isBuffer(data) ? data.toString('utf-8') : (data as string)
    logger.debug('接收到服务器消息 (JSON):', rawMessageString)

    try {
      const parsedMessage = JSON.parse(rawMessageString) as Partial<ServerMessage>

      const taskIdFromHeader = parsedMessage.header?.task_id
      if (!taskIdFromHeader) {
        logger.warn('接收到 header 中没有 task_id 的服务器消息。忽略。', parsedMessage)
        return
      }

      const taskContext = this.activeTasks.get(taskIdFromHeader)
      if (!taskContext) {
        logger.warn(`接收到未知或已完成任务 task_id: ${taskIdFromHeader} 的服务器消息。忽略。当前活动任务: ${Array.from(this.activeTasks.keys())}`)
        return
      }

      const message = parsedMessage as ServerMessage

      switch (message.header.event) {
        case 'task-started':
          logger.info(`任务 ${taskContext.taskId} 已由服务器启动。`)
          if (taskContext.taskStartedTimeoutId) {
            clearTimeout(taskContext.taskStartedTimeoutId)
            delete taskContext.taskStartedTimeoutId
          }
          taskContext.isTaskStarted = true
          this._sendTextChunksAndFinish(taskContext).catch(err => {
            logger.error(`发送任务 ${taskContext.taskId} 的文本块和结束指令时出错:`, err.message)
            if (this.activeTasks.has(taskContext.taskId)) {
              taskContext.reject(err instanceof Error ? err : new Error(String(err)))
              this.activeTasks.delete(taskContext.taskId)
            }
          })
          break

        case 'result-generated':
          logger.debug(`接收到任务 ${taskContext.taskId} 的 'result-generated' 事件。载荷:`, message.payload)
          if (message.payload.usage?.characters) {
            logger.info(`任务 ${taskContext.taskId}: 累计计费字符数: ${message.payload.usage.characters}`)
          }
          break

        case 'task-finished':
          logger.info(`任务 ${taskContext.taskId} 已由服务器完成。`)
          const finalAudioBuffer = Buffer.concat(taskContext.audioChunks)
          logger.info(`任务 ${taskContext.taskId}: 总音频大小: ${finalAudioBuffer.length} 字节。`)
          if (message.payload.usage?.characters) {
            logger.info(`任务 ${taskContext.taskId}: 最终计费字符数: ${message.payload.usage.characters}`)
          }
          taskContext.resolve(finalAudioBuffer)
          this.activeTasks.delete(message.header.task_id)
          break

        case 'task-failed':
          const errorMsg = `任务 ${taskContext.taskId} 失败。错误: ${message.header.error_message} (代码: ${message.header.error_code})`
          logger.error(errorMsg)
          if (taskContext.taskStartedTimeoutId) {
            clearTimeout(taskContext.taskStartedTimeoutId)
            delete taskContext.taskStartedTimeoutId
          }
          taskContext.reject(new Error(errorMsg))
          this.activeTasks.delete(message.header.task_id)
          break

        default:
          const _exhaustiveCheck = message
          logger.warn(`接收到任务 ${taskContext.taskId} 的未知服务器事件类型:`, (_exhaustiveCheck as any)?.header?.event, rawMessageString)
      }
    } catch (error: any) {
      logger.error('解析服务器消息或处理事件时出错:', error.message, rawMessageString)
      const taskIdFromError = this._tryExtractTaskId(rawMessageString)
      if (taskIdFromError) {
        const taskCtx = this.activeTasks.get(taskIdFromError)
        if (taskCtx) {
          taskCtx.reject(new Error(`解析服务器消息失败: ${error.message}`))
          this.activeTasks.delete(taskIdFromError)
        }
      }
    }
  }

  private async _sendTextChunksAndFinish(taskContext: TaskContext): Promise<void> {
    const { taskId, texts: textsToSendViaContinue, reject: taskReject } = taskContext
    logger.debug(`任务 ${taskId}: 通过 continue-task 发送 ${textsToSendViaContinue.length} 个文本块。`)

    if (!this.activeTasks.has(taskId)) {
      logger.warn(`任务 ${taskId} 在发送文本块前不再活动。中止。`)
      return
    }

    try {
      for (const textChunk of textsToSendViaContinue) {
        if (!this.activeTasks.has(taskId)) {
          logger.warn(`任务 ${taskId} 在发送 continue-task 块期间变为非活动状态。中止后续块。`)
          return
        }
        if (Buffer.from(textChunk, 'utf-8').length > 4000) {
          logger.warn(
            `任务 ${taskId}: 用于 continue-task 的文本块可能超过 2000 字符 (字节数: ${Buffer.from(textChunk, 'utf-8').length})。API 限制为 2000 字符。`,
          )
        }
        const continueMessage: ClientMessage = {
          header: { action: 'continue-task', task_id: taskId, streaming: 'duplex' },
          payload: { input: { text: textChunk } },
        }
        this.sendServiceMessage(continueMessage) // Changed to sendServiceMessage
      }

      if (!this.activeTasks.has(taskId)) {
        logger.warn(`任务 ${taskId} 在发送 finish-task 前变为非活动状态。中止 finish-task。`)
        return
      }
      logger.debug(`任务 ${taskId}: 所有文本块已发送。发送 finish-task 指令。`)
      const finishMessage: ClientMessage = {
        header: { action: 'finish-task', task_id: taskId, streaming: 'duplex' },
        payload: { input: {} },
      }
      this.sendServiceMessage(finishMessage) // Changed to sendServiceMessage
    } catch (err) {
      logger.error(`任务 ${taskId}: 发送文本块或结束指令时出错:`, err instanceof Error ? err.message : String(err))
      if (this.activeTasks.has(taskId)) {
        taskReject(err instanceof Error ? err : new Error(String(err)))
        this.activeTasks.delete(taskId)
      }
      throw err
    }
  }

  public async synthesizeSpeech(
    texts: string | string[],
    modelParams: { model?: string; parameters: SynthesisParameters },
    outputFilePath?: string,
  ): Promise<Buffer> {
    logger.info(`正在发起新的语音合成请求。输出路径: ${outputFilePath || 'N/A'}`)
    // Ensure connection is active or try to connect.
    // AliyunWebSocketClient handles actual connection logic.
    // If it's not open, connect() will be called. If it is, this is a no-op.
    await this.wsClient.connect().catch(err => {
      logger.error('连接到 WebSocket 失败:', err.message)
      throw new Error(`连接到 WebSocket 失败: ${err.message}`)
    })

    const taskId = uuidv4()
    const effectiveModel = modelParams.model || DEFAULT_MODEL
    logger.info(
      `任务 ${taskId}: 使用模型 '${effectiveModel}'。音色: '${modelParams.parameters.voice}', 格式: '${modelParams.parameters.format || 'default'}'。`,
    )

    let initialTextForRunTask: string
    let remainingTextsForContinue: string[]

    if (Array.isArray(texts)) {
      if (texts.length > 0) {
        initialTextForRunTask = texts[0]
        remainingTextsForContinue = texts.slice(1)
        logger.debug(`任务 ${taskId}: 提供了 ${texts.length} 个文本块。第一个块用于 run-task，剩余 ${remainingTextsForContinue.length} 个用于 continue-task。`)
      } else {
        initialTextForRunTask = ''
        remainingTextsForContinue = []
        logger.warn(`任务 ${taskId}: 提供了空的文本数组。`)
      }
    } else {
      initialTextForRunTask = texts
      remainingTextsForContinue = []
      logger.debug(`任务 ${taskId}: 提供了单个文本字符串用于 run-task。`)
    }

    if (Buffer.from(initialTextForRunTask, 'utf-8').length > 4000) {
      logger.warn(
        `任务 ${taskId}: 用于 run-task 的初始文本可能超过 2000 字符 (字节数: ${Buffer.from(initialTextForRunTask, 'utf-8').length})。API 限制为 2000 字符。`,
      )
    }

    return new Promise<Buffer>(async (resolve, reject) => {
      const taskContext: TaskContext = {
        taskId,
        resolve: async audioBuffer => {
          if (outputFilePath) {
            try {
              logger.info(`任务 ${taskId}: 正在保存音频到 ${outputFilePath}`)
              const dir = path.dirname(outputFilePath)
              await fs.mkdir(dir, { recursive: true })
              await fs.writeFile(outputFilePath, audioBuffer)
              logger.info(`任务 ${taskId}: 音频成功保存到 ${outputFilePath}`)
            } catch (err: any) {
              logger.error(`任务 ${taskId}: 保存音频到 ${outputFilePath} 失败:`, err.message)
            }
          }
          resolve(audioBuffer)
        },
        reject: (err: Error) => {
          logger.error(`任务 ${taskId} 被拒绝: ${err.message}`)
          if (taskContext.taskStartedTimeoutId) {
            clearTimeout(taskContext.taskStartedTimeoutId)
            delete taskContext.taskStartedTimeoutId
          }
          reject(err)
        },
        audioChunks: [],
        outputFilePath,
        isTaskStarted: false,
        modelParameters: { model: effectiveModel, parameters: modelParams.parameters },
        texts: remainingTextsForContinue,
      }
      this.activeTasks.set(taskId, taskContext)
      logger.info(`任务 ${taskId}: 上下文已创建并添加到活动任务。`)

      const runTaskMessage: ClientMessage = {
        header: { action: 'run-task', task_id: taskId, streaming: 'duplex' },
        payload: {
          task_group: 'audio',
          task: 'tts',
          function: 'SpeechSynthesizer',
          model: effectiveModel,
          parameters: {
            text_type: 'PlainText',
            ...modelParams.parameters,
          },
          input: initialTextForRunTask ? { text: initialTextForRunTask } : {},
        },
      }
      this.sendServiceMessage(runTaskMessage) // Changed to sendServiceMessage

      const timeoutDelay = 10000
      logger.debug(`任务 ${taskId}: 设置 task-started 超时 (${timeoutDelay / 1000}秒)。`)
      taskContext.taskStartedTimeoutId = setTimeout(() => {
        const currentTask = this.activeTasks.get(taskId)
        if (currentTask && !currentTask.isTaskStarted) {
          const timeoutError = new Error(`等待任务 ${taskId} 的 'task-started' 事件超时 (${timeoutDelay / 1000}秒)。`)
          logger.error(timeoutError.message)
          currentTask.reject(timeoutError)
          this.activeTasks.delete(taskId)
        }
      }, timeoutDelay)
    })
  }

  public close(): void {
    logger.info('调用 CosyVoiceService.close 方法。')
    const closeError = new Error('连接被客户端主动关闭 (调用 CosyVoiceService.close())。')
    this.cleanupActiveTasks(closeError)
    this.wsClient.close(1000, 'CosyVoiceService explicitly closed')
    logger.info('CosyVoiceService 已关闭。')
  }
}
