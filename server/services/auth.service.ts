import { Config, adjectives, animals, uniqueNamesGenerator } from 'unique-names-generator'
import { v4 as uuidv4 } from 'uuid'
import { config } from '~/server/config'
import { ErrorCode } from '../../utils/error-code'
import { User } from '../models/user.model'
import { Wallet } from '../models/wallet.model'
import { AppError } from '../utils/error-handler'
import logger from '../utils/logger'
import { LoginInput, RegisterInput } from '../validators/auth.validator'
const INIT_FREE_TOKEN = config.sku.free.token

export const generateUsernameFromEmail = (email: string): string => {
  const localPart = email.split('@')[0]
  return localPart.replace(/[^a-zA-Z0-9]/g, '')
}

export const generateRandomName = (): string => {
  const config: Config = {
    dictionaries: [adjectives, animals],
    separator: ' ',
    length: 2,
    style: 'capital',
  }

  return uniqueNamesGenerator(config)
}

export const registerUser = async (input: RegisterInput) => {
  try {
    const existingUser = await User.findOne({ email: input.email })
    if (existingUser) {
      throw new AppError(ErrorCode.USER_EMAIL_EXISTS.message, ErrorCode.USER_EMAIL_EXISTS.code)
    }
    const generatedUsername = generateUsernameFromEmail(input.email)
    const randomName = generateRandomName()
    const avatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${crypto.randomUUID()}`
    const user = await User.create({
      _id: uuidv4(),
      email: input.email.toLowerCase(),
      password: input.password,
      username: generatedUsername,
      name: randomName,
      image: avatarUrl,
    })
    await Wallet.create({
      userId: user._id,
      planKey: 'free',
      paymentType: 'free',
      activated: true,
      remainingTokens: INIT_FREE_TOKEN,
    })
    return {
      id: user._id,
      email: user.email,
      username: user.username,
      name: user.name,
      image: user.image,
    }
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    logger.error('用户注册过程中发生错误:', error)
    throw new AppError(ErrorCode.INTERNAL_SERVER_ERROR.message, ErrorCode.INTERNAL_SERVER_ERROR.code)
  }
}

export const loginUser = async (input: LoginInput) => {
  const user = await User.findOne({ email: input.email })
  if (!user) {
    throw new AppError(ErrorCode.USER_NOT_FOUND.message, ErrorCode.USER_NOT_FOUND.code)
  }

  if (user.githubId) {
    throw new AppError(ErrorCode.USER_GITHUB_LOGIN_REQUIRED.message, ErrorCode.USER_GITHUB_LOGIN_REQUIRED.code)
  }

  const isValidPassword = await user.comparePassword(input.password)
  if (!isValidPassword) {
    throw new AppError(ErrorCode.USER_INVALID_PASSWORD.message, ErrorCode.USER_INVALID_PASSWORD.code)
  }

  logger.info(`用户登录成功: ${user.email}`)

  return {
    id: user._id,
    email: user.email,
    username: user.username,
  }
}
