import { consola } from 'consola'
import { randomUUID } from 'crypto'
import type { Finish<PERSON>ask<PERSON>ommand, GummyServiceConfig, ResultGeneratedPayloadOutput, RunTaskCommand, RunTaskParameters, ServerEvent } from '~/types/gummy'
import { AliyunWebSocketClient, AliyunWebSocketClientOptions } from '../../utils/aliyunWebSocket'

const logger = consola.withTag('GummyService')

const GUMMY_WEBSOCKET_URL = 'wss://dashscope.aliyuncs.com/api-ws/v1/inference'
const DEFAULT_MODEL_NAME = 'gummy-realtime-v1'
const TASK_GROUP_AUDIO = 'audio'
const TASK_ASR = 'asr'
const FUNCTION_RECOGNITION = 'recognition'
const STREAMING_DUPLEX = 'duplex'

export class GummyService {
  private wsClient: AliyunWebSocketClient
  private currentTaskId: string | null = null
  private config: GummyServiceConfig

  private taskStartedPromise: Promise<void> | null = null
  private taskStartedResolve: (() => void) | null = null
  private taskStartedReject: ((reason?: any) => void) | null = null

  constructor(config: GummyServiceConfig) {
    this.config = config
    const additionalHeaders: Record<string, string> = {}
    if (config.userAgent) {
      additionalHeaders['user-agent'] = config.userAgent
    }
    if (config.workspace) {
      additionalHeaders['X-DashScope-WorkSpace'] = config.workspace
    }

    const clientOptions: AliyunWebSocketClientOptions = {
      url: GUMMY_WEBSOCKET_URL,
      apiKey: config.apiKey,
      additionalHeaders,
      dataInspection: config.dataInspection !== undefined ? config.dataInspection : true,
    }

    this.wsClient = new AliyunWebSocketClient(clientOptions)
    this.setupEventHandlers()
    logger.info('GummyService initialized.')
  }

  private setupEventHandlers(): void {
    this.wsClient.onOpen(() => {
      logger.info('WebSocket connection opened for Gummy service.')
      this.config.onSocketOpen?.()
    })

    this.wsClient.onMessage((data, isBinary) => {
      try {
        let messageStr: string
        if (isBinary) {
          if (Buffer.isBuffer(data)) {
            messageStr = data.toString('utf-8')
          } else if (data instanceof ArrayBuffer) {
            messageStr = Buffer.from(data).toString('utf-8')
          } else {
            logger.error('Received binary data in unexpected format:', data)
            return
          }
        } else {
          if (typeof data === 'string') {
            messageStr = data
          } else {
            logger.error('Received non-binary data that is not a string:', data)
            return
          }
        }
        const event = JSON.parse(messageStr) as ServerEvent
        this.handleServerEvent(event)
      } catch (error) {
        logger.error('Failed to parse server event or invalid event structure:', error, data)
      }
    })

    this.wsClient.onError(error => {
      logger.error('WebSocket error in Gummy service:', error.message)
      if (this.taskStartedReject) {
        this.taskStartedReject(error)
        this.resetTaskStartedPromiseState()
      }
      this.config.onSocketError?.(error)
      this.currentTaskId = null
    })

    this.wsClient.onClose((code, reason) => {
      logger.warn(`WebSocket connection closed. Code: ${code}, Reason: ${reason}`)
      if (this.taskStartedReject) {
        this.taskStartedReject(new Error(`Connection closed: ${code} - ${reason}`))
        this.resetTaskStartedPromiseState()
      }
      this.config.onSocketClose?.(code, reason)
      this.currentTaskId = null
    })
  }

  private resetTaskStartedPromiseState(): void {
    this.taskStartedPromise = null
    this.taskStartedResolve = null
    this.taskStartedReject = null
  }

  private handleServerEvent(event: ServerEvent): void {
    if (!event.header || !event.header.task_id) {
      logger.warn('Received event without task_id or header:', event)
      return
    }

    const eventTaskId = event.header.task_id

    switch (event.header.event) {
      case 'task-started':
        logger.info(`Task started: ${eventTaskId}`)
        if (eventTaskId === this.currentTaskId && this.taskStartedResolve) {
          this.taskStartedResolve()
          this.resetTaskStartedPromiseState()
        }
        this.config.onTaskStarted?.(eventTaskId)
        break
      case 'result-generated':
        // logger.debug(`Result generated for task: ${eventTaskId}`, event.payload.output);
        if ('output' in event.payload) {
          this.config.onResultGenerated?.(event.payload.output as ResultGeneratedPayloadOutput, eventTaskId)
        } else {
          logger.warn(`Received result-generated event without output for task: ${eventTaskId}`)
        }
        break
      case 'task-finished':
        logger.info(`Task finished: ${eventTaskId}`)
        this.config.onTaskFinished?.(eventTaskId)
        if (eventTaskId === this.currentTaskId) {
          this.currentTaskId = null
        }
        break
      case 'task-failed':
        logger.error(`Task failed: ${eventTaskId}. Code: ${event.header.error_code}, Message: ${event.header.error_message}`)
        if (eventTaskId === this.currentTaskId && this.taskStartedReject) {
          this.taskStartedReject(new Error(`${event.header.error_code}: ${event.header.error_message}`))
          this.resetTaskStartedPromiseState()
        }
        this.config.onTaskFailed?.(event.header.error_code!, event.header.error_message!, eventTaskId)
        if (eventTaskId === this.currentTaskId) {
          this.currentTaskId = null
        }
        break
      default:
        logger.warn('Received unknown event type from Gummy:', event)
    }
  }

  public async connect(): Promise<void> {
    if (this.wsClient.isOpen) {
      logger.debug('WebSocket already connected.')
      return Promise.resolve()
    }
    logger.info('GummyService attempting to connect WebSocket...')
    return this.wsClient.connect()
  }

  public close(code: number = 1000, reason: string = 'Client closed Gummy service connection'): void {
    logger.info('GummyService closing WebSocket connection...')
    this.wsClient.close(code, reason)
    if (this.currentTaskId && this.taskStartedReject) {
      this.taskStartedReject(new Error('Connection explicitly closed by client during task startup.'))
    }
    this.currentTaskId = null
    this.resetTaskStartedPromiseState()
  }

  private generateTaskId(): string {
    return randomUUID().replace(/-/g, '')
  }

  public async runTask(parameters: RunTaskParameters, model: 'gummy-realtime-v1' = DEFAULT_MODEL_NAME): Promise<string> {
    if (!this.wsClient.isOpen) {
      const connectErrorMsg = 'WebSocket is not connected. Call connect() first.'
      logger.error(connectErrorMsg)
      throw new Error(connectErrorMsg)
    }

    if (this.currentTaskId) {
      logger.warn(
        `A task with ID ${this.currentTaskId} is considered active. Ensure previous task is finished or connection reset. Starting new task will overwrite context.`,
      )
      // As per Gummy docs, a failed task closes the connection, a finished one allows reuse.
      // If currentTaskId is set, it implies we haven't received a terminal event for it.
    }

    this.currentTaskId = this.generateTaskId()
    logger.info(`Starting new Gummy task with ID: ${this.currentTaskId}`)

    const command: RunTaskCommand = {
      header: {
        action: 'run-task',
        task_id: this.currentTaskId,
        streaming: STREAMING_DUPLEX,
      },
      payload: {
        model,
        parameters,
        input: {},
        task: TASK_ASR,
        task_group: TASK_GROUP_AUDIO,
        function: FUNCTION_RECOGNITION,
      },
    }

    // Setup promise to wait for task-started event
    this.taskStartedPromise = new Promise<void>((resolve, reject) => {
      this.taskStartedResolve = resolve
      this.taskStartedReject = reject
    })

    try {
      this.wsClient.send(command)
      logger.debug('run-task command sent for task ID:', this.currentTaskId)
      await this.taskStartedPromise // Wait for 'task-started' event
      logger.info(`Task ${this.currentTaskId} successfully started.`)
      return this.currentTaskId
    } catch (error) {
      logger.error(`Failed to send run-task command or task did not start for ${this.currentTaskId}:`, error instanceof Error ? error.message : error)
      this.currentTaskId = null
      this.resetTaskStartedPromiseState() // Clean up promise state
      throw error
    }
  }

  /**
   * 发送音频数据块。
   * Gummy API 要求音频数据作为原始二进制帧发送。
   * `AliyunWebSocketClient` 已更新以支持直接发送 `Buffer`。
   *
   * @param audioChunk 要发送的音频数据块 (Buffer)。
   * @param taskId 音频数据所属的任务 ID。必须与当前活动任务 ID 匹配。
   */
  public sendAudioChunk(audioChunk: Buffer, taskId: string): void {
    if (!this.currentTaskId || taskId !== this.currentTaskId) {
      const errorMsg = `Cannot send audio. No active task or taskId mismatch. Current: ${this.currentTaskId}, Provided: ${taskId}`
      logger.error(errorMsg)
      throw new Error(errorMsg)
    }
    if (!this.wsClient.isOpen) {
      const errorMsg = 'WebSocket is not connected. Cannot send audio.'
      logger.error(errorMsg)
      throw new Error(errorMsg)
    }

    // 直接发送 Buffer，AliyunWebSocketClient 现在支持此操作
    this.wsClient.send(audioChunk)

    logger.debug(`Sent audio chunk for task ${taskId}. Size: ${audioChunk.length} bytes.`)
  }

  public async finishTask(taskId: string): Promise<void> {
    if (!this.currentTaskId || taskId !== this.currentTaskId) {
      const errorMsg = `Cannot finish task. Task ID mismatch or no active task. Current: ${this.currentTaskId}, Provided: ${taskId}`
      logger.error(errorMsg)
      throw new Error(errorMsg)
    }
    if (!this.wsClient.isOpen) {
      const errorMsg = 'WebSocket is not connected. Cannot finish task.'
      logger.error(errorMsg)
      throw new Error(errorMsg)
    }

    const command: FinishTaskCommand = {
      header: {
        action: 'finish-task',
        task_id: taskId,
        streaming: STREAMING_DUPLEX,
      },
      payload: {
        input: {},
      },
    }

    this.wsClient.send(command)
    logger.info(`finish-task command sent for task ID: ${taskId}`)
    // currentTaskId is cleared by task-finished or task-failed event handlers.
  }

  public get isOpen(): boolean {
    return this.wsClient.isOpen
  }

  public getCurrentTaskId(): string | null {
    return this.currentTaskId
  }
}
