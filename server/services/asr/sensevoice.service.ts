import { consola } from 'consola'
import { config } from '~/server/config'
import type { SenseVoiceQueryTaskResponse, SenseVoiceSubmitTaskParams, SenseVoiceSubmitTaskResponse } from '~/types/sensevoice'

const logger = consola.withTag('SenseVoiceService')

const SENSEVOICE_API_URL = 'https://dashscope.aliyuncs.com/api/v1/services/audio/asr/transcription'
const SENSEVOICE_TASK_QUERY_URL = 'https://dashscope.aliyuncs.com/api/v1/tasks/'
const DEFAULT_MODEL_NAME = 'sensevoice-v1'

export class SenseVoiceService {
  private apiKey: string

  constructor(apiKey?: string) {
    this.apiKey = apiKey || config.aliyun.apiKey
    if (!this.apiKey) throw new Error('SenseVoice API Key 未配置')
  }

  /**
   * 提交语音识别任务
   * @param params 任务参数
   */
  public async submitTask(params: SenseVoiceSubmitTaskParams): Promise<SenseVoiceSubmitTaskResponse> {
    const headers = {
      Authorization: `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      'X-DashScope-Async': 'enable',
    }
    const body = JSON.stringify({
      model: DEFAULT_MODEL_NAME,
      input: { file_urls: params.fileUrls },
      parameters: {
        channel_id: params.channelId ?? [0],
        disfluency_removal_enabled: params.disfluencyRemovalEnabled ?? false,
        language_hints: params.languageHints ?? ['auto'],
      },
    })
    logger.info('SenseVoice 提交任务', body)
    const res = await fetch(SENSEVOICE_API_URL, {
      method: 'POST',
      headers,
      body,
    })
    const json = await res.json()
    if (!res.ok) {
      logger.error('SenseVoice 提交任务失败', json)
      throw new Error(json.message || 'SenseVoice 提交任务失败')
    }
    return json.output as SenseVoiceSubmitTaskResponse
  }

  /**
   * 查询语音识别任务状态和结果
   * @param taskId 任务 ID
   */
  public async queryTask(taskId: string): Promise<SenseVoiceQueryTaskResponse> {
    const headers = {
      Authorization: `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
    }
    logger.info('SenseVoice 查询任务', taskId)
    const res = await fetch(`${SENSEVOICE_TASK_QUERY_URL}${taskId}`, {
      method: 'POST',
      headers,
    })
    const json = await res.json()
    if (!res.ok) {
      logger.error('SenseVoice 查询任务失败', json)
      throw new Error(json.message || 'SenseVoice 查询任务失败')
    }
    return json.output as SenseVoiceQueryTaskResponse
  }
}

export const senseVoiceService = new SenseVoiceService()
