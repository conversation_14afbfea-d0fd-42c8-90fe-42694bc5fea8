import { Buffer } from 'buffer'
import crypto from 'crypto'
import WebSocket from 'ws'
import { config } from '~/server/config'
import { sleep } from '~/utils/timeUtils'

// API 常量
const STT_API_HOST = 'iat.xf-yun.com'
const STT_API_PATH = '/v1'
const STT_WEBSOCKET_URL = `wss://${STT_API_HOST}${STT_API_PATH}`
const DEFAULT_TIMEOUT_MS = 60000 // 默认超时时间 60 秒
const FRAME_SIZE = 1280 // 每一帧音频大小
const FRAME_INTERVAL = 40 // 发送每帧的间隔时间 (ms)

/**
 * 讯飞 STT 服务参数 (parameter.iat)
 * @see https://www.xfyun.cn/doc/asr/voicedictation/API.html#%E6%8E%A5%E5%8F%A3%E8%A6%81%E6%B1%82
 */
export type XunfeiSttParams = {
  domain: 'slm' // 领域: 大模型中文语音识别
  language: 'zh_cn' // 语种
  accent?: 'mandarin' // 方言: 普通话
  eos?: number // 静音后端点: 多少毫秒后停止识别
  vinfo?: 1 // 句子级流式识别
  dwa?: 'wpgs' // 动态修正
  result?: {
    encoding: 'utf8'
    compress: 'raw'
    format: 'json'
  }
}

/**
 * 音频数据描述 (payload.audio)
 */
export type XunfeiSttAudioMeta = {
  encoding: 'raw' | 'lame' // pcm / mp3
  sample_rate: 16000 | 8000
  channels: 1
  bit_depth: 16
}

type XunfeiSttAudioPayload = XunfeiSttAudioMeta & {
  seq: number
  status: 0 | 1 | 2 // 0:首帧, 1:中间帧, 2:最后一帧
  audio: string // base64 编码的音频数据
}

/**
 * 讯飞 STT API 请求体结构
 */
type XunfeiSttRequestPayload = {
  header: {
    app_id: string
    status: 0 | 1 | 2
  }
  parameter?: {
    iat: XunfeiSttParams
  }
  payload?: {
    audio: XunfeiSttAudioPayload
  }
}

/**
 * 返回结果中 result.text base64解码后的内容
 */
type DecodedTextResult = {
  sn: number // 结果序号
  ls: boolean // 是否最后一片
  ws: {
    cw: {
      w: string // 词
    }[]
  }[]
}

/**
 * 讯飞 STT API WebSocket 响应消息结构
 */
type XunfeiSttResponseMessage = {
  header: {
    code: number
    message: string
    sid?: string
    status: 0 | 1 | 2
  }
  payload?: {
    result: {
      text: string // base64 编码的 JSON 字符串 (DecodedTextResult)
    }
  }
}

/**
 * 生成讯飞 STT API 的鉴权 URL
 * @returns {string} 鉴权后的 WebSocket URL
 * @throws {Error} 如果缺少必要的环境变量
 */
function getAuthenticatedUrl(): string {
  const { apiKey, apiSecret } = config.xunfeiAsr

  if (!apiKey || !apiSecret) {
    throw new Error('xunfei asr config error')
  }

  const date = new Date().toUTCString()
  const signatureOrigin = `host: ${STT_API_HOST}\ndate: ${date}\nGET ${STT_API_PATH} HTTP/1.1`
  const signatureSha = crypto.createHmac('sha256', apiSecret).update(signatureOrigin).digest()
  const signature = signatureSha.toString('base64')

  const authorizationOrigin = `api_key="${apiKey}", algorithm="hmac-sha256", headers="host date request-line", signature="${signature}"`
  const authorization = Buffer.from(authorizationOrigin).toString('base64')

  const params = new URLSearchParams({
    authorization,
    date,
    host: STT_API_HOST,
  })

  return `${STT_WEBSOCKET_URL}?${params.toString()}`
}

/**
 * 流式事件数据类型
 */
export type StreamEventData = {
  type: 'progress' | 'result' | 'complete' | 'error'
  data: {
    text?: string
    isPartial?: boolean
    confidence?: number
    timestamp?: number
    audioInfo?: {
      fileName?: string
      fileType?: string
      fileSize?: number
      encoding?: string
      sampleRate?: number
    }
    error?: string
  }
}

/**
 * 调用讯飞语音识别服务
 * @param {Buffer} audioBuffer 要识别的音频Buffer
 * @param {XunfeiSttParams} sttParams 业务参数
 * @param {XunfeiSttAudioMeta} audioMeta 音频元数据
 * @param {number} [timeout=DEFAULT_TIMEOUT_MS] 操作超时时间（毫秒）
 * @returns {Promise<string>} 解析为识别出的文本
 * @throws {Error} 如果环境变量未设置或 API 调用失败
 */
export async function recognizeSpeech(
  audioBuffer: Buffer,
  sttParams: XunfeiSttParams,
  audioMeta: XunfeiSttAudioMeta,
  timeout: number = DEFAULT_TIMEOUT_MS,
): Promise<string> {
  const { appId } = config.xunfeiAsr
  if (!appId) {
    throw new Error('xunfei asr app id error')
  }

  const authenticatedUrl = getAuthenticatedUrl()

  return new Promise((resolve, reject) => {
    const ws = new WebSocket(authenticatedUrl)
    let latestCompleteText = '' // 保存最新的完整文本
    let finalPunctuation = '' // 保存最后的标点符号
    let operationTimedOut = false
    let isLastResult = false

    const timeoutId = setTimeout(() => {
      operationTimedOut = true
      ws.close(1006, 'Operation timed out')
      reject(new Error(`xunfei stt operation timeout (${timeout}ms)`))
    }, timeout)

    const cleanup = () => {
      clearTimeout(timeoutId)
    }

    ws.onopen = async () => {
      if (operationTimedOut) return
      try {
        // 1. 发送第一帧 (包含参数)
        const firstFramePayload: XunfeiSttRequestPayload = {
          header: { app_id: appId, status: 0 },
          parameter: {
            iat: {
              ...sttParams,
              result: { encoding: 'utf8', compress: 'raw', format: 'json' },
            },
          },
          payload: {
            audio: {
              ...audioMeta,
              status: 0,
              seq: 0,
              audio: audioBuffer.subarray(0, FRAME_SIZE).toString('base64'),
            },
          },
        }
        ws.send(JSON.stringify(firstFramePayload))

        // 2. 发送中间帧
        let seq = 1
        for (let i = FRAME_SIZE; i < audioBuffer.length; i += FRAME_SIZE) {
          if (operationTimedOut) break

          await sleep(FRAME_INTERVAL)
          const chunk = audioBuffer.subarray(i, i + FRAME_SIZE)
          const middleFramePayload: XunfeiSttRequestPayload = {
            header: { app_id: appId, status: 1 },
            payload: {
              audio: {
                ...audioMeta,
                status: 1,
                seq,
                audio: chunk.toString('base64'),
              },
            },
          }
          ws.send(JSON.stringify(middleFramePayload))
          seq++
        }

        // 3. 发送结束帧
        if (!operationTimedOut) {
          await sleep(FRAME_INTERVAL)
          const lastFramePayload: XunfeiSttRequestPayload = {
            header: { app_id: appId, status: 2 },
            payload: {
              audio: {
                ...audioMeta,
                status: 2,
                seq,
                audio: '',
              },
            },
          }
          ws.send(JSON.stringify(lastFramePayload))
        }
      } catch (error) {
        const errorMessage = 'xunfei stt send data error'
        console.error('[讯飞STT] 发送数据失败:', errorMessage, error)
        if (!operationTimedOut) {
          cleanup()
          reject(new Error(errorMessage + (error instanceof Error ? ` Details: ${error.message}` : '')))
        }
      }
    }

    ws.onmessage = event => {
      if (operationTimedOut) return
      try {
        const message = JSON.parse(event.data.toString()) as XunfeiSttResponseMessage

        if (message.header.code !== 0) {
          const errorMessage = `xunfei stt api error: ${message.header.message} (Code: ${message.header.code})`
          console.error('[讯飞STT] API错误:', errorMessage)
          cleanup()
          reject(new Error(errorMessage))
          return
        }

        if (message.payload?.result?.text) {
          const resultJson = Buffer.from(message.payload.result.text, 'base64').toString('utf-8')
          const result = JSON.parse(resultJson) as DecodedTextResult

          // 提取当前片段的文本内容
          let segmentText = ''
          result.ws.forEach(wordInfo => {
            wordInfo.cw.forEach(charInfo => {
              segmentText += charInfo.w
            })
          })

          // 检查是否为最后一个结果片段
          if (result.ls) {
            isLastResult = true
            // 最后一个片段通常是标点符号
            finalPunctuation = segmentText
          } else {
            // 中间片段是渐进式累积的完整文本，保存最新的
            if (segmentText.trim()) {
              latestCompleteText = segmentText
            }
          }
        }

        // 当收到最后一帧或最后一个结果片段时，组合所有结果
        if (message.header.status === 2 || isLastResult) {
          // 组合最终结果：最新的完整文本 + 最后的标点符号
          const finalText = latestCompleteText + finalPunctuation

          cleanup()
          resolve(finalText)
        }
      } catch (error) {
        const errorMessage = 'xunfei stt handle message error'
        console.error('[讯飞STT] 处理消息失败:', errorMessage, error)
        if (!operationTimedOut) {
          cleanup()
          reject(new Error(errorMessage + (error instanceof Error ? ` Details: ${error.message}` : '')))
        }
      }
    }

    ws.onerror = errorEvent => {
      if (operationTimedOut) return
      const errorMessage = `xunfei stt websocket error: ${errorEvent.message}`
      console.error(errorMessage, errorEvent.error)
      cleanup()
      reject(new Error(errorMessage))
    }

    ws.onclose = () => {
      cleanup()
    }
  })
}

/**
 * 调用讯飞语音识别服务 - 流式版本
 * @param {Buffer} audioBuffer 要识别的音频Buffer
 * @param {XunfeiSttParams} sttParams 业务参数
 * @param {XunfeiSttAudioMeta} audioMeta 音频元数据
 * @param {number} [timeout=DEFAULT_TIMEOUT_MS] 操作超时时间（毫秒）
 * @param {Function} onStream 流式数据回调函数
 * @returns {Promise<void>}
 * @throws {Error} 如果环境变量未设置或 API 调用失败
 */
export async function recognizeSpeechStream(
  audioBuffer: Buffer,
  sttParams: XunfeiSttParams,
  audioMeta: XunfeiSttAudioMeta,
  timeout: number = DEFAULT_TIMEOUT_MS,
  onStream: (data: StreamEventData) => Promise<void>,
): Promise<void> {
  const { appId } = config.xunfeiAsr
  if (!appId) {
    throw new Error('xunfei asr app id error')
  }

  const authenticatedUrl = getAuthenticatedUrl()

  return new Promise((resolve, reject) => {
    const ws = new WebSocket(authenticatedUrl)
    let latestCompleteText = '' // 保存最新的完整文本
    let finalPunctuation = '' // 保存最后的标点符号
    let operationTimedOut = false
    let isLastResult = false

    const timeoutId = setTimeout(() => {
      operationTimedOut = true
      ws.close(1006, 'Operation timed out')
      reject(new Error(`xunfei stt operation timeout (${timeout}ms)`))
    }, timeout)

    const cleanup = () => {
      clearTimeout(timeoutId)
    }

    ws.onopen = async () => {
      if (operationTimedOut) return
      try {
        // 1. 发送第一帧 (包含参数)
        const firstFramePayload: XunfeiSttRequestPayload = {
          header: { app_id: appId, status: 0 },
          parameter: {
            iat: {
              ...sttParams,
              result: { encoding: 'utf8', compress: 'raw', format: 'json' },
            },
          },
          payload: {
            audio: {
              ...audioMeta,
              status: 0,
              seq: 0,
              audio: audioBuffer.subarray(0, FRAME_SIZE).toString('base64'),
            },
          },
        }
        ws.send(JSON.stringify(firstFramePayload))

        // 2. 发送中间帧
        let seq = 1
        for (let i = FRAME_SIZE; i < audioBuffer.length; i += FRAME_SIZE) {
          if (operationTimedOut) break

          await sleep(FRAME_INTERVAL)
          const chunk = audioBuffer.subarray(i, i + FRAME_SIZE)
          const middleFramePayload: XunfeiSttRequestPayload = {
            header: { app_id: appId, status: 1 },
            payload: {
              audio: {
                ...audioMeta,
                status: 1,
                seq,
                audio: chunk.toString('base64'),
              },
            },
          }
          ws.send(JSON.stringify(middleFramePayload))
          seq++
        }

        // 3. 发送结束帧
        if (!operationTimedOut) {
          await sleep(FRAME_INTERVAL)
          const lastFramePayload: XunfeiSttRequestPayload = {
            header: { app_id: appId, status: 2 },
            payload: {
              audio: {
                ...audioMeta,
                status: 2,
                seq,
                audio: '',
              },
            },
          }
          ws.send(JSON.stringify(lastFramePayload))
        }
      } catch (error) {
        const errorMessage = 'xunfei stt send data error'
        console.error('[讯飞STT] 发送数据失败:', errorMessage, error)
        if (!operationTimedOut) {
          cleanup()
          reject(new Error(errorMessage + (error instanceof Error ? ` Details: ${error.message}` : '')))
        }
      }
    }

    ws.onmessage = async event => {
      if (operationTimedOut) return
      try {
        const message = JSON.parse(event.data.toString()) as XunfeiSttResponseMessage

        if (message.header.code !== 0) {
          const errorMessage = `xunfei stt api error: ${message.header.message} (Code: ${message.header.code})`
          console.error('[讯飞STT] API错误:', errorMessage)

          // 发送错误事件
          await onStream({
            type: 'error',
            data: { error: errorMessage },
          })

          cleanup()
          reject(new Error(errorMessage))
          return
        }

        if (message.payload?.result?.text) {
          const resultJson = Buffer.from(message.payload.result.text, 'base64').toString('utf-8')
          const result = JSON.parse(resultJson) as DecodedTextResult

          // 提取当前片段的文本内容
          let segmentText = ''
          result.ws.forEach(wordInfo => {
            wordInfo.cw.forEach(charInfo => {
              segmentText += charInfo.w
            })
          })

          // 检查是否为最后一个结果片段
          if (result.ls) {
            isLastResult = true
            // 最后一个片段通常是标点符号
            finalPunctuation = segmentText

            // 发送最终完整结果
            const finalText = latestCompleteText + finalPunctuation
            await onStream({
              type: 'complete',
              data: {
                text: finalText,
                isPartial: false,
                timestamp: Date.now(),
              },
            })
          } else {
            // 中间片段是渐进式累积的完整文本，保存最新的
            if (segmentText.trim()) {
              latestCompleteText = segmentText

              // 发送中间结果
              await onStream({
                type: 'result',
                data: {
                  text: latestCompleteText,
                  isPartial: true,
                  timestamp: Date.now(),
                },
              })
            }
          }
        }

        // 当收到最后一帧或最后一个结果片段时，结束识别
        if (message.header.status === 2 || isLastResult) {
          cleanup()
          resolve()
        }
      } catch (error) {
        const errorMessage = 'xunfei stt handle message error'
        console.error('[讯飞STT] 处理消息失败:', errorMessage, error)

        // 发送错误事件
        await onStream({
          type: 'error',
          data: { error: errorMessage + (error instanceof Error ? ` Details: ${error.message}` : '') },
        })

        if (!operationTimedOut) {
          cleanup()
          reject(new Error(errorMessage + (error instanceof Error ? ` Details: ${error.message}` : '')))
        }
      }
    }

    ws.onerror = async errorEvent => {
      if (operationTimedOut) return
      const errorMessage = `xunfei stt websocket error: ${errorEvent.message}`
      console.error('[讯飞STT] WebSocket错误:', errorMessage, errorEvent.error)

      // 发送错误事件
      await onStream({
        type: 'error',
        data: { error: errorMessage },
      })

      cleanup()
      reject(new Error(errorMessage))
    }

    ws.onclose = () => {
      cleanup()
    }
  })
}
