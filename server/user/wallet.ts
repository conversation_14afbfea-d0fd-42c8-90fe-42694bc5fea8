import { CREDIT_UNIT } from '~/server/config'
import { models, modelType } from '../ai/constants'
export async function updateTokenUsage(
  userId: string,
  model: { inputTokenRatio: number; outputTokenRatio: number; model: modelType },
  rawInputToken: number,
  rawOutputToken: number,
) {
  const inputToken = rawInputToken * model.inputTokenRatio
  const outputToken = rawOutputToken * model.outputTokenRatio
  let inputCost = 0
  let outputCost = 0
  const modelInfo = models[model.model]
  if (modelInfo?.costOf1KRawInputTokens && modelInfo?.costOf1KRawOutputTokens) {
    inputCost = (rawInputToken / CREDIT_UNIT) * modelInfo.costOf1KRawInputTokens
    outputCost = (rawOutputToken / CREDIT_UNIT) * modelInfo.costOf1KRawOutputTokens
  }

  const tokenCount = inputToken + outputToken
  const totalCost = inputCost + outputCost
  return {
    inputCost,
    outputCost,
    tokenCount,
    totalCost,
  }
}
