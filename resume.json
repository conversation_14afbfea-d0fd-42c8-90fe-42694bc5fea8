{

        "resume_text":"原始的简历内容",
        "job_info": "熟悉Mysql、SQLserver数据库 (硬性技能) --> 【简历证据】:技能列表中明确列出'熟悉Mysql、SQLserver数据库的相关技术,对数据库命令能熟练掌握'。",
        
        // analyzer SOP 的数据结构 【技能要求缺失、其他如软技能、工作经历要求】
        "analyzer_sop": [
            "【JD关键词】:熟悉Mysql、SQLserver数据库 (硬性技能) --> 【简历缺乏】:技能列表中没有明确列出'熟悉Mysql、SQLserver数据库的相关技术,对数据库命令能熟练掌握'。",
            "【JD关键词】:掌握Linux系统常用命令及部署 (硬性技能) --> 【简历缺乏】:技能列表中没有明确列出'掌握Linux系统的常用命令并了解其内核层，能够在Linux系统上部署hadoop集群、分布式存储'。",
            "【JD关键词】:掌握Shell编程 (硬性技能) --> 【简历缺乏】:技能列表中没有明确列出'掌握shell编程,并有一定的编写脚本能力'，且工作经历中没有提及'使用Shell编程语言编写自动化巡检脚本'。",
        ],

        // 诊断SOP的数据结构
        "diagnosis_sop": [
            "【量化数据缺失】工作经历描述缺乏具体数字或量化成果（如：脚本效率提升比例、监控覆盖率提升比例等），HR难以评估候选人的实际业绩贡献和能力水平，降低了竞争力。",
            "【技能展示不足】简历中提及'了解docker容器技术'，但未提供具体应用案例或成果，可能被HR视为技能掌握程度不足。",
            "【内容冗余/关联度低】简历中包含'自我评价'部分，内容较为泛泛，与目标职位关联度不高，占用了宝贵的展示空间，建议移除或大幅精简。",
            "【格式/细节问题风险】简历中存在'cup'（应为CPU）等明显笔误，可能影响HR对候选人严谨度和职业态度的评估。"
        ],

        // 改进建议SOP 的数据结构
        "suggestions_sop": [
            {
                "title": "优化简历标题和个人简介，直击HR关切点",
                "content": "当前简历的开篇未能迅速突出与系统运维工程师的核心匹配度。**为了让HR在最初几秒内就将您视为强相关候选人**，建议：1) 标题改为更具针对性的 '黄元雾 | 系统运维工程师 | Linux & 自动化运维'。2) 个人简介/专业概况部分，直接使用JD中的核心关键词（如'Linux', '自动化运维'），并量化核心成就。例如，将 '本人有1.5年的运维实践经验和有良好的职场处事能力' 修改为 '修改后：拥有1.5年系统运维经验，专注于Linux系统部署与自动化运维，近期通过编写Shell脚本将巡检效率提升30%【量化补充】。**这样的表述能立刻向HR证明您的核心价值与职位的匹配度。**'"
            },
            {
                "title": "强化工作/项目经历的成果量化，向HR证明价值",
                "content": "**HR在评估经验时，最看重的是实际成果和影响力。** 目前简历描述多为职责罗列，缺乏数据支撑。建议：对照JD核心职责，使用 STAR 法则重写关键经历，突出**量化成果**。例如，将 '使用Shell编程语言编写自动化巡检脚本' 修改为 '修改后：【项目/职责】针对客户主机监控需求，编写自动化巡检Shell脚本【行动】，部署至50+台主机【任务】，实现每日自动巡检并生成报告【结果】，巡检效率提升30%【量化成果】。**这能具体地向HR展示您的能力和贡献。**' 务必检查每项经历，思考能用哪些数字（提升%、降低%、节省￥、达成#）来证明您的价值。"
            },
            {
                "title": "策略性嵌入JD核心关键词/技能，确保筛选通过率",
                "content": "【**核心关键词是ATS筛选和HR快速识别匹配度的关键。** 简历目前对JD中的'docker', 'OpenStack'等关键词覆盖不足。建议：1) 在【技能列表】中，务必包含JD明确要求的硬技能。2) 在【工作/项目经历】描述中，自然地融入关键词。例如，描述Linux部署时加入'采用**OpenStack**部署服务，实现资源池化管理'；涉及容器技术时提及'探索并应用**docker**容器技术进行应用隔离部署，实现部署效率提升20%'。**关键在于将关键词置于具体的行动和成果语境中，向HR证明您确实拥有并应用了这些技能，而非简单堆砌。**"
            },
            {
                "title": "精简无关内容，优化版式，提升HR阅读体验",
                "content": "**HR筛选简历的时间通常很短，简洁、重点突出的简历更容易获得青睐。** 建议：1) 移除或大幅缩减'自我评价'部分，内容较为泛泛，与目标职位关联度不高。2) 统一格式：使用一致的项目符号、动词时态（如用过去时描述已完成的经历），段落间留白适当。**专业的版式能给HR留下良好印象，体现您的注重细节。** 确保PDF导出，避免格式混乱。"
            },
            {
                "title": "修正细节问题，提升专业度",
                "content": "**简历中的细节问题可能影响HR对候选人严谨度的评估。** 建议：1) 修正'cup'为'CPU'等明显笔误。2) 检查所有技术术语的大小写和拼写，确保专业性和一致性。**细节决定成败，专业的简历能给HR留下良好的第一印象。**"
            }
        ]
    }
