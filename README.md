# Hi-Offer开发文档

## 安装

### PNPM 版本要求

本项目使用 pnpm v8.14.3。请按照以下步骤确保使用正确的 pnpm 版本：

```bash
# 启用 Corepack（如果尚未启用）
corepack enable

# 安装指定版本的 pnpm
corepack prepare pnpm@8.14.3 --activate

# 验证 pnpm 版本
pnpm --version
```

### 安装依赖

确保安装依赖：

```bash
pnpm install
```

## 使用 Docker 运行 MongoDB

要使用 Docker 运行 MongoDB，可以使用以下命令：

```bash
docker run --name mongodb -d -p 27017:27017 mongo
```

此命令将在端口 27017 上启动一个无需认证的 MongoDB 实例。

## 使用 Docker Compose 运行开发环境

项目提供了 Docker Compose 配置，可以一键启动所需的 MongoDB 和 Redis 服务：

```bash
docker-compose up -d
```

此命令将启动：

- MongoDB：运行在端口 27017
- Redis：运行在端口 10637

服务的数据会被持久化存储在 Docker volumes 中，重启容器不会丢失数据。

要停止服务，可以运行：

```bash
docker-compose down
```

## 开发服务器

在 `http://localhost:3000` 上启动开发服务器：

```bash
pnpm dev
```

## 环境变量配置

项目使用加密的环境变量来增强安全性。请按照以下步骤设置环境变量：

### 解密环境变量

如果您已经获得了加密的 `.env.vault` 文件和 `.env.keys` 文件，可以使用以下命令解密环境变量：

```bash
pnpm env:decrypt
```

这将使用 `.env.keys` 中的私钥解密 `.env.vault` 文件，并生成 `.env` 文件。

### 创建新的环境变量

如果您需要创建新的环境变量文件，可以使用 `.env.example` 作为模板：

1. 根据您的环境设置适当的值
2. 使用以下命令加密环境变量：

```bash
pnpm env:encrypt
```

这将生成一个加密的 `.env.vault` 文件，可以安全地提交到代码仓库。

### 环境变量说明

以下是主要环境变量的说明：

```plaintext
# MongoDB 配置
MONGODB_URI=mongodb://<your-mongodb-host>:<port>/<your-database-name>  # MongoDB 连接 URI
NODE_ENV=development                              # 应用程序环境 (development, production, etc.)

# Redis 配置
REDIS_HOST=<your-redis-host>                      # Redis 主机地址
REDIS_PORT=<your-redis-port>                      # Redis 端口号
REDIS_DB=<your-redis-db>                          # Redis 数据库编号
REDIS_KEY_PREFIX=<your-key-prefix>                # Redis 键前缀

# Crypto 配置
NUXT_CRYPTO_KEY=<your-crypto-key>                 # 加密密钥，用于加密操作

# Auth 配置
AUTH_ORIGIN=http://<your-auth-origin>:<port>      # 认证服务的来源地址
AUTH_SECRET=<your-auth-secret>                    # 认证服务的密钥
NUXT_AUTH_SECRET=<your-nuxt-auth-secret>          # Nuxt 认证密钥
GITHUB_CLIENT_ID=<your-github-client-id>          # GitHub OAuth 客户端 ID
GITHUB_CLIENT_SECRET=<your-github-client-secret>  # GitHub OAuth 客户端密钥
GOOGLE_CLIENT_ID=<your-google-client-id>          # Google OAuth 客户端 ID
GOOGLE_CLIENT_SECRET=<your-google-client-secret>  # Google OAuth 客户端密钥

# OpenAI 配置
OPENAI_API_KEY=<your-openai-api-key>              # OpenAI API 密钥
OPENAI_BASE_URL=<your-openai-base-url>            # OpenAI API 基础 URL

# Mock 模式
MOCK_TRANSCRIPTION=false                          # 是否启用模拟转录
PORT=<your-port>                                  # 容器中应用程序运行的端口
```

确保将占位符替换为实际的值，以便应用程序能够正确连接到所需的服务。

### 注意事项

- 不要将 `.env` 和 `.env.keys` 文件提交到代码仓库
- 只有 `.env.vault` 和 `.env.example` 文件可以安全地提交
- 团队成员之间应通过安全渠道共享 `.env.keys` 文件

其他内容待补充......
