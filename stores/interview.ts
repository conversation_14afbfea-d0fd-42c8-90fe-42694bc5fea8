import { defineStore } from 'pinia'
import type { InterviewType } from '~/types/interview'

export type Interviewer = {
  id: string
  name: string
  title: string
  avatar?: string
}

export enum InterviewStep {
  POSITION_INFO = 1,
  RESUME_SELECTION = 2,
  PREPARATION_COMPLETE = 3,
}

export enum InterviewStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  ERROR = 'error',
}

// 添加明确的 StepStatus 类型，与 StepProgress 组件保持一致
export type StepStatus = 'current' | 'completed' | 'upcoming'

interface InterviewState {
  currentType: InterviewType | null
  isStarted: boolean
  startTime: number | null
  currentInterviewer: Interviewer | null
}

export const useInterviewStore = defineStore('interview', {
  state: (): InterviewState => ({
    currentType: null,
    isStarted: false,
    startTime: null,
    currentInterviewer: null,
  }),

  actions: {
    setType(type: InterviewType) {
      this.currentType = type
    },
    setInterviewer(interviewer: Interviewer) {
      this.currentInterviewer = interviewer
    },
    startInterview() {
      this.isStarted = true
      this.startTime = Date.now()
    },
    endInterview() {
      this.isStarted = false
      this.currentType = null
      this.startTime = null
      this.currentInterviewer = null
    },
  },
})
