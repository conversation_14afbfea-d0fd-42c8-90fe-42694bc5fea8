import { defineStore } from 'pinia'
import type { ApiResponse } from '~/types'

interface UserProfile {
  name: string
  bio: string
  image: string
}

export const useUserStore = defineStore('user', {
  state: (): UserProfile => ({
    name: '',
    bio: '',
    image: '',
  }),

  actions: {
    async fetchProfile() {
      try {
        const { data } = await useFetch<ApiResponse>('/api/user/profile')
        if (data.value?.data) {
          this.name = data.value.data.name || ''
          this.bio = data.value.data.bio || ''
          this.image = data.value.data.image || ''
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        throw error
      }
    },
  },
})
