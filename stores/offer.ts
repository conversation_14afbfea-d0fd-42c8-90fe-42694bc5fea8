import { defineStore } from 'pinia'
type Template = {
  title: string
  content: string
}

export const useOfferStore = defineStore('offer', () => {
  const currentTemplates = ref<Template[]>([])
  return {
    currentTemplates,
  }
})

export const DELAY_REASONS = [
  {
    id: 'waiting-other',
    label: '等待心仪公司结果/争取薪资调整',
    templates: [
      {
        title: '模板 1',
        content:
          'XX经理您好，非常感谢贵司的认可！目前我确实在同步等待另一家行业头部企业的终面结果。但必须坦诚告知，贵司的岗位方向与我职业规划高度契合，团队氛围也让我非常向往。如果能将base薪资调整到XX范围，我完全可以优先放弃其他机会立即确认意向。不知这个期望值是否有协商空间？我可以在3个工作日内给您最终答复。',
      },
      {
        title: '模板 2',
        content:
          'XX经理您好，感谢贵司的offer！目前我正在等待另一家公司的终面结果，预计本周内会有答复。贵司的岗位我非常感兴趣，希望能给我一些时间权衡。我会在下周一前给您明确答复。',
      },
    ],
  },
  {
    id: 'school-process',
    label: '利用校招流程缓冲时间',
    templates: [
      {
        title: '模板 1',
        content:
          '老师好，非常荣幸能通过贵司考核！目前学校的三方协议系统正在集中审核阶段，辅导员反馈最近教委系统升级，电子协议需要5-7个工作日才能完成备案。我会持续跟进审批进度，预计下周三前能完成所有手续。期间如果有任何需要补充的材料或流程，请您随时告知，我一定全力配合。',
      },
      {
        title: '模板 2',
        content: '老师好，感谢贵司的offer！目前学校的三方协议系统正在维护中，预计需要3-5个工作日才能恢复正常。我会及时跟进进度，尽快完成签约。',
      },
    ],
  },
  {
    id: 'handover',
    label: '离职交接过渡时间',
    template:
      'XX您好，特别感谢贵司发放offer的信任！关于入职时间，由于当前岗位涉及三个重点项目收尾交接，本着职业操守需要妥善完成工作移交。如果可以在XX月XX日之后入职，既能保证前公司业务平稳过渡，也能让我以更专注的状态投入新工作。考虑到贵司业务节奏，我可以在过渡期内提前了解一下公司的业务。本周四前我会与前司确认最终交接时间表，周五上午10点向您同步确切日期。您看这样的安排是否可行？',
  },
  {
    id: 'relocation',
    label: '换城市的时间成本',
    template:
      'Hi，XX您好呀！超开心能加入咱们团队！不过我从xx搬到xx、可能得花点时间呢。现在住的房子合同要提前解约，中介说要留足xx天转租，加上找房搬家..可能得xx月初才能到岗。但我会尽快搞定嘿嘿！大概XX[时间]就过去看房，要是着急的话，我也可以先远程参与新人培训的！',
  },
  {
    id: 'adaptation',
    label: '适应环境，调整工作状态',
    template:
      'Hi XX哥/姐！有个小请求想和您商量下～这次从xx搬到xx，除了找房搬家这些体力活，我还想预留点时间适应新环境，调整到最好的状态来迎接工作！比如提前摸清通勤路线、熟悉一下周边环境，计划这两周搞定房子和搬家，剩下3天专门调整作息、预习业务手册，保证入职后火力全开！如果比较着急的话，过渡期我还可以先线上参加新人培训，绝不耽误进度！您看这样安排可以嘛？',
  },
]
export const DELAY_PRINCIPLES = [
  '保持3天一次的主动反馈节奏，每次沟通必带新信息',
  '重要节点提前24小时告知（如"明天下午三点前一定回复"）',
  '善用客观理由：家庭商议、导师建议、证件办理等中性因素',
  '每次沟通必表诚意："这个岗位始终是我的第一选择""已拒绝其他两家邀约"',
]

export const DECLINE_REASONS = [
  {
    id: 'better-opportunity',
    label: '更好的机会',
    templates: [
      {
        title: '模板 1',
        content:
          'XX经理您好，非常感谢贵司的认可和offer！经过慎重考虑，我决定接受另一家公司的offer。这个决定主要基于个人职业发展规划的考虑，希望能在一个更适合我长期发展的环境中工作。再次感谢贵司给予的机会，祝愿贵司发展越来越好！',
      },
      {
        title: '模板 2',
        content:
          'XX您好，感谢贵司的offer！经过深思熟虑，我决定接受另一家公司的offer。这个决定主要基于个人职业发展规划的考虑。感谢贵司的赏识，祝愿贵司蒸蒸日上！',
      },
    ],
  },
  {
    id: 'location',
    label: '地理位置',
    templates: [
      {
        title: '模板 1',
        content:
          'XX经理您好，非常感谢贵司的offer！经过慎重考虑，由于工作地点与我的居住地距离较远，通勤时间较长，可能会影响到工作质量和生活平衡。因此，我决定婉拒贵司的offer。感谢贵司的赏识，祝愿贵司发展越来越好！',
      },
      {
        title: '模板 2',
        content: 'XX您好，感谢贵司的offer！由于工作地点与我的居住地距离较远，考虑到通勤时间等因素，我决定婉拒贵司的offer。感谢贵司的赏识，祝愿贵司蒸蒸日上！',
      },
    ],
  },
  {
    id: 'salary',
    label: '薪资待遇',
    templates: [
      {
        title: '模板 1',
        content:
          'XX经理您好，非常感谢贵司的offer！经过慎重考虑，由于薪资待遇与我的期望有一定差距，考虑到生活成本等因素，我决定婉拒贵司的offer。感谢贵司的赏识，祝愿贵司发展越来越好！',
      },
      {
        title: '模板 2',
        content: 'XX您好，感谢贵司的offer！由于薪资待遇与我的期望有一定差距，我决定婉拒贵司的offer。感谢贵司的赏识，祝愿贵司蒸蒸日上！',
      },
    ],
  },
  {
    id: 'work-life-balance',
    label: '工作生活平衡',
    templates: [
      {
        title: '模板 1',
        content:
          'XX经理您好，非常感谢贵司的offer！经过慎重考虑，由于工作强度较大，可能会影响到我的工作生活平衡，我决定婉拒贵司的offer。感谢贵司的赏识，祝愿贵司发展越来越好！',
      },
      {
        title: '模板 2',
        content: 'XX您好，感谢贵司的offer！考虑到工作强度等因素，我决定婉拒贵司的offer。感谢贵司的赏识，祝愿贵司蒸蒸日上！',
      },
    ],
  },
  {
    id: 'career-path',
    label: '职业发展',
    templates: [
      {
        title: '模板 1',
        content:
          'XX经理您好，非常感谢贵司的offer！经过慎重考虑，由于岗位发展方向与我的职业规划有一定差异，我决定婉拒贵司的offer。感谢贵司的赏识，祝愿贵司发展越来越好！',
      },
      {
        title: '模板 2',
        content: 'XX您好，感谢贵司的offer！由于岗位发展方向与我的职业规划有一定差异，我决定婉拒贵司的offer。感谢贵司的赏识，祝愿贵司蒸蒸日上！',
      },
    ],
  },
]

export const DECLINE_PRINCIPLES = [
  '保持3天一次的主动反馈节奏，每次沟通必带新信息',
  '重要节点提前24小时告知（如"明天下午三点前一定回复"）',
  '善用客观理由：家庭商议、导师建议、证件办理等中性因素',
  '每次沟通必表诚意："这个岗位始终是我的第一选择""已拒绝其他两家邀约"',
]

export const SALARY_PRINCIPLES = [
  '保持3天一次的主动反馈节奏，每次沟通必带新信息',
  '重要节点提前24小时告知（如"明天下午三点前一定回复"）',
  '善用客观理由：家庭商议、导师建议、证件办理等中性因素',
  '每次沟通必表诚意："这个岗位始终是我的第一选择""已拒绝其他两家邀约"',
]
export const SALARY_REASONS = [
  {
    id: 'market-value',
    label: '市场价值',
    templates: [
      {
        title: '模板 1',
        content:
          'XX经理您好，非常感谢贵司的认可！我注意到贵司提供的薪资与当前市场行情有一定差距。根据我的经验和技能，以及市场上类似职位的薪资水平，我希望能够将base薪资调整到XX范围。我相信这个期望值能够更好地反映我的市场价值，同时也表明我对加入贵司的诚意。不知这个期望值是否有协商空间？',
      },
      {
        title: '模板 2',
        content:
          'XX经理您好，感谢贵司的offer！我注意到贵司提供的薪资与当前市场行情有一定差距。根据我的调研，市场上类似职位的薪资水平在XX范围。我希望能够将base薪资调整到这个范围，以更好地反映我的市场价值。不知这个期望值是否有协商空间？',
      },
    ],
  },
  {
    id: 'experience',
    label: '经验与技能',
    templates: [
      {
        title: '模板 1',
        content:
          'XX您好，非常感谢贵司的offer！我注意到贵司提供的薪资与我的经验和技能水平有一定差距。我在这个领域有X年的经验，并且掌握了多项核心技能，如XX、XX等。我相信这些经验和技能能够为贵司带来更大的价值。因此，我希望能够将base薪资调整到XX范围。不知这个期望值是否有协商空间？',
      },
      {
        title: '模板 2',
        content:
          'XX您好，感谢贵司的offer！我在这个领域有X年的经验，并且掌握了多项核心技能。我相信这些经验和技能能够为贵司带来更大的价值。因此，我希望能够将base薪资调整到XX范围。不知这个期望值是否有协商空间？',
      },
    ],
  },
  {
    id: 'responsibility',
    label: '岗位职责',
    templates: [
      {
        title: '模板 1',
        content:
          'XX您好，非常感谢贵司的认可！我注意到贵司提供的薪资与岗位职责的复杂度有一定差距。这个岗位需要承担XX、XX等多项重要职责，我相信我的经验和技能能够胜任这些工作。因此，我希望能够将base薪资调整到XX范围。不知这个期望值是否有协商空间？',
      },
      {
        title: '模板 2',
        content:
          'XX您好，感谢贵司的offer！这个岗位需要承担多项重要职责，我相信我的经验和技能能够胜任这些工作。因此，我希望能够将base薪资调整到XX范围。不知这个期望值是否有协商空间？',
      },
    ],
  },
  {
    id: 'growth',
    label: '成长空间',
    templates: [
      {
        title: '模板 1',
        content:
          'XX您好，非常感谢贵司的offer！我注意到贵司提供的薪资与我的成长空间有一定差距。我相信在贵司的工作环境中，我能够不断成长和进步，为贵司带来更大的价值。因此，我希望能够将base薪资调整到XX范围。不知这个期望值是否有协商空间？',
      },
      {
        title: '模板 2',
        content:
          'XX您好，感谢贵司的offer！我相信在贵司的工作环境中，我能够不断成长和进步，为贵司带来更大的价值。因此，我希望能够将base薪资调整到XX范围。不知这个期望值是否有协商空间？',
      },
    ],
  },
  {
    id: 'other-offers',
    label: '其他offer',
    templates: [
      {
        title: '模板 1',
        content:
          'XX您好，非常感谢贵司的认可！我目前收到了其他公司的offer，薪资在XX范围。虽然贵司的岗位方向与我的职业规划高度契合，但薪资差距较大。因此，我希望能够将base薪资调整到XX范围。不知这个期望值是否有协商空间？',
      },
      {
        title: '模板 2',
        content:
          'XX您好，感谢贵司的offer！我目前收到了其他公司的offer，薪资在XX范围。贵司的岗位方向与我的职业规划高度契合，但薪资差距较大。因此，我希望能够将base薪资调整到XX范围。不知这个期望值是否有协商空间？',
      },
    ],
  },
]
