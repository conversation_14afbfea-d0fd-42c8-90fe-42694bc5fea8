import { defineStore } from 'pinia'
import type { InterviewType } from '~/types/interview'

export type PositionInfo = {
  name: string
  description: string
}

export const POSITION_INFO_LIST: Record<InterviewType, PositionInfo> = {
  'new-media-operation': {
    name: '新媒体运营',
    description:
      '负责企业新媒体矩阵（微信公众号、抖音、小红书、微博等平台）的体系化运营，制定并执行月度传播策略与内容日历。独立完成热点追踪、爆款文案创作及短视频脚本设计，持续提升内容传播效果。主导粉丝增长活动策划，通过数据分析工具监测转化效果，优化投放策略。管理外部MCN机构合作，协调设计、视频团队完成重点项目落地。要求具备3年以上美妆/快消行业运营经验，有成功的新媒体运营案例。熟悉各平台算法机制，能够快速响应热点，具备优秀的文案创作能力和数据分析能力。主导过品牌IP孵化项目，熟悉KOL/KOC合作模式，具备优秀的项目管理能力和团队协作能力。',
  },
  frontend: {
    name: '前端工程师',
    description:
      '负责企业级中后台系统的前端架构设计与核心模块开发，主导React技术栈的技术选型与工程化建设。熟练使用React18+TypeScript开发复杂交互功能，深入理解React Hooks、Context API等核心特性。主导微前端架构设计与实现，使用qiankun等框架构建可扩展的前端应用。开发可视化低代码平台组件库，基于Ant Design等UI框架进行二次开发。主导前端工程化建设，包括Webpack/Vite构建优化、自动化测试、CI/CD流程等。要求精通React技术栈，具有3年以上前端开发经验，有大型系统架构升级经验。熟悉前端性能优化、状态管理、组件设计等核心领域，具备优秀的工程化能力和问题解决能力。主导过大型项目的前端架构设计，熟悉前端安全防护方案，具备优秀的团队协作和沟通能力。',
  },
  backend: {
    name: '后端工程师',
    description:
      '负责企业级分布式系统的架构设计与核心模块开发，主导Java技术栈的技术选型与工程化建设。熟练使用Spring Boot/Spring Cloud Alibaba开发微服务架构，深入理解Spring核心原理与最佳实践。主导MySQL/PostgreSQL数据库设计与优化，使用MyBatis/JPA等ORM框架进行数据访问。开发RESTful API接口，实现接口文档自动化与接口测试。主导系统性能优化，包括JVM调优、数据库优化、缓存策略等。要求精通Java技术栈，具有3年以上后端开发经验，有大型系统架构设计经验。熟悉分布式系统设计、微服务治理、消息队列等核心领域，具备优秀的系统设计能力和问题解决能力。主导过大型分布式系统的架构设计，熟悉系统监控与运维方案，具备优秀的团队协作和沟通能力。',
  },
  fullstack: {
    name: '全栈工程师',
    description:
      '负责企业级中后台系统的前端和后端架构设计与核心模块开发，主导React技术栈的技术选型与工程化建设。熟练使用React18+TypeScript开发复杂交互功能，深入理解React Hooks、Context API等核心特性。同时，负责后端架构设计与核心模块开发，主导Java技术栈的技术选型与工程化建设。熟练使用Spring Boot/Spring Cloud Alibaba开发微服务架构，深入理解Spring核心原理与最佳实践。要求精通React和Java技术栈，具有3年以上前端和后端开发经验，有大型系统架构设计经验。熟悉前端性能优化、状态管理、组件设计等核心领域，以及后端性能优化、微服务治理、数据库设计等核心领域，具备优秀的工程化能力和问题解决能力。主导过大型项目的前端和后端架构设计，熟悉前端和后端安全防护方案，具备优秀的团队协作和沟通能力。',
  },
  'test-engineer': {
    name: '测试工程师',
    description:
      '构建全链路自动化测试体系，主导开发基于Selenium+TestNG的UI自动化框架与Jmeter分布式压测方案。设计智能化测试用例，提升测试覆盖率。完成系统性能调优，优化服务器资源使用。推动质量门禁系统建设，集成代码扫描与覆盖率检测。负责安全测试方案实施，识别并修复系统漏洞。要求精通混沌工程实践，具有3年以上测试开发经验，有大型项目质量保障经验。熟悉金融级容灾测试方案与ISO27001安全标准，具备优秀的测试设计能力和问题分析能力。主导过大型项目的测试体系建设，熟悉持续集成和持续部署流程，具备优秀的团队协作和沟通能力。',
  },
  algorithm: {
    name: '算法工程师',
    description:
      '负责核心业务场景的算法优化，包括个性化推荐系统、智能风控模型及NLP语义理解。主导特征工程开发，构建大规模数据处理平台。设计并实施在线AB测试方案，优化模型迭代效率。优化TensorFlow Serving推理性能，提升系统响应速度。要求精通联邦学习框架，具有3年以上算法落地经验，有大型AI项目优化经验。熟悉模型解释性方案与数据隐私合规要求，具备优秀的算法设计能力和工程实现能力。主导过大型AI项目的算法优化，熟悉分布式训练和推理优化，具备优秀的团队协作和沟通能力。',
  },
  'ui-designer': {
    name: 'UI设计师',
    description:
      '主导企业级设计系统（DesignSystem）的构建与迭代，制定原子化组件规范并开发Figma组件库。完成用户旅程地图绘制，通过可用性测试提升用户体验。设计数据可视化方案，开发图表模板库。主导设计走查机制建立，确保设计规范落地。要求精通动效设计（AfterEffects+Lottie），具有3年以上B端产品设计经验，有大型系统视觉升级经验。熟悉无障碍设计标准与国际化多语言适配方案，具备优秀的设计能力和用户体验意识。主导过大型项目的设计系统建设，熟悉设计趋势和行业规范，具备优秀的团队协作和沟通能力。',
  },
  'project-manager': {
    name: '项目经理',
    description:
      '管理IT项目的全生命周期，制定项目计划并实施敏捷开发流程。建立风险预警机制，有效控制项目风险。管理项目成本，确保项目利润率。主导客户需求分析，完成需求变更管控。要求持有PMP+CSM双认证，具有3年以上金融科技项目管理经验，有大型项目交付经验。熟悉CMMI3级实施标准与GDPR合规要求，具备优秀的项目管理能力和团队领导能力。主导过大型项目的全生命周期管理，熟悉项目风险控制和质量管理，具备优秀的沟通协调和问题解决能力。',
  },
  'product-manager': {
    name: '产品经理',
    description:
      '负责企业级产品全生命周期管理，主导用户需求分析并制定产品路线图。通过数据分析工具实现用户行为洞察，优化产品转化率。设计并实施A/B测试方案，提升用户体验。主导产品文档编写与PRD评审，确保开发团队理解产品需求。要求精通Axure、Figma等原型工具，具有3年以上B端产品经验，有成功产品迭代经验。熟悉敏捷开发流程与数据驱动决策方法，具备优秀的产品设计能力和市场洞察能力。主导过大型产品的全生命周期管理，熟悉产品战略规划和商业模式设计，具备优秀的团队协作和沟通能力。',
  },
  'sales-specialist': {
    name: '销售专员',
    description:
      '负责企业级解决方案的销售工作，制定并执行销售策略。通过客户画像分析，建立精准营销体系，提升客户转化率。主导商务谈判与合同签订，确保项目利润率。建立客户关系管理系统，提升客户满意度。要求具备3年以上企业级软件销售经验，有成功项目签约经验。熟悉招投标流程与商务谈判技巧，具备优秀的销售能力和客户关系管理能力。主导过大型项目的销售工作，熟悉行业动态和竞争态势，具备优秀的团队协作和沟通能力。',
  },
  marketing: {
    name: '市场营销',
    description:
      '制定并执行企业品牌营销战略，主导年度营销预算分配与ROI分析。通过多渠道整合营销（SEM、SEO、社交媒体、内容营销等），提升品牌影响力。设计并实施营销自动化方案，优化获客成本。主导市场调研与竞品分析，建立品牌差异化定位。要求精通Google Analytics、HubSpot等营销工具，具有3年以上B2B营销经验，有成功营销项目经验。熟悉营销漏斗优化与客户旅程设计，具备优秀的营销策划能力和数据分析能力。主导过大型品牌营销项目，熟悉数字营销和社交媒体运营，具备优秀的团队协作和沟通能力。',
  },
  'human-resource-specialist': {
    name: '人力资源专员',
    description:
      '负责企业人才招聘与培养体系建设，主导年度招聘计划制定与执行。通过人才画像分析，建立精准招聘渠道，优化招聘效率。设计并实施员工培训体系，提升员工留存率。主导绩效管理体系优化，提升员工满意度。要求精通招聘系统与人才测评工具，具有1-3年HR经验，有成功人才体系建设经验。熟悉劳动法规与员工关系管理，具备优秀的人才管理能力和组织发展能力。主导过大型组织的人才发展项目，熟悉人才梯队建设和继任者计划，具备优秀的团队协作和沟通能力。',
  },
  'customer-service': {
    name: '客服专员',
    description:
      '负责企业客户服务体系的建设与优化，主导客服团队培训与考核机制。通过客户反馈分析，建立问题分类体系，提升问题解决率。设计并实施客户满意度调查方案，提升客户满意度。主导客服知识库建设，优化问题响应效率。要求精通客服系统与CRM工具，具有3年以上客服管理经验，有大型客服团队管理经验。熟悉客户服务标准与投诉处理流程，具备优秀的服务管理能力和问题解决能力。主导过大型客服团队的管理工作，熟悉服务质量监控和绩效评估，具备优秀的团队协作和沟通能力。',
  },
}

export enum PrepareInterviewStep {
  POSITION_INFO = 1,
  RESUME_SELECTION = 2,
  PREPARATION_COMPLETE = 3,
}

// 定义模拟面试相关的状态管理
export const usePrepareInterviewStore = defineStore('prepareInterview', () => {
  // 步骤状态管理
  const currentStep = ref<PrepareInterviewStep>(PrepareInterviewStep.POSITION_INFO)

  // 岗位列表数据
  const positions = reactive([
    { id: 1, name: '新媒体运营', selected: false, key: 'new-media-operation' },
    { id: 2, name: '产品经理', selected: false, key: 'product-manager' },
    { id: 3, name: '销售专员', selected: false, key: 'sales-specialist' },
    { id: 4, name: '市场营销', selected: false, key: 'marketing' },
    { id: 5, name: '前端工程师', selected: false, key: 'frontend' },
    { id: 6, name: '后端工程师', selected: true, key: 'backend' },
    { id: 7, name: '测试工程师', selected: false, key: 'test-engineer' },
    { id: 8, name: '算法工程师', selected: false, key: 'algorithm' },
    { id: 9, name: '人力资源专员', selected: false, key: 'human-resource-specialist' },
    { id: 10, name: 'UI设计师', selected: false, key: 'ui-designer' },
    { id: 11, name: '项目经理', selected: false, key: 'project-manager' },
    { id: 12, name: '客户服务', selected: false, key: 'customer-service' },
  ]) as { id: number; name: string; selected: boolean; key: InterviewType }[]

  // 表单数据
  const formData = reactive({
    selectedInterviewType: 'backend' as InterviewType,
    positionName: '',
    positionDescription: '',
    companyDescription: '',
    resumeId: null as string | null,
    resumeContent: '',
    majorQuestions: '',
  })

  // 字数统计
  const characterCounts = reactive({
    positionDescription: 0,
    companyDescription: 0,
    resumeContent: 0,
  })

  // 获取选中岗位名称
  const getSelectedPositionName = computed(() => {
    const position = positions.find(p => p.key === formData.selectedInterviewType)
    return position ? position.name : ''
  })

  // 表单验证
  const isPositionFormValid = computed(() => {
    return formData.positionName.trim() !== '' || formData.selectedInterviewType !== null
  })

  const isResumeFormValid = computed(() => {
    return formData.resumeId !== null || formData.resumeContent.trim() !== ''
  })

  // 更新岗位描述字数
  const updatePositionDescriptionCount = () => {
    characterCounts.positionDescription = formData.positionDescription.length
  }

  // 更新公司描述字数
  const updateCompanyDescriptionCount = () => {
    characterCounts.companyDescription = formData.companyDescription.length
  }

  // 更新简历内容字数
  const updateResumeContentCount = () => {
    characterCounts.resumeContent = formData.resumeContent.length
  }

  // 选择岗位信息
  const selectPosition = (InterviewType: InterviewType) => {
    const positionInfo = POSITION_INFO_LIST[InterviewType]
    formData.selectedInterviewType = InterviewType
    formData.positionName = positionInfo.name
    formData.positionDescription = positionInfo.description
    characterCounts.positionDescription = formData.positionDescription.length
  }

  // 步骤导航
  const nextStep = () => {
    if (currentStep.value < PrepareInterviewStep.PREPARATION_COMPLETE) {
      currentStep.value++
    }
  }

  const prevStep = () => {
    if (currentStep.value > PrepareInterviewStep.POSITION_INFO) {
      currentStep.value--
    }
  }

  // 重置状态
  const resetInterviewSimulator = () => {
    currentStep.value = PrepareInterviewStep.POSITION_INFO
    formData.selectedInterviewType = 'backend'
    formData.positionName = ''
    formData.positionDescription = ''
    formData.companyDescription = ''
    formData.resumeId = null
    formData.resumeContent = ''
    characterCounts.positionDescription = 0
    characterCounts.companyDescription = 0
    characterCounts.resumeContent = 0
  }

  return {
    POSITION_INFO_LIST,
    // 状态
    currentStep,
    positions,
    formData,
    characterCounts,
    getSelectedPositionName,
    isPositionFormValid,
    isResumeFormValid,
    // 方法
    updatePositionDescriptionCount,
    updateCompanyDescriptionCount,
    updateResumeContentCount,
    selectPosition,
    nextStep,
    prevStep,
    resetInterviewSimulator,
  }
})
