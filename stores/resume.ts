import { defineStore } from 'pinia'
import { computed, reactive, ref } from 'vue'

export enum Tab {
  OPTIMIZE = 'optimize',
  INTERVIEWER = 'interviewer',
  WRITE = 'write',
  TRANSLATE = 'translate',
}

export enum Status {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  ERROR = 'error',
}

export enum InputType {
  TEXT = 'text',
  FILE = 'file',
}

export enum JobType {
  GENERAL = 'general',
  TECH = 'tech',
  PRODUCT = 'product',
  DESIGN = 'design',
  SALES = 'sales',
  MARKETING = 'marketing',
  OPERATIONS = 'operations',
  HR = 'hr',
  FINANCE = 'finance',
  CONSULTING = 'consulting',
  OTHERS = 'others',
}

export enum EnhancementMode {
  STANDARD = 'standard',
  PROFESSIONAL = 'professional',
}

export enum Module {
  DEFAULT = 'default',
  WORK_OR_PROJECT = 'workOrProject',
  EDUCATION = 'education',
  SKILL = 'skill',
  SELF_EVALUATION = 'selfEvaluation',
}

export enum FullResumeStrategy {
  QUANTIFIED = 'quantified', // 量化项目/工作成果
  QUALITATIVE = 'qualitative', // 润色措辞
  DIAGNOSIS = 'diagnosis', // 简历诊断
  TRANSLATE = 'translate', // 简历翻译
}

export enum Language {
  ZH_CN = 'zh-CN',
  ZH_TW = 'zh-TW',
  EN = 'en',
}

// 下拉菜单状态
export const dropdownState = reactive({
  language: false,
  enhancement: false,
  jobType: false,
})

// 保留常量定义在组件内部
export const LANGUAGE_OPTIONS = [
  { value: 'zh-CN', label: '中文简体' },
  { value: 'zh-TW', label: '中文繁体' },
  { value: 'en', label: '英文' },
]

export const ENHANCEMENT_OPTIONS = [
  { value: 'standard', label: '标准优化' },
  { value: 'professional', label: '专业优化' },
]

export const JOB_TYPE_OPTIONS = [
  { value: 'general', label: '通用' },
  { value: 'tech', label: '技术类' },
  { value: 'product', label: '产品类' },
  { value: 'design', label: '设计类' },
  { value: 'sales', label: '销售类' },
  { value: 'marketing', label: '市场类' },
  { value: 'operations', label: '运营类' },
  { value: 'hr', label: '人事类' },
  { value: 'finance', label: '财务类' },
  { value: 'consulting', label: '咨询类' },
  { value: 'others', label: '其他' },
]

export const RESUME_MODULE_OPTIONS = [
  { value: 'default', label: '通用' },
  { value: 'workOrProject', label: '工作/实习/项目经历' },
  { value: 'education', label: '教育经历' },
  { value: 'skill', label: '技能' },
  { value: 'selfEvaluation', label: '自我评价' },
]

export const RESUME_EDIT_MODULE_OPTIONS = [
  { value: 'workOrProject', label: '工作/实习/项目经历' },
  { value: 'skill', label: '技能' },
  { value: 'selfEvaluation', label: '自我评价' },
]

export const Full_RESUME_STRATEGY_OPTIONS = [
  { value: 'quantified', label: '量化项目/工作成果' },
  { value: 'qualitative', label: '润色措辞' },
  // { value: 'diagnosis', label: '简历诊断' }, 暂不支持
  { value: 'translate', label: '简历翻译' },
]

export const PLACEHOLDERS = {
  jobInfo: `例如：
1. 工作职责：
- 负责产品需求分析和功能设计
- 参与产品规划和技术方案设计
...
2. 任职要求：
- 具备良好的沟通能力和团队协作精神
- 具备较强的学习能力和解决问题的能力
- 具备较强的责任心和执行力
...`,
}

// 顶层Store管理全局状态
export const useResumeStore = defineStore('resume', () => {
  const activeTab = ref<Tab>(Tab.OPTIMIZE)
  const switchTab = (tab: Tab) => {
    activeTab.value = tab
  }
  return {
    activeTab,
    switchTab,
  }
})

export const useOptimizationStore = defineStore('optimization', () => {
  const status = ref<Status>(Status.PENDING) // 优化内容的状态 【模块优化，整体优化通用】
  const uploadFileStatus = ref<Status>(Status.PENDING) // 上传文件状态 【仅整体优化】
  const resumeInput = reactive({
    content: '', // 模块文本内容
    file: null as File | null, // 上传的PDF文件
    fileToMarkdownContent: '', // 上传的 PDF整体优化内容
  })
  const attachment = reactive<{
    jobInfo: string
    targetLanguage: Language
    module: Module
    fullResumeStrategy: FullResumeStrategy
    jobType: JobType
    enhancementMode: EnhancementMode
  }>({
    jobInfo: '', // 岗位信息
    targetLanguage: Language.ZH_CN, // 目标语言
    module: Module.DEFAULT, // 选择优化的模块
    fullResumeStrategy: FullResumeStrategy.QUANTIFIED, // 整体优化的服务
    jobType: JobType.GENERAL, // 职位类型
    enhancementMode: EnhancementMode.STANDARD, // 优化模式
  })

  const output = reactive({
    content: '', // 输出的优化内容
  })

  const uiState = reactive<{
    inputType: InputType
    showJdInput: boolean
  }>({
    inputType: InputType.TEXT, // 输入类型：text或pdf
    showJdInput: false, // 是否显示JD输入
  })

  const updateResumeInputData = (options: Partial<typeof resumeInput>) => {
    Object.assign(resumeInput, options)
  }

  const updateAttachment = <K extends keyof typeof attachment>(key: K, value: (typeof attachment)[K]) => {
    attachment[key] = value
  }

  const switchInputType = (type: InputType) => {
    uiState.inputType = type
  }

  const toggleJdInput = () => {
    uiState.showJdInput = !uiState.showJdInput
  }

  const isFormValid = computed(() => {
    if (uiState.inputType === InputType.TEXT && !resumeInput.content) {
      return false
    }
    if (uiState.inputType === InputType.FILE && !resumeInput.file) {
      return false
    }
    if (!attachment.jobType) {
      return false
    }
    return true
  })

  // 处理文件上传
  const handleFileUpload = (file: File | null) => {
    if (file && file.type === 'application/pdf') {
      resumeInput.file = file
    }
  }

  return {
    status,
    uploadFileStatus,
    resumeInput: resumeInput,
    attachment: attachment,
    output: output,
    uiState,

    // 更新数据
    updateResumeInputData,
    updateAttachment,
    switchInputType,
    toggleJdInput,
    handleFileUpload,
    isFormValid,
  }
})

export const useIAMInterviewerStore = defineStore('IAMInterviewer', () => {
  const status = ref<Status>(Status.PENDING) // 优化内容的状态 【模块优化，整体优化通用】
  const uploadFileStatus = ref<Status>(Status.PENDING)

  const resumeInput = reactive({
    content: '', // 见文本内容
    file: null as File | null, // 上传的PDF文件
    fileToMarkdownContent: '', // 上传的 PDF整体优化内容
  })

  const output = reactive({
    content: '', // 输出的面试问题
  })

  return {
    status,
    uploadFileStatus,
    resumeInput,
    output,
  }
})

export const useResumeEditStore = defineStore('resumeEdit', () => {
  const status = ref<Status>(Status.PENDING) // 优化内容的状态 【模块优化，整体优化通用】
  const resumeInput = reactive({
    content: '', // 模块文本内容
  })
  const attachment = reactive<{
    jobInfo: string
    targetLanguage: Language
    module: Module
    fullResumeStrategy: FullResumeStrategy
    jobType: JobType
    enhancementMode: EnhancementMode
  }>({
    jobInfo: '', // 岗位信息
    targetLanguage: Language.ZH_CN, // 目标语言
    module: Module.WORK_OR_PROJECT, // 选择优化的模块
    fullResumeStrategy: FullResumeStrategy.QUANTIFIED, // 整体优化的服务
    jobType: JobType.GENERAL, // 职位类型
    enhancementMode: EnhancementMode.STANDARD, // 优化模式
  })

  const output = reactive({
    content: '', // 输出的优化内容
  })

  const uiState = reactive<{
    inputType: InputType
    showJdInput: boolean
  }>({
    inputType: InputType.TEXT, // 输入类型：text或pdf
    showJdInput: false, // 是否显示JD输入
  })

  const updateResumeInputData = (options: Partial<typeof resumeInput>) => {
    Object.assign(resumeInput, options)
  }

  const updateAttachment = <K extends keyof typeof attachment>(key: K, value: (typeof attachment)[K]) => {
    attachment[key] = value
  }

  const switchInputType = (type: InputType) => {
    uiState.inputType = type
  }

  const toggleJdInput = () => {
    uiState.showJdInput = !uiState.showJdInput
  }

  const isFormValid = computed(() => {
    if (uiState.inputType === InputType.TEXT && !resumeInput.content) {
      return false
    }
    if (!attachment.jobType) {
      return false
    }
    return true
  })

  return {
    status,
    resumeInput: resumeInput,
    attachment: attachment,
    output: output,
    uiState,

    // 更新数据
    updateResumeInputData,
    updateAttachment,
    switchInputType,
    toggleJdInput,
    isFormValid,
  }
})
