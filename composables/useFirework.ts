import confetti from 'canvas-confetti'

export const useFireworks = () => {
  const launchFireworks = () => {
    // base config
    const defaults = {
      spread: 360,
      ticks: 50,
      gravity: 0,
      decay: 0.94,
      startVelocity: 30,
      colors: ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff'],
    }

    // create a fireworks explosion effect
    const createFirework = (particleCount = 50) => {
      // random position
      const x = Math.random()
      const y = Math.random() * 0.5 + 0.3

      // launch fireworks
      confetti({
        ...defaults,
        particleCount,
        origin: { x, y },
        scalar: 1.2,
      })

      // create sparks after the explosion
      setTimeout(() => {
        confetti({
          ...defaults,
          particleCount: particleCount * 2,
          origin: { x, y },
          scalar: 0.75,
          shapes: ['circle'],
        })
      }, 100)
    }

    // launch multiple fireworks
    const duration = 5000
    const end = Date.now() + duration
    const interval = setInterval(() => {
      if (Date.now() > end && interval) {
        clearInterval(interval)
        return
      }
      createFirework(Math.floor(Math.random() * 30 + 20))
    }, 200) as any
  }

  return {
    launchFireworks,
  }
}

export default useFireworks
