import { ref } from 'vue'
import { useStreamProcessing, type StreamProcessorOptions } from './useStreamProcessing'

/**
 * 处理结果的通用类型定义
 * @template TContent 内容的具体类型 (例如 string, string[])
 */
export type ProcessedResult<TContent = string> = {
  content: TContent
}

/**
 * useStreamedContentProcessor 的选项
 * @template TRequestBody API 请求体的类型
 * @template TContent 内容的具体类型
 */
export type StreamedContentProcessorOptions<TRequestBody extends Record<string, any>, TContent = string> = {
  streamOptions: Omit<StreamProcessorOptions, 'processedContentRef' | 'isStreamProcessingRef' | 'body'> & { body: TRequestBody }
  // 可选的函数，用于转换从流中累积的文本
  transformAccumulatedText?: (text: string) => TContent
  // 获取错误时应返回的空内容
  getEmptyContentOnError: () => TContent
  // 用于错误日志和用户错误消息的上下文信息
  errorContextMessage: string
}

export const useStreamedContentProcessor = <TRequestBody extends Record<string, any>, TContent = string>() => {
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const { processTextStream } = useStreamProcessing()

  // processedContent 存储从流中获取的原始累积文本
  const processedContent = ref('')
  // isStreamProcessing 指示流是否正在处理中
  const isStreamProcessing = ref(false)

  /**
   * 处理流式 API 调用、关键词提取和状态管理的核心方法
   */
  const process = async (options: StreamedContentProcessorOptions<TRequestBody, TContent>): Promise<ProcessedResult<TContent>> => {
    isLoading.value = true
    error.value = null
    let accumulatedText = ''

    try {
      accumulatedText = await processTextStream({
        url: options.streamOptions.url,
        method: options.streamOptions.method,
        body: options.streamOptions.body,
        processedContentRef: processedContent,
        isStreamProcessingRef: isStreamProcessing,
      })

      if (!accumulatedText || accumulatedText.trim() === '') {
        throw new Error('从流获取的文本为空或仅包含空白字符。')
      }

      // 转换累积文本
      // 如果 TContent 就是 string 类型，并且没有提供 transformAccumulatedText，则直接使用 accumulatedText
      const finalContent: TContent = options.transformAccumulatedText
        ? options.transformAccumulatedText(accumulatedText)
        : (accumulatedText as unknown as TContent)

      return { content: finalContent }
    } catch (err: any) {
      console.error(`${options.errorContextMessage} 失败:`, err)
      error.value = err.data?.message || err.message || `${options.errorContextMessage}，请重试`
      const emptyContent = options.getEmptyContentOnError()
      return { content: emptyContent }
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    error,
    process,
    processedContent,
    isStreamProcessing,
  }
}
