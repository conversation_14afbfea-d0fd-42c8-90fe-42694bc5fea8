import { formatTime } from '@/utils/timeUtils'
import { nextTick, ref } from 'vue'

export interface MessageRole {
  interviewer: 'interviewer'
  user: 'user'
  assistant: 'assistant'
}

export type MessageRoleType = keyof MessageRole

export interface MessageType {
  text: 'text'
  voice: 'voice'
}

export interface HistoryMessage {
  role: keyof MessageRole
  content: string
  time: string
  type: keyof MessageType
  audioDuration?: number
  isStreaming?: boolean
  id?: string | number
}

export const useMessageHandler = (messageContainerRef: Ref<HTMLElement | null>) => {
  const messages = ref<HistoryMessage[]>([])
  const userInput = ref('')

  /**
   * 滚动消息容器到底部
   */
  const scrollToBottom = () => {
    nextTick(() => {
      if (messageContainerRef.value) {
        messageContainerRef.value.scrollTop = messageContainerRef.value.scrollHeight
      }
    })
  }

  /**
   * 添加新消息
   * @param role 消息角色
   * @param content 消息内容
   * @param type 消息类型
   * @param audioDuration 语音消息时长（可选）
   */
  const addMessage = (role: keyof MessageRole, content: string, type: keyof MessageType = 'text', audioDuration?: number) => {
    messages.value.push({
      role,
      content,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      type,
      ...(audioDuration ? { audioDuration } : {}),
    })
    scrollToBottom()
  }

  /**
   * 发送用户文本消息
   */
  const sendTextMessage = () => {
    const text = userInput.value.trim()
    if (!text) return

    addMessage('user', text, 'text')
    userInput.value = ''
  }

  /**
   * 清空用户输入
   */
  const clearText = () => {
    userInput.value = ''
  }

  return {
    messages,
    userInput,
    addMessage,
    sendTextMessage,
    clearText,
    scrollToBottom,
  }
}
