import { createCrypto } from '~/utils/crypto'

interface Credentials {
  email: string
  password: string
  expiresAt: number
}

export const useCredentials = () => {
  const config = useRuntimeConfig()
  let crypto: ReturnType<typeof createCrypto> | null = null

  const fallbackKey = process.env.NODE_ENV === 'production' ? 'FZqGMTLJ7xcDpzvxXVQ6ICbmCPTN4SNz43qL7RNtJ6s=' : ''

  try {
    crypto = createCrypto(config.public.cryptoKey || fallbackKey)
  } catch (error) {
    console.error('Failed to initialize crypto:', error)
    console.log(config.public.cryptoKey)
  }

  const STORAGE_KEY = 'secure_credentials'
  const EXPIRATION_DAYS = 7

  const saveCredentials = (email: string, password: string) => {
    if (!crypto) return

    try {
      const credentials: Credentials = {
        email,
        password,
        expiresAt: Date.now() + EXPIRATION_DAYS * 24 * 60 * 60 * 1000,
      }

      const encryptedData = crypto.encrypt(JSON.stringify(credentials))

      const cookie = useCookie(STORAGE_KEY, {
        maxAge: EXPIRATION_DAYS * 24 * 60 * 60,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
      })

      cookie.value = encryptedData
    } catch (error) {
      console.error('Failed to save credentials:', error)
      removeCredentials()
    }
  }

  const getCredentials = (): Credentials | null => {
    if (!crypto) return null

    const cookie = useCookie(STORAGE_KEY)

    if (!cookie.value) return null

    try {
      const decryptedData = crypto.decrypt(cookie.value)
      const credentials: Credentials = JSON.parse(decryptedData)

      if (Date.now() > credentials.expiresAt) {
        removeCredentials()
        return null
      }

      return credentials
    } catch (error) {
      console.error('Failed to get credentials:', error)
      removeCredentials()
      return null
    }
  }

  const removeCredentials = () => {
    const cookie = useCookie(STORAGE_KEY)
    cookie.value = null
  }

  return {
    saveCredentials,
    getCredentials,
    removeCredentials,
  }
}
