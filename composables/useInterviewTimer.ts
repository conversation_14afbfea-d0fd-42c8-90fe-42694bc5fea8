import { ref, computed } from 'vue'
import { useTimestamp, useIntervalFn, pausableWatch } from '@vueuse/core'

/**
 * 面试计时器组合式函数
 * 提供计时功能，格式化为 HH:MM:SS 格式
 */
export const useInterviewTimer = () => {
  // 计时器开始时间
  const startTime = ref<number | null>(null)
  // 计时器是否正在运行
  const isRunning = ref(false)
  // 当前时间戳
  const { timestamp, pause: pauseTimestamp, resume: resumeTimestamp } = useTimestamp({ controls: true })
  // 累计时间（毫秒）
  const elapsedTime = ref(0)

  // 格式化时间为 HH:MM:SS
  const formattedTime = computed(() => {
    const totalSeconds = Math.floor(elapsedTime.value / 1000)
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60
    
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      seconds.toString().padStart(2, '0')
    ].join(':')
  })

  // 更新计时器
  const updateTimer = () => {
    if (isRunning.value && startTime.value) {
      elapsedTime.value = timestamp.value - startTime.value
    }
  }

  // 设置计时器更新间隔
  const { pause: pauseInterval, resume: resumeInterval } = useIntervalFn(updateTimer, 1000, { immediate: false })

  // 开始计时
  const start = () => {
    if (!isRunning.value) {
      isRunning.value = true
      startTime.value = timestamp.value - elapsedTime.value
      resumeTimestamp()
      resumeInterval()
    }
  }

  // 暂停计时
  const pause = () => {
    if (isRunning.value) {
      isRunning.value = false
      pauseInterval()
      pauseTimestamp()
    }
  }

  // 重置计时器
  const reset = () => {
    pause()
    elapsedTime.value = 0
    startTime.value = null
  }

  // 重新开始计时
  const restart = () => {
    reset()
    start()
  }

  return {
    formattedTime,
    isRunning,
    elapsedTime,
    start,
    pause,
    reset,
    restart
  }
}
