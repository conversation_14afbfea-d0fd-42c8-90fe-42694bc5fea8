import type { ComprehensiveEvaluation } from '~/server/prompt-service/interview-evaluation'
import type { InterviewType } from '~/types/interview'

interface EvaluationResponse {
  data: {
    evaluation: ComprehensiveEvaluation
    message: string
  }
}

interface MessageForApi {
  role: 'user' | 'assistant' | 'interviewer'
  content: string
}

export const useInterviewEvaluation = () => {
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const generateEvaluation = async (
    messages: MessageForApi[],
    type: InterviewType,
    resumeText?: string,
    jobInfo?: string,
  ): Promise<ComprehensiveEvaluation> => {
    try {
      isLoading.value = true
      error.value = null

      const response = await $fetch<EvaluationResponse>('/api/interview/evaluation', {
        method: 'POST',
        body: {
          messages,
          type,
          resumeText: resumeText || '',
          jobInfo: jobInfo || '',
        },
      })

      return response.data.evaluation
    } catch (err: any) {
      error.value = err.message || '生成评价失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    error,
    generateEvaluation,
  }
}
