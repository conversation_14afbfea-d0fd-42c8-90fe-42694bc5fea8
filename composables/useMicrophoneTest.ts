export const useMicrophoneTest = (threshold = 30, testDuration = 5000) => {
  const isRecording = ref(false)
  const testCompleted = ref(false)
  const audioLevel = ref(0)
  const maxAudioLevel = ref(0)
  const testSuccess = ref(false)
  const stream = ref<MediaStream | null>(null)
  const audioContext = ref<AudioContext | null>(null)
  const analyser = ref<AnalyserNode | null>(null)
  const dataArray = ref<Uint8Array | null>(null)
  const animationFrame = ref<number | null>(null)

  const startRecording = async (deviceId?: string) => {
    try {
      isRecording.value = true
      maxAudioLevel.value = 0
      testCompleted.value = false

      stream.value = await navigator.mediaDevices.getUserMedia({
        audio: { deviceId: deviceId && deviceId !== 'default' ? deviceId : undefined },
        video: false,
      })

      audioContext.value = new AudioContext()
      const source = audioContext.value.createMediaStreamSource(stream.value)
      analyser.value = audioContext.value.createAnalyser()
      analyser.value.fftSize = 256
      source.connect(analyser.value)

      const bufferLength = analyser.value.frequencyBinCount
      dataArray.value = new Uint8Array(bufferLength)

      updateAudioLevel()

      setTimeout(() => {
        completeTest()
      }, testDuration)
    } catch (err) {
      console.error('开始录音失败:', err)
      isRecording.value = false
      completeTest(false)
    }
  }

  const updateAudioLevel = () => {
    if (!analyser.value || !dataArray.value) return

    analyser.value.getByteFrequencyData(dataArray.value)

    let sum = 0
    for (let i = 0; i < dataArray.value.length; i++) {
      sum += dataArray.value[i]
    }
    const average = sum / dataArray.value.length

    audioLevel.value = Math.round((average / 255) * 100)

    if (audioLevel.value > maxAudioLevel.value) {
      maxAudioLevel.value = audioLevel.value
    }

    if (isRecording.value) {
      animationFrame.value = requestAnimationFrame(updateAudioLevel)
    }
  }

  const completeTest = (forceSuccess: boolean | null = null) => {
    if (testCompleted.value) return

    testCompleted.value = true
    isRecording.value = false

    if (forceSuccess !== null) {
      testSuccess.value = forceSuccess
    } else {
      testSuccess.value = maxAudioLevel.value >= threshold
    }

    if (animationFrame.value) {
      cancelAnimationFrame(animationFrame.value)
      animationFrame.value = null
    }

    if (audioContext.value) {
      audioContext.value.close()
      audioContext.value = null
    }

    if (stream.value) {
      stream.value.getTracks().forEach(track => track.stop())
      stream.value = null
    }
  }

  const resetTest = () => {
    testCompleted.value = false
    audioLevel.value = 0
    maxAudioLevel.value = 0
    testSuccess.value = false
  }

  onBeforeUnmount(() => {
    if (animationFrame.value) {
      cancelAnimationFrame(animationFrame.value)
    }

    if (audioContext.value) {
      audioContext.value.close()
    }

    if (stream.value) {
      stream.value.getTracks().forEach(track => track.stop())
    }
  })

  return {
    isRecording,
    testCompleted,
    audioLevel,
    maxAudioLevel,
    testSuccess,
    startRecording,
    completeTest,
    resetTest,
  }
}
