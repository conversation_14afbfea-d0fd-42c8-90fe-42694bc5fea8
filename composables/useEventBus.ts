import { EventEmitter } from 'events'
import { getCurrentInstance, onBeforeUnmount } from 'vue'

export const eventBus = new EventEmitter()

export const useEventBus = () => {
  return eventBus
}

export const useEvent = <Key extends keyof GlobalEvent>(eventName: Key) => {
  const callbacks: ((...args: EventParams<GlobalEvent[Key]>) => void)[] = []
  const instance = getCurrentInstance()
  if (instance) {
    onBeforeUnmount(() => {
      callbacks.forEach(cb => eventBus.off(eventName, cb as any))
    })
  }
  return {
    on: (callback: (...args: EventParams<GlobalEvent[Key]>) => void) => {
      if (!import.meta.client) {
        return () => {}
      }
      eventBus.on(eventName, callback as any)
      callbacks.push(callback as any)
      return () => eventBus.off(eventName, callback as any)
    },
    once: (callback: (...args: EventParams<GlobalEvent[Key]>) => void) => {
      if (!import.meta.client) {
        return () => {}
      }
      eventBus.once(eventName, callback as any)
      callbacks.push(callback)
      return () => eventBus.off(eventName, callback as any)
    },
    off: (callback: (...args: EventParams<GlobalEvent[Key]>) => void) => {
      eventBus.off(eventName, callback as any)
    },
    emit: (...args: EventParams<GlobalEvent[Key]>) => {
      eventBus.emit(eventName, ...args)
    },
  }
}
type EventParams<T> = T extends (...args: infer P) => void ? P : T extends any[] ? T : never

export type GlobalEvent = {
  showToast: (payload: { message: string; type?: 'info' | 'warning' | 'success' | 'error'; duration?: number }) => void
  showPaywall: () => void
  showCreditsNotification: () => void
}
