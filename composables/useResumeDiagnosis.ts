import { ref } from 'vue'
import { useStreamedContentProcessor } from './useStreamedContentProcessor'

type DiagnosisFormData = {
  resumeText: string
  jobDescription: string
  model: 'standard' | 'professional'
}

type DiagnosisResult = {
  analyzer_sop: any
  diagnosis_sop: any
  suggestions_sop: any
}

export const useResumeDiagnosis = () => {
  const processor = useStreamedContentProcessor<DiagnosisFormData & { stageOneResult: DiagnosisResult }, string>()
  
  // 阶段一：诊断分析
  const getDiagnosisResult = async (formData: DiagnosisFormData): Promise<DiagnosisResult> => {
    try {
      const response = await $fetch('/api/resume/agent-stage-one', {
        method: 'POST',
        body: formData
      }) as any

      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || '诊断失败')
      }
    } catch (error: any) {
      throw new Error(error.message || '诊断过程中发生错误')
    }
  }

  // 阶段二：生成优化简历（流式输出）
  const generateOptimizedResume = async (formData: DiagnosisFormData, stageOneResult: DiagnosisResult) => {
    return processor.process({
      streamOptions: {
        url: '/api/resume/agent-stage-two',
        body: {
          ...formData,
          stageOneResult
        },
      },
      transformAccumulatedText: (text: string): string => {
        // 清理可能的markdown格式包装
        return text
          .trim()
          .replace(/^```markdown\s*/, '')
          .replace(/^```\s*/, '')
          .replace(/\s*```$/, '')
      },
      getEmptyContentOnError: () => '生成简历失败，请重试',
      errorContextMessage: '生成优化简历',
    })
  }

  return {
    // 流式输出相关状态
    isLoading: processor.isLoading,
    error: processor.error,
    processedContent: processor.processedContent,
    isStreamProcessing: processor.isStreamProcessing,
    
    // 业务方法
    getDiagnosisResult,
    generateOptimizedResume,
  }
} 