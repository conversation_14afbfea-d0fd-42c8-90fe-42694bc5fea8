import { ref } from 'vue'
import type { MessageRole, MessageType } from './useMessageHandler'

export interface Message {
  role: MessageRole
  type: MessageType
  content: string
  audioUrl?: string
  audioDuration?: number
  isTemp?: boolean
}

const messages = ref<Message[]>([])

export const useMessages = () => {
  const addMessage = (role: MessageRole, content: string, isTemp: boolean = false, type: MessageType = 'text', audioUrl?: string, audioDuration?: number) => {
    messages.value.push({
      role,
      type,
      content,
      audioUrl,
      audioDuration,
      isTemp,
    })
  }

  const addVoiceMessage = (role: MessageRole, content: string, audioUrl: string, audioDuration: number, isTemp: boolean = false) => {
    addMessage(role, content, isTemp, 'voice', audioUrl, audioDuration)
  }

  const removeTempMessages = () => {
    messages.value = messages.value.filter(message => !message.isTemp)
  }

  const clearMessages = () => {
    messages.value = []
  }

  const getContextMessages = () => {
    return messages.value
      .filter(msg => !msg.isTemp)
      .filter((msg, index, self) => index === self.findIndex(t => t.role === msg.role && t.content === msg.content))
  }

  return {
    messages,
    addMessage,
    addVoiceMessage,
    removeTempMessages,
    clearMessages,
    getContextMessages,
  }
}
