/**
 * Composable for generating canonical URLs
 * Handles both static and dynamic routes with proper SEO formatting
 */
export const useCanonicalUrl = () => {
  const route = useRoute()
  const runtimeConfig = useRuntimeConfig()

  /**
   * Get the base site URL from runtime config
   */
  const getSiteUrl = (): string => {
    return runtimeConfig.public.siteUrl || 'https://hi-offer.com'
  }

  /**
   * Clean and normalize a URL path
   * Removes trailing slashes and ensures proper formatting
   */
  const cleanPath = (path: string): string => {
    // Remove trailing slash except for root path
    if (path !== '/' && path.endsWith('/')) {
      path = path.slice(0, -1)
    }

    // Ensure path starts with /
    if (!path.startsWith('/')) {
      path = '/' + path
    }

    return path
  }

  /**
   * Generate canonical URL for the current route
   * @param customPath - Optional custom path to override current route
   * @returns Complete canonical URL
   */
  const getCanonicalUrl = (customPath?: string): string => {
    const siteUrl = getSiteUrl()
    const path = customPath || route.path
    const cleanedPath = cleanPath(path)

    return `${siteUrl}${cleanedPath}`
  }

  /**
   * Generate canonical URL for a specific path
   * @param path - The path to generate canonical URL for
   * @returns Complete canonical URL
   */
  const getCanonicalUrlForPath = (path: string): string => {
    const siteUrl = getSiteUrl()
    const cleanedPath = cleanPath(path)

    return `${siteUrl}${cleanedPath}`
  }

  /**
   * Generate canonical URL for dynamic routes with parameters
   * @param basePath - The base path (e.g., '/user')
   * @param params - Route parameters object
   * @returns Complete canonical URL
   */
  const getCanonicalUrlForDynamicRoute = (basePath: string, params: Record<string, string | number>): string => {
    let path = basePath

    // Replace route parameters in the path
    Object.entries(params).forEach(([key, value]) => {
      path = path.replace(`[${key}]`, String(value))
      path = path.replace(`:${key}`, String(value))
    })

    return getCanonicalUrlForPath(path)
  }

  /**
   * Set canonical URL meta tag for the current page
   * @param customPath - Optional custom path to override current route
   */
  const setCanonicalUrl = (customPath?: string): void => {
    const canonicalUrl = getCanonicalUrl(customPath)

    useHead({
      link: [
        {
          rel: 'canonical',
          href: canonicalUrl,
        },
      ],
    })
  }

  /**
   * Set canonical URL meta tag for a specific path
   * @param path - The path to set canonical URL for
   */
  const setCanonicalUrlForPath = (path: string): void => {
    const canonicalUrl = getCanonicalUrlForPath(path)

    useHead({
      link: [
        {
          rel: 'canonical',
          href: canonicalUrl,
        },
      ],
    })
  }

  /**
   * Set canonical URL meta tag for dynamic routes
   * @param basePath - The base path (e.g., '/user')
   * @param params - Route parameters object
   */
  const setCanonicalUrlForDynamicRoute = (basePath: string, params: Record<string, string | number>): void => {
    const canonicalUrl = getCanonicalUrlForDynamicRoute(basePath, params)

    useHead({
      link: [
        {
          rel: 'canonical',
          href: canonicalUrl,
        },
      ],
    })
  }

  /**
   * Auto-set canonical URL for the current route
   * This is the most common use case - just call this in your page setup
   */
  const useAutoCanonicalUrl = (): void => {
    setCanonicalUrl()
  }

  return {
    getSiteUrl,
    getCanonicalUrl,
    getCanonicalUrlForPath,
    getCanonicalUrlForDynamicRoute,
    setCanonicalUrl,
    setCanonicalUrlForPath,
    setCanonicalUrlForDynamicRoute,
    useAutoCanonicalUrl,
    cleanPath,
  }
}
