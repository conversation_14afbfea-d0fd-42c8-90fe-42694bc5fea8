import { ref } from 'vue'

export const useAudioAnalyzer = () => {
  const audioData = ref<Float32Array | undefined>(undefined)
  let audioContext: AudioContext | null = null
  let analyser: AnalyserNode | null = null
  let dataArray: Float32Array | null = null
  let animationFrameId: number | null = null

  const startAnalyzing = (stream: MediaStream) => {
    // 设置音频分析
    audioContext = new AudioContext()
    const source = audioContext.createMediaStreamSource(stream)
    analyser = audioContext.createAnalyser()
    analyser.fftSize = 256
    source.connect(analyser)

    dataArray = new Float32Array(analyser.frequencyBinCount)

    const analyze = () => {
      if (!analyser || !dataArray) return

      analyser.getFloatTimeDomainData(dataArray)
      audioData.value = dataArray

      animationFrameId = requestAnimationFrame(analyze)
    }

    analyze()
  }

  const stopAnalyzing = () => {
    if (audioContext) {
      audioContext.close()
      audioContext = null
    }
    if (animationFrameId !== null) {
      cancelAnimationFrame(animationFrameId)
      animationFrameId = null
    }
    audioData.value = undefined
  }

  return {
    audioData,
    startAnalyzing,
    stopAnalyzing,
  }
}
