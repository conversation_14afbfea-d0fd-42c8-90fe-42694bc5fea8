/**
 * 生成 hreflang 属性的组合式函数，用于国际 SEO
 * 集成现有的规范 URL 系统，并遵循 SEO 最佳实践
 */

export type HreflangConfig = {
  /** 语言代码（例如：'zh-CN', 'en-US'） */
  lang: string
  /** 区域代码（例如：'CN', 'US'） - 可选 */
  region?: string
  /** 此语言版本的域名 - 默认为主站点 */
  domain?: string
  /** 此语言的路径前缀（例如：'/en', '/zh-tw'） - 可选 */
  pathPrefix?: string
}

export type HreflangLink = {
  /** 语言和区域代码（例如：'zh-CN', 'en-US'） */
  hreflang: string
  /** 此语言版本的绝对 URL */
  href: string
}

export const useHreflang = () => {
  const route = useRoute()
  const { getSiteUrl, cleanPath } = useCanonicalUrl()

  /**
   * 支持的语言配置
   * 基于现有代码库中的语言支持
   */
  const supportedLanguages: HreflangConfig[] = [
    {
      lang: 'zh-CN',
      region: 'CN',
    },
  ]

  /**
   * 获取当前语言或默认语言
   */
  const getCurrentLanguage = (): string => {
    const path = route.path

    return 'zh-CN' // 默认简体中文
  }

  /**
   * 获取不带语言前缀的基础路径
   */
  const getBasePath = (path?: string): string => {
    const currentPath = path || route.path

    return currentPath
  }

  /**
   * 生成特定语言版本的 URL
   */
  const getLanguageUrl = (langConfig: HreflangConfig, basePath: string): string => {
    const siteUrl = langConfig.domain || getSiteUrl()
    const pathPrefix = langConfig.pathPrefix || ''
    const fullPath = pathPrefix + basePath
    const cleanedPath = cleanPath(fullPath)

    return `${siteUrl}${cleanedPath}`
  }

  /**
   * 生成当前页面的所有 hreflang 链接
   */
  const generateHreflangLinks = (customPath?: string): HreflangLink[] => {
    const basePath = getBasePath(customPath)
    const links: HreflangLink[] = []

    supportedLanguages.forEach(langConfig => {
      const hreflang = langConfig.region ? `${langConfig.lang.toLowerCase()}` : langConfig.lang.toLowerCase()
      const href = getLanguageUrl(langConfig, basePath)

      links.push({ hreflang, href })
    })

    const defaultUrl = getLanguageUrl(supportedLanguages[0], basePath)
    links.push({ hreflang: 'x-default', href: defaultUrl })

    return links
  }

  /**
   * 为当前页面设置 hreflang 元标签
   * 集成 Nuxt 的 useHead()，遵循现有的 SEO 模式
   */
  const setHreflangTags = (customPath?: string): void => {
    const hreflangLinks = generateHreflangLinks(customPath)

    const linkTags = hreflangLinks.map(link => ({
      rel: 'alternate',
      hreflang: link.hreflang,
      href: link.href,
    }))

    useHead({ link: linkTags })
  }

  /**
   * 为特定路径设置 hreflang 标签
   */
  const setHreflangForPath = (path: string): void => {
    setHreflangTags(path)
  }

  /**
   * 为动态路由设置 hreflang 标签
   */
  const setHreflangForDynamicRoute = (basePath: string, params: Record<string, string | number>): void => {
    let fullPath = basePath
    Object.entries(params).forEach(([key, value]) => {
      fullPath = fullPath.replace(`[${key}]`, String(value))
    })

    setHreflangTags(fullPath)
  }

  /**
   * 自动为当前路由设置 hreflang 标签
   * 这是最常见的用例 - 只需在页面设置中调用此函数
   */
  const useAutoHreflang = (): void => {
    setHreflangTags()
  }

  /**
   * 获取特定语言的导航 URL
   */
  const getLanguageSpecificUrl = (targetLang: string, path?: string): string => {
    const basePath = getBasePath(path)
    const langConfig = supportedLanguages.find(config => config.lang === targetLang)

    if (!langConfig) {
      console.warn(`语言 ${targetLang} 不受支持`)
      return getSiteUrl() + basePath
    }

    return getLanguageUrl(langConfig, basePath)
  }

  /**
   * 检查语言是否受支持
   */
  const isLanguageSupported = (lang: string): boolean => {
    return supportedLanguages.some(config => config.lang === lang)
  }

  /**
   * 获取所有受支持的语言代码
   */
  const getSupportedLanguages = (): string[] => {
    return supportedLanguages.map(config => config.lang)
  }

  return {
    // 配置
    supportedLanguages,

    // 当前状态
    getCurrentLanguage,
    getBasePath,

    // URL 生成
    getLanguageUrl,
    getLanguageSpecificUrl,
    generateHreflangLinks,

    // 元标签管理
    setHreflangTags,
    setHreflangForPath,
    setHreflangForDynamicRoute,
    useAutoHreflang,

    // 工具函数
    isLanguageSupported,
    getSupportedLanguages,
  }
}
