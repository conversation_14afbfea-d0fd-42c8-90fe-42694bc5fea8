import { ref } from 'vue'
import type { InterviewStage, InterviewType } from '~/types/interview'
import { useMessages } from './useMessages'

interface AIResponse {
  choices: {
    finish_reason: string
    index: number
    logprobs: null
    message: {
      content: string
      role: string
    }
  }[]
  created: number
  id: string
  model: string
  object: string
  usage: {
    completion_tokens: number
    prompt_tokens: number
    total_tokens: number
    prompt_tokens_details: {
      cached_tokens: number
    }
  }
}

// 添加API响应接口
interface ChatResponse {
  data: {
    message: string
    content: string
    usage: {
      completion_tokens: number
      prompt_tokens: number
      total_tokens: number
      prompt_tokens_details: {
        cached_tokens: number
      }
    }
  }
}

export const useInterviewChat = () => {
  const isWaitingResponse = ref(false)
  const { getContextMessages } = useMessages()
  const sendMessage = async (content: string, interviewerName: string, type: InterviewType, stage: InterviewStage, onChunk?: (chunk: string) => void) => {
    try {
      isWaitingResponse.value = true
      const contextMessages = getContextMessages()
      const messagesArray = [...contextMessages].filter(msg => msg.content !== '')

      const response = (await $fetch('/api/interview/chat', {
        method: 'POST',
        body: {
          messages: messagesArray,
          type,
          stage,
          interviewerName,
        },
        responseType: 'stream',
      })) as ReadableStream

      const reader = response.getReader()
      const decoder = new TextDecoder()
      let result = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        result += chunk

        if (onChunk) {
          onChunk(chunk)
        }
      }

      return result
    } catch (error) {
      console.error('AI回答错误:', error)
      throw error
    } finally {
      isWaitingResponse.value = false
    }
  }

  return {
    sendMessage,
    isWaitingResponse,
  }
}
