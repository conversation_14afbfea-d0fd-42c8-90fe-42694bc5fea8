import { usePrepareInterviewStore } from '~/stores/prepare-interview'
import type { InterviewType } from '~/types/interview'
import { useStreamedContentProcessor, type ProcessedResult } from './useStreamedContentProcessor'

type ApiMessage = {
  role: 'user' | 'assistant' | 'interviewer'
  content: string
}

export type AnalysisData = ProcessedResult<string>

type AnalysisBody = {
  scenario: 'analysis'
  messages: ApiMessage[]
  interviewType: InterviewType
  resumeText: string
  jobInfo: string
}

export const useInterviewAnalysis = () => {
  const processor = useStreamedContentProcessor<AnalysisBody, string>()

  const prepareInterviewStore = usePrepareInterviewStore()
  const { resumeContent: resumeText, positionDescription, companyDescription } = prepareInterviewStore.formData
  const jobInfo = `${positionDescription} ${companyDescription}`

  const getAnalysis = async (messages: ApiMessage[], interviewType: InterviewType): Promise<AnalysisData> => {
    return processor.process({
      streamOptions: {
        url: '/api/interview/generate',
        body: {
          scenario: 'analysis',
          messages,
          interviewType,
          resumeText,
          jobInfo,
        },
      },
      getEmptyContentOnError: () => '无法加载分析内容。',
      errorContextMessage: '获取分析内容',
    })
  }

  return {
    isLoading: processor.isLoading,
    error: processor.error,
    getAnalysis,
    processedContent: processor.processedContent,
    isStreamProcessing: processor.isStreamProcessing,
  }
}
