import { useInterviewQuestion, type InterviewQuestionData } from '@/composables/useInterviewQuestion'
import { stripMarkers } from '@/utils/textUtils'
const INTERVIEWER_AVATAR_DEFAULT = '/images/interviewer.png'

export type ChatMessage = {
  role: 'user' | 'assistant' | 'interviewer'
  content: string
  type: 'text' | 'voice'
  time: string
  audioDuration?: number
  isStreaming?: boolean
  id?: string | number
  [key: string]: any
}

const MIN_SPEECH_TEXT_SYNC_INTERVAL_MS = 10

export type UseInterviewerAgentWithMinimaxOptions = {
  /** 用于播放面试官音频的 HTMLAudioElement 引用。 */
  interviewerAudioPlayer: Ref<HTMLAudioElement | null>
  /** 聊天消息数组的引用。 */
  messages: Ref<ChatMessage[]>
  /** 当前正在流式传输的面试官消息的引用。 */
  streamingInterviewerMessageInChat: Ref<ChatMessage | null>
  /** 聊天消息历史记录容器的 HTML 元素引用，用于滚动控制。 */
  messageHistoryRef: Ref<HTMLElement | null>
  /** 面试官的初始名称。 */
  interviewerInitialName?: string
  /** 面试官的初始头像 URL。 */
  interviewerInitialAvatar?: string
  /** MiniMax TTS 配置选项 */
  ttsOptions?: {
    model?: string
    voiceId?: string
    speed?: number
    volume?: number
    pitch?: number
    emotion?: string
    format?: string
    sampleRate?: number
    bitrate?: number
  }
}

/**
 * 管理面试官代理的核心逻辑，使用 MiniMax TTS 进行语音合成
 * @param options - {@link UseInterviewerAgentWithMinimaxOptions}
 */
export function useInterviewerAgentWithMinimax(options: UseInterviewerAgentWithMinimaxOptions) {
  const {
    interviewerAudioPlayer,
    messages,
    streamingInterviewerMessageInChat,
    messageHistoryRef,
    interviewerInitialName = '面试官',
    interviewerInitialAvatar = INTERVIEWER_AVATAR_DEFAULT,
    ttsOptions = {},
  } = options

  const { error: errorQuestion, getInitialQuestion, getNextQuestion } = useInterviewQuestion()

  const isLoadingQuestionText = ref(false)
  const isSynthesizingAudio = ref(false)
  const interviewerAudioSrc = ref<string | null>(null)
  const revealedQuestionContent = ref('')
  const isStreamingText = ref(false)
  const lastFetchedAudioBlob = ref<Blob | null>(null)

  const isPlaybackBlockedByInteraction = ref(false)

  const interviewer = reactive<{
    name: string
    avatarUrl: string
    currentQuestion: { content: string }
  }>({
    name: interviewerInitialName,
    avatarUrl: interviewerInitialAvatar,
    currentQuestion: { content: '' },
  })

  /**
   * 使用 MiniMax TTS API 合成语音
   * @param text 要转换为语音的文本
   * @returns Promise<Blob | null> 音频 Blob 或 null（如果失败）
   */
  const fetchMinimaxTTSAudio = async (text: string): Promise<Blob | null> => {
    try {
      const requestBody = {
        text: stripMarkers(text),
        model: ttsOptions.model || 'speech-02-hd',
        voice_id: ttsOptions.voiceId || 'male-qn-jingying',
        speed: ttsOptions.speed || 1.0,
        volume: ttsOptions.volume || 1.0,
        pitch: ttsOptions.pitch || 0,
        emotion: ttsOptions.emotion || 'happy',
        format: ttsOptions.format || 'mp3',
        sample_rate: ttsOptions.sampleRate || 32000,
        bitrate: ttsOptions.bitrate || 128000,
        channel: 1,
        language_boost: 'Chinese',
        latex_read: false,
        english_normalization: false,
        subtitle_enable: false,
      }

      console.log('正在调用 MiniMax TTS API，文本长度:', text.length)

      const response = await $fetch('/api/tts/minimax', {
        method: 'POST',
        body: requestBody,
        responseType: 'blob',
      })

      if (response instanceof Blob) {
        console.log('MiniMax TTS 合成成功，音频大小:', response.size, '字节')
        return response
      } else {
        console.error('MiniMax TTS API 返回了非 Blob 响应')
        return null
      }
    } catch (error: any) {
      console.error('MiniMax TTS API 调用失败:', error)

      // 如果是认证错误或配额错误，提供更具体的错误信息
      if (error.status === 401) {
        console.error('MiniMax API 认证失败，请检查 API Key 配置')
      } else if (error.status === 429) {
        console.error('MiniMax API 调用频率超限，请稍后重试')
      } else if (error.status === 400) {
        console.error('MiniMax API 请求参数错误:', error.data)
      }

      return null
    }
  }

  /**
   * 播放提供的音频 Blob 并返回其时长（秒）。
   * @param audioBlob - 要播放的音频 Blob。
   * @returns Promise，解析为音频时长（秒）。
   */
  const playAudioAndGetDuration = (audioBlob: Blob): Promise<number> => {
    return new Promise((resolve, reject) => {
      if (interviewerAudioPlayer.value) {
        if (interviewerAudioSrc.value) {
          URL.revokeObjectURL(interviewerAudioSrc.value)
          interviewerAudioSrc.value = null
        }

        const objectURL = URL.createObjectURL(audioBlob)
        interviewerAudioSrc.value = objectURL
        const audioPlayerElement = interviewerAudioPlayer.value
        audioPlayerElement.src = objectURL

        const cleanupListeners = () => {
          audioPlayerElement.removeEventListener('loadedmetadata', onLoadedMetadataWithPlay)
          audioPlayerElement.removeEventListener('error', onErrorLoadingMetadata)
        }

        const onLoadedMetadataWithPlay = () => {
          const duration = audioPlayerElement.duration
          audioPlayerElement
            .play()
            .then(() => {
              isPlaybackBlockedByInteraction.value = false
              resolve(duration)
            })
            .catch(playError => {
              if (playError.name === 'NotAllowedError') {
                console.warn('音频自动播放失败：用户需要首先与文档交互。查看 https://goo.gl/xX8pDD')
                isPlaybackBlockedByInteraction.value = true
                reject(playError)
              } else {
                console.error('音频播放时发生错误:', playError)
                reject(playError)
              }
            })
            .finally(() => {
              cleanupListeners()
            })
        }

        const onErrorLoadingMetadata = (e: Event) => {
          console.error('加载音频元数据时出错:', e)
          reject(new Error('检查音频时长时加载音频元数据失败。'))
          cleanupListeners()
        }

        audioPlayerElement.addEventListener('loadedmetadata', onLoadedMetadataWithPlay)
        audioPlayerElement.addEventListener('error', onErrorLoadingMetadata)
        audioPlayerElement.load()
      } else {
        reject(new Error('未找到音频播放器元素。'))
      }
    })
  }

  let textStreamTimeoutId: number | undefined

  /**
   * 模拟文本流式显示效果，同步文本显示与音频播放。
   * 标记符号（如 **）会立即显示，其包裹的内容会逐字延迟显示。
   * @param fullText - 要流式显示的完整文本，可能包含标记。
   * @param audioDurationInSeconds - 音频的总时长（秒），对应于去除标记后的文本。
   * @returns Promise，在文本流完成时解析。
   */
  const startSimulatedTextStream = (fullText: string, audioDurationInSeconds: number): Promise<void> => {
    isStreamingText.value = true
    return new Promise(resolve => {
      clearTimeout(textStreamTimeoutId)
      revealedQuestionContent.value = ''

      const textForTTS = stripMarkers(fullText)
      const numCharsInTTS = textForTTS.length

      if (numCharsInTTS === 0 || audioDurationInSeconds <= 0 || audioDurationInSeconds < 0.5 || numCharsInTTS < 10) {
        revealedQuestionContent.value = fullText
        isStreamingText.value = false
        resolve()
        return
      }

      const avgTimePerSpokenChar = Math.max(MIN_SPEECH_TEXT_SYNC_INTERVAL_MS, (audioDurationInSeconds * 1000) / numCharsInTTS)
      let currentFullTextIndex = 0
      const marker = '**'

      const processNext = () => {
        if (currentFullTextIndex >= fullText.length) {
          if (revealedQuestionContent.value !== fullText) {
            revealedQuestionContent.value = fullText
          }
          isStreamingText.value = false
          resolve()
          return
        }

        let nextDelay: number

        if (fullText.startsWith(marker, currentFullTextIndex)) {
          revealedQuestionContent.value += marker
          currentFullTextIndex += marker.length
          nextDelay = MIN_SPEECH_TEXT_SYNC_INTERVAL_MS
        } else {
          revealedQuestionContent.value += fullText[currentFullTextIndex]
          currentFullTextIndex++
          nextDelay = avgTimePerSpokenChar
        }
        nextDelay = Math.max(MIN_SPEECH_TEXT_SYNC_INTERVAL_MS, nextDelay)

        if (currentFullTextIndex < fullText.length) {
          textStreamTimeoutId = setTimeout(processNext, nextDelay) as unknown as number
        } else {
          if (revealedQuestionContent.value !== fullText) {
            revealedQuestionContent.value = fullText
          }
          isStreamingText.value = false
          resolve()
        }
      }
      processNext()
    })
  }

  /**
   * 处理一个完整的问题获取、MiniMax TTS 和显示周期。
   * @param questionFetcher - 一个异步函数，用于获取面试问题数据。
   * @returns Promise，解析为获取到的问题原始内容。
   */
  const handleQuestionCycle = async (questionFetcher: () => Promise<InterviewQuestionData>): Promise<string> => {
    revealedQuestionContent.value = ''
    if (interviewerAudioSrc.value) {
      URL.revokeObjectURL(interviewerAudioSrc.value)
      interviewerAudioSrc.value = null
    }
    lastFetchedAudioBlob.value = null
    clearTimeout(textStreamTimeoutId)
    isStreamingText.value = false

    isLoadingQuestionText.value = true
    isSynthesizingAudio.value = false

    if (streamingInterviewerMessageInChat.value) {
      streamingInterviewerMessageInChat.value = null
    }

    let questionFullContent = ''
    let finalContentForChatMessage = ''

    try {
      const questionData = await questionFetcher()
      questionFullContent = questionData.content
      interviewer.currentQuestion.content = questionFullContent

      isLoadingQuestionText.value = false

      if (isPlaybackBlockedByInteraction.value) {
        console.warn('音频播放被用户交互阻止。跳过 MiniMax TTS 和音频播放，仅显示文本。')
        revealedQuestionContent.value = questionFullContent
        finalContentForChatMessage = stripMarkers(questionFullContent)
        isStreamingText.value = false
        isSynthesizingAudio.value = false
      } else {
        isSynthesizingAudio.value = true
        const audioBlob = await fetchMinimaxTTSAudio(questionFullContent)
        lastFetchedAudioBlob.value = audioBlob

        if (audioBlob && interviewerAudioPlayer.value) {
          isSynthesizingAudio.value = false
          try {
            const audioDuration = await playAudioAndGetDuration(audioBlob)
            if (!isPlaybackBlockedByInteraction.value) {
              await startSimulatedTextStream(questionFullContent, audioDuration)
            } else {
              console.warn('音频播放在 handleQuestionCycle 期间被用户交互阻止。文本流中止，显示完整文本。')
              revealedQuestionContent.value = questionFullContent
              isStreamingText.value = false
            }
            finalContentForChatMessage = stripMarkers(questionFullContent)
          } catch (playError: any) {
            console.warn(`音频播放处理失败: ${playError.message}。显示文本。`)
            revealedQuestionContent.value = questionFullContent
            finalContentForChatMessage = stripMarkers(questionFullContent)
            isStreamingText.value = false
            isSynthesizingAudio.value = false
          }
        } else {
          console.warn('MiniMax TTS 失败或音频播放器不可用。显示完整文本。')
          isSynthesizingAudio.value = false
          revealedQuestionContent.value = questionFullContent
          finalContentForChatMessage = stripMarkers(questionFullContent)
          isStreamingText.value = false
        }
      }
    } catch (err: any) {
      console.error('获取面试问题失败:', errorQuestion.value || err)
      const errorMessage = (errorQuestion.value as any)?.message || err?.message || '抱歉，处理面试官回复时遇到错误。'
      revealedQuestionContent.value = errorMessage
      finalContentForChatMessage = stripMarkers(errorMessage)
      isStreamingText.value = false
      isLoadingQuestionText.value = false
      isSynthesizingAudio.value = false
      lastFetchedAudioBlob.value = null
    } finally {
      isLoadingQuestionText.value = false
      isSynthesizingAudio.value = false

      const contentForChat = finalContentForChatMessage || stripMarkers(revealedQuestionContent.value) || '抱歉，处理时发生未知错误。'

      const newChatMessage: ChatMessage = {
        role: 'interviewer',
        content: contentForChat,
        type: 'text',
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        isStreaming: false,
      }
      messages.value.push(newChatMessage)

      if (streamingInterviewerMessageInChat.value) {
        streamingInterviewerMessageInChat.value = null
      }
    }
    return questionFullContent
  }

  /**
   * 重置面试官的状态，清除当前问题、显示的文本和音频资源。
   */
  const resetInterviewerState = () => {
    interviewer.currentQuestion.content = ''
    revealedQuestionContent.value = ''
    isLoadingQuestionText.value = false
    isSynthesizingAudio.value = false
    if (interviewerAudioSrc.value) {
      URL.revokeObjectURL(interviewerAudioSrc.value)
      interviewerAudioSrc.value = null
    }
    lastFetchedAudioBlob.value = null
    clearTimeout(textStreamTimeoutId)
    isStreamingText.value = false
    isPlaybackBlockedByInteraction.value = false
  }

  /**
   * 手动触发音频播放，用于处理浏览器自动播放限制
   */
  const triggerManualAudioPlayback = async () => {
    if (!interviewer.currentQuestion.content) {
      console.warn('没有当前问题可播放音频。')
      isPlaybackBlockedByInteraction.value = false
      return
    }

    console.log('用户触发手动音频播放。解除阻止并尝试为当前问题处理音频。')
    isPlaybackBlockedByInteraction.value = false
    isLoadingQuestionText.value = false

    clearTimeout(textStreamTimeoutId)
    isStreamingText.value = false
    revealedQuestionContent.value = ''

    const questionFullContent = interviewer.currentQuestion.content
    let audioProcessedSuccessfully = false

    if (lastFetchedAudioBlob.value && interviewerAudioPlayer.value) {
      console.log('找到缓存的音频 blob。尝试直接播放。')
      isSynthesizingAudio.value = false

      try {
        const audioDuration = await playAudioAndGetDuration(lastFetchedAudioBlob.value)
        if (!isPlaybackBlockedByInteraction.value) {
          await startSimulatedTextStream(questionFullContent, audioDuration)
          audioProcessedSuccessfully = true
        } else {
          console.warn('即使在手动触发后，音频播放仍被阻止（缓存音频）。显示完整文本。')
          revealedQuestionContent.value = questionFullContent
        }
      } catch (playError: any) {
        console.warn(`手动触发的缓存音频播放处理失败: ${playError.message}。显示文本。清除缓存的 blob。`)
        revealedQuestionContent.value = questionFullContent
        isStreamingText.value = false
        isSynthesizingAudio.value = false
        lastFetchedAudioBlob.value = null
      }
    } else {
      console.log('没有缓存的音频 blob 或播放器不可用。获取新的 MiniMax TTS。')
      isSynthesizingAudio.value = true

      try {
        const newAudioBlob = await fetchMinimaxTTSAudio(questionFullContent)
        if (newAudioBlob && interviewerAudioPlayer.value) {
          lastFetchedAudioBlob.value = newAudioBlob
          isSynthesizingAudio.value = false

          const audioDuration = await playAudioAndGetDuration(newAudioBlob)
          if (!isPlaybackBlockedByInteraction.value) {
            await startSimulatedTextStream(questionFullContent, audioDuration)
            audioProcessedSuccessfully = true
          } else {
            console.warn('获取新的 MiniMax TTS 后音频播放被阻止。显示完整文本。')
            revealedQuestionContent.value = questionFullContent
            isStreamingText.value = false
          }
        } else {
          console.warn('手动触发期间 MiniMax TTS 失败或音频播放器不可用。显示完整文本。')
          lastFetchedAudioBlob.value = null
          isSynthesizingAudio.value = false
          revealedQuestionContent.value = questionFullContent
          isStreamingText.value = false
        }
      } catch (error) {
        console.error('手动触发中新的 MiniMax TTS 获取/播放时出错:', error)
        lastFetchedAudioBlob.value = null
        isSynthesizingAudio.value = false
        revealedQuestionContent.value = questionFullContent
        isStreamingText.value = false
      }
    }

    if (!audioProcessedSuccessfully) {
      isStreamingText.value = false
      if (!revealedQuestionContent.value.trim() && questionFullContent) {
        revealedQuestionContent.value = questionFullContent
      }
    }

    if (isSynthesizingAudio.value && !audioProcessedSuccessfully) {
      isSynthesizingAudio.value = false
    }

    if (audioProcessedSuccessfully) {
      console.log('手动音频播放和流式传输成功启动。')
    } else {
      console.warn('手动音频播放/流尝试完成；查看日志。如果音频失败，应显示完整文本。')
    }
  }

  /**
   * 更新 MiniMax TTS 配置选项
   * @param newOptions 新的 TTS 配置选项
   */
  const updateTTSOptions = (newOptions: Partial<typeof ttsOptions>) => {
    Object.assign(ttsOptions, newOptions)
  }

  return {
    interviewer,
    isLoadingQuestionText,
    isSynthesizingAudio,
    isStreamingText,
    revealedQuestionContent,
    displayQuestionContent: revealedQuestionContent,
    handleQuestionCycle,
    getInitialQuestion,
    getNextQuestion,
    setInterviewerName: (name: string) => {
      interviewer.name = name
    },
    setInterviewerAvatar: (avatarUrl: string = INTERVIEWER_AVATAR_DEFAULT) => {
      interviewer.avatarUrl = avatarUrl
    },
    resetInterviewerState,
    isPlaybackBlockedByInteraction,
    triggerManualAudioPlayback,
    updateTTSOptions,
    // 暴露 MiniMax 特有的功能
    fetchMinimaxTTSAudio,
    ttsOptions: readonly(ttsOptions),
  }
}
