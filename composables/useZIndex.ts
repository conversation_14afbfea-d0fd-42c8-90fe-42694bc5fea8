import { useZIndexStore } from '@/stores/zindex'

export function useZIndex(group = 'default', baseLevel = 1000) {
  const idx = ref(useZIndexStore().getNextIndex(group, baseLevel))
  return {
    index: idx,
    floatTop() {
      idx.value = useZIndexStore().getNextIndex(group, baseLevel)
    },
    resetBase() {
      idx.value = baseLevel
    },
  }
}

export enum ZIndexPriorityGroup {
  PricingModal = 5000,
  RebrandingGiftModal = 6000,
  PaymentSuccessModal = 7000,
}
