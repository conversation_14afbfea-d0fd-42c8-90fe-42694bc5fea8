import { useInterviewQuestion, type InterviewQuestionData } from '@/composables/useInterviewQuestion'
import { stripMarkers } from '@/utils/textUtils'

const INTERVIEWER_AVATAR_DEFAULT = '/images/interviewer.png'
export type ChatMessage = {
  role: 'user' | 'assistant' | 'interviewer'
  content: string
  type: 'text' | 'voice'
  time: string
  audioDuration?: number
  isStreaming?: boolean
  id?: string | number
  [key: string]: any
}

const MIN_SPEECH_TEXT_SYNC_INTERVAL_MS = 10

export type UseInterviewerAgentOptions = {
  /** 用于播放面试官音频的 HTMLAudioElement 引用。 */
  interviewerAudioPlayer: Ref<HTMLAudioElement | null>
  /** 聊天消息数组的引用。 */
  messages: Ref<ChatMessage[]>
  /** 当前正在流式传输的面试官消息的引用。 */
  streamingInterviewerMessageInChat: Ref<ChatMessage | null>
  /** 聊天消息历史记录容器的 HTML 元素引用，用于滚动控制。 */
  messageHistoryRef: Ref<HTMLElement | null>
  /** 面试官的初始名称。 */
  interviewerInitialName?: string
  /** 面试官的初始头像 URL。 */
  interviewerInitialAvatar?: string
}

/**
 * 管理面试官代理的核心逻辑，包括获取问题、文本转语音 (TTS) 和消息流。
 * @param options - {@link UseInterviewerAgentOptions}
 */
export function useInterviewerAgent(options: UseInterviewerAgentOptions) {
  const {
    interviewerAudioPlayer,
    messages,
    streamingInterviewerMessageInChat,
    messageHistoryRef,
    interviewerInitialName = '面试官',
    interviewerInitialAvatar = INTERVIEWER_AVATAR_DEFAULT,
  } = options

  const { error: errorQuestion, getInitialQuestion, getNextQuestion } = useInterviewQuestion()

  const isLoadingQuestionText = ref(false)
  const isSynthesizingAudio = ref(false)
  const interviewerAudioSrc = ref<string | null>(null)
  const revealedQuestionContent = ref('')
  const isStreamingText = ref(false)
  const lastFetchedAudioBlob = ref<Blob | null>(null)

  const isPlaybackBlockedByInteraction = ref(false)

  const interviewer = reactive<{
    name: string
    avatarUrl: string
    currentQuestion: { content: string }
  }>({
    name: interviewerInitialName,
    avatarUrl: interviewerInitialAvatar,
    currentQuestion: { content: '' },
  })

  const fetchTTSAudio = async (text: string): Promise<Blob | null> => {
    try {
      const response = await fetch('/api/tts/cosyvoice', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ texts: text, voice: 'longcheng_v2', model: 'cosyvoice-v2' }),
      })
      if (!response.ok) {
        console.error('TTS API 请求失败:', response.status, await response.text())
        return null
      }
      return response.blob()
    } catch (error) {
      console.error('获取 TTS 音频失败:', error)
      return null
    }
  }

  /**
   * 播放提供的音频 Blob 并返回其时长（秒）。
   * @param audioBlob - 要播放的音频 Blob。
   * @returns Promise，解析为音频时长（秒）。
   */
  const playAudioAndGetDuration = (audioBlob: Blob): Promise<number> => {
    return new Promise((resolve, reject) => {
      if (interviewerAudioPlayer.value) {
        if (interviewerAudioSrc.value) {
          URL.revokeObjectURL(interviewerAudioSrc.value)
          interviewerAudioSrc.value = null
        }

        const objectURL = URL.createObjectURL(audioBlob)
        interviewerAudioSrc.value = objectURL
        const audioPlayerElement = interviewerAudioPlayer.value
        audioPlayerElement.src = objectURL

        const cleanupListeners = () => {
          audioPlayerElement.removeEventListener('loadedmetadata', onLoadedMetadataWithPlay)
          audioPlayerElement.removeEventListener('error', onErrorLoadingMetadata)
        }

        const onLoadedMetadataWithPlay = () => {
          const duration = audioPlayerElement.duration
          audioPlayerElement
            .play()
            .then(() => {
              isPlaybackBlockedByInteraction.value = false
              resolve(duration)
            })
            .catch(playError => {
              if (playError.name === 'NotAllowedError') {
                console.warn('音频自动播放失败：用户需要首先与文档交互。查看 https://goo.gl/xX8pDD')
                isPlaybackBlockedByInteraction.value = true
                reject(playError)
              } else {
                console.error('音频播放时发生错误:', playError)
                reject(playError)
              }
            })
            .finally(() => {
              cleanupListeners()
            })
        }

        const onErrorLoadingMetadata = (e: Event) => {
          console.error('加载音频元数据时出错:', e)
          reject(new Error('检查音频时长时加载音频元数据失败。'))
          cleanupListeners()
        }

        audioPlayerElement.addEventListener('loadedmetadata', onLoadedMetadataWithPlay)
        audioPlayerElement.addEventListener('error', onErrorLoadingMetadata)
        audioPlayerElement.load()
      } else {
        reject(new Error('未找到音频播放器元素。'))
      }
    })
  }

  let textStreamTimeoutId: number | undefined
  /**
   * 模拟文本流式显示效果，同步文本显示与音频播放。
   * 标记符号（如 **）会立即显示，其包裹的内容会逐字延迟显示。
   * @param fullText - 要流式显示的完整文本，可能包含标记。
   * @param audioDurationInSeconds - 音频的总时长（秒），对应于去除标记后的文本。
   * @returns Promise，在文本流完成时解析。
   */
  const startSimulatedTextStream = (fullText: string, audioDurationInSeconds: number): Promise<void> => {
    isStreamingText.value = true
    return new Promise(resolve => {
      clearTimeout(textStreamTimeoutId)
      revealedQuestionContent.value = ''

      const textForTTS = stripMarkers(fullText)
      const numCharsInTTS = textForTTS.length

      if (numCharsInTTS === 0 || audioDurationInSeconds <= 0 || audioDurationInSeconds < 0.5 || numCharsInTTS < 10) {
        revealedQuestionContent.value = fullText
        isStreamingText.value = false
        resolve()
        return
      }

      const avgTimePerSpokenChar = Math.max(MIN_SPEECH_TEXT_SYNC_INTERVAL_MS, (audioDurationInSeconds * 1000) / numCharsInTTS)
      let currentFullTextIndex = 0
      const marker = '**'

      const processNext = () => {
        if (currentFullTextIndex >= fullText.length) {
          if (revealedQuestionContent.value !== fullText) {
            revealedQuestionContent.value = fullText
          }
          isStreamingText.value = false
          resolve()
          return
        }

        let nextDelay: number

        if (fullText.startsWith(marker, currentFullTextIndex)) {
          revealedQuestionContent.value += marker
          currentFullTextIndex += marker.length
          nextDelay = MIN_SPEECH_TEXT_SYNC_INTERVAL_MS
        } else {
          revealedQuestionContent.value += fullText[currentFullTextIndex]
          currentFullTextIndex++
          nextDelay = avgTimePerSpokenChar
        }
        nextDelay = Math.max(MIN_SPEECH_TEXT_SYNC_INTERVAL_MS, nextDelay)

        if (currentFullTextIndex < fullText.length) {
          textStreamTimeoutId = setTimeout(processNext, nextDelay) as unknown as number
        } else {
          if (revealedQuestionContent.value !== fullText) {
            revealedQuestionContent.value = fullText
          }
          isStreamingText.value = false
          resolve()
        }
      }
      processNext()
    })
  }

  /**
   * 处理一个完整的问题获取、TTS 和显示周期。
   * 不再接受 initialChatMessageText 参数。
   * @param questionFetcher - 一个异步函数，用于获取面试问题数据。
   * @returns Promise，解析为获取到的问题原始内容。
   */
  const handleQuestionCycle = async (questionFetcher: () => Promise<InterviewQuestionData>): Promise<string> => {
    revealedQuestionContent.value = ''
    if (interviewerAudioSrc.value) {
      URL.revokeObjectURL(interviewerAudioSrc.value)
      interviewerAudioSrc.value = null
    }
    lastFetchedAudioBlob.value = null
    clearTimeout(textStreamTimeoutId)
    isStreamingText.value = false

    isLoadingQuestionText.value = true
    isSynthesizingAudio.value = false

    if (streamingInterviewerMessageInChat.value) {
      streamingInterviewerMessageInChat.value = null
    }

    let questionFullContent = ''
    let finalContentForChatMessage = ''

    try {
      const questionData = await questionFetcher()
      questionFullContent = questionData.content
      interviewer.currentQuestion.content = questionFullContent

      isLoadingQuestionText.value = false

      if (isPlaybackBlockedByInteraction.value) {
        console.warn('Audio playback is blocked by interaction. Skipping TTS and audio playback for new question. Displaying text only.')
        revealedQuestionContent.value = questionFullContent
        finalContentForChatMessage = stripMarkers(questionFullContent)
        isStreamingText.value = false
        isSynthesizingAudio.value = false
      } else {
        isSynthesizingAudio.value = true
        const audioBlob = await fetchTTSAudio(stripMarkers(questionFullContent))
        lastFetchedAudioBlob.value = audioBlob

        if (audioBlob && interviewerAudioPlayer.value) {
          isSynthesizingAudio.value = false
          try {
            const audioDuration = await playAudioAndGetDuration(audioBlob)
            if (!isPlaybackBlockedByInteraction.value) {
              await startSimulatedTextStream(questionFullContent, audioDuration)
            } else {
              console.warn('Audio play was blocked by interaction during handleQuestionCycle. Text stream aborted, displaying full text.')
              revealedQuestionContent.value = questionFullContent
              isStreamingText.value = false
            }
            finalContentForChatMessage = stripMarkers(questionFullContent)
          } catch (playError: any) {
            console.warn(`Audio playback processing failed: ${playError.message}. Displaying text.`)
            revealedQuestionContent.value = questionFullContent
            finalContentForChatMessage = stripMarkers(questionFullContent)
            isStreamingText.value = false
            isSynthesizingAudio.value = false
          }
        } else {
          console.warn('TTS failed or audio player unavailable. Displaying full text.')
          isSynthesizingAudio.value = false
          revealedQuestionContent.value = questionFullContent
          finalContentForChatMessage = stripMarkers(questionFullContent)
          isStreamingText.value = false
        }
      }
    } catch (err: any) {
      console.error('获取面试问题失败:', errorQuestion.value || err)
      const errorMessage = (errorQuestion.value as any)?.message || err?.message || '抱歉，处理面试官回复时遇到错误。'
      revealedQuestionContent.value = errorMessage
      finalContentForChatMessage = stripMarkers(errorMessage)
      isStreamingText.value = false
      isLoadingQuestionText.value = false
      isSynthesizingAudio.value = false
      lastFetchedAudioBlob.value = null
    } finally {
      isLoadingQuestionText.value = false
      isSynthesizingAudio.value = false

      const contentForChat = finalContentForChatMessage || stripMarkers(revealedQuestionContent.value) || '抱歉，处理时发生未知错误。'

      const newChatMessage: ChatMessage = {
        role: 'interviewer',
        content: contentForChat,
        type: 'text',
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        isStreaming: false,
      }
      messages.value.push(newChatMessage)

      if (streamingInterviewerMessageInChat.value) {
        streamingInterviewerMessageInChat.value = null
      }
    }
    return questionFullContent
  }

  /**
   * 重置面试官的状态，清除当前问题、显示的文本和音频资源。
   */
  const resetInterviewerState = () => {
    interviewer.currentQuestion.content = ''
    revealedQuestionContent.value = ''
    isLoadingQuestionText.value = false
    isSynthesizingAudio.value = false
    if (interviewerAudioSrc.value) {
      URL.revokeObjectURL(interviewerAudioSrc.value)
      interviewerAudioSrc.value = null
    }
    lastFetchedAudioBlob.value = null
    clearTimeout(textStreamTimeoutId)
    isStreamingText.value = false
    isPlaybackBlockedByInteraction.value = false
  }

  const triggerManualAudioPlayback = async () => {
    if (!interviewer.currentQuestion.content) {
      console.warn('No current question to play audio for.')
      isPlaybackBlockedByInteraction.value = false
      return
    }

    console.log('User triggered manual audio playback. Unblocking and attempting to process audio for current question.')
    isPlaybackBlockedByInteraction.value = false
    isLoadingQuestionText.value = false

    clearTimeout(textStreamTimeoutId)
    isStreamingText.value = false
    revealedQuestionContent.value = ''

    const questionFullContent = interviewer.currentQuestion.content
    let audioProcessedSuccessfully = false

    if (lastFetchedAudioBlob.value && interviewerAudioPlayer.value) {
      console.log('Found cached audio blob. Attempting to play directly.')
      isSynthesizingAudio.value = false

      try {
        const audioDuration = await playAudioAndGetDuration(lastFetchedAudioBlob.value)
        if (!isPlaybackBlockedByInteraction.value) {
          await startSimulatedTextStream(questionFullContent, audioDuration)
          audioProcessedSuccessfully = true
        } else {
          console.warn('Audio play was blocked again even after manual trigger (cached audio). Displaying full text.')
          revealedQuestionContent.value = questionFullContent
        }
      } catch (playError: any) {
        console.warn(`Manually triggered cached audio playback processing failed: ${playError.message}. Displaying text. Clearing cached blob.`)
        revealedQuestionContent.value = questionFullContent
        isStreamingText.value = false
        isSynthesizingAudio.value = false
        lastFetchedAudioBlob.value = null
      }
    } else {
      console.log('No cached audio blob or player unavailable. Fetching new TTS.')
      isSynthesizingAudio.value = true

      try {
        const newAudioBlob = await fetchTTSAudio(stripMarkers(questionFullContent))
        if (newAudioBlob && interviewerAudioPlayer.value) {
          lastFetchedAudioBlob.value = newAudioBlob
          isSynthesizingAudio.value = false

          const audioDuration = await playAudioAndGetDuration(newAudioBlob)
          if (!isPlaybackBlockedByInteraction.value) {
            await startSimulatedTextStream(questionFullContent, audioDuration)
            audioProcessedSuccessfully = true
          } else {
            console.warn('Audio play was blocked after fetching new TTS. Displaying full text.')
            revealedQuestionContent.value = questionFullContent
            isStreamingText.value = false
          }
        } else {
          console.warn('TTS failed or audio player unavailable during manual trigger. Displaying full text.')
          lastFetchedAudioBlob.value = null
          isSynthesizingAudio.value = false
          revealedQuestionContent.value = questionFullContent
          isStreamingText.value = false
        }
      } catch (error) {
        console.error('Error during new TTS fetch/playback in manual trigger:', error)
        lastFetchedAudioBlob.value = null
        isSynthesizingAudio.value = false
        revealedQuestionContent.value = questionFullContent
        isStreamingText.value = false
      }
    }

    if (!audioProcessedSuccessfully) {
      isStreamingText.value = false
      if (!revealedQuestionContent.value.trim() && questionFullContent) {
        revealedQuestionContent.value = questionFullContent
      }
    }

    if (isSynthesizingAudio.value && !audioProcessedSuccessfully) {
      isSynthesizingAudio.value = false
    }

    if (audioProcessedSuccessfully) {
      console.log('Manual audio playback and streaming initiated successfully.')
    } else {
      console.warn('Manual audio playback/stream attempt completed; review logs. Full text should be visible if audio failed.')
    }
  }

  return {
    interviewer,
    isLoadingQuestionText,
    isSynthesizingAudio,
    isStreamingText,
    revealedQuestionContent,
    displayQuestionContent: revealedQuestionContent,
    handleQuestionCycle,
    getInitialQuestion,
    getNextQuestion,
    setInterviewerName: (name: string) => {
      interviewer.name = name
    },
    setInterviewerAvatar: (avatarUrl: string) => {
      interviewer.avatarUrl = avatarUrl
    },
    resetInterviewerState,
    isPlaybackBlockedByInteraction,
    triggerManualAudioPlayback,
  }
}
