import { onMounted, onUnmounted, ref } from 'vue'

type ApiResponse = {
  success: boolean
  error: boolean
  errorCode: number
  errorMessage: string
  data: {
    remainingCredits: number
  }
}

export const useCredit = () => {
  const remainingCredits = ref(0)
  let pollingInterval: NodeJS.Timeout | null = null

  const fetchCreditUsage = async () => {
    try {
      const { data } = await useFetch<ApiResponse>('/api/user/quota-usage', {
        method: 'POST',
      })
      if (!data.value?.success || !data.value?.data?.remainingCredits) {
        return
      }
      remainingCredits.value = data.value.data.remainingCredits
    } catch (error) {
      console.error('Failed to fetch credit usage:', error)
    }
  }

  const startPolling = () => {
    // 立即执行一次
    fetchCreditUsage()
    // 设置轮询
    pollingInterval = setInterval(fetchCreditUsage, 3000)
  }

  const stopPolling = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval)
      pollingInterval = null
    }
  }

  onMounted(() => {
    startPolling()
  })

  onUnmounted(() => {
    stopPolling()
  })

  return {
    remainingCredits,
  }
}
