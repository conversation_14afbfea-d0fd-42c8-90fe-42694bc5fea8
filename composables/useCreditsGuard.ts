import { useCredit } from './useCredit'
import { useEvent } from './useEventBus'

type CreditsGuardCallback<T = void> = () => T | Promise<T>

export const useCreditsGuard = () => {
  const { remainingCredits } = useCredit()
  const { emit: showCreditsNotification } = useEvent('showCreditsNotification')

  /**
   * 检查用户credits并执行相应操作
   * @param callback 当credits足够时执行的回调函数
   * @param requiredCredits 需要的credits数量，默认为1
   * @returns Promise 返回回调函数的结果，若积分不足则返回false
   */
  const checkCreditsAndExecute = async <T = void>(callback: CreditsGuardCallback<T>, requiredCredits: number = 1): Promise<T | false> => {
    if (remainingCredits.value < requiredCredits) {
      showCreditsNotification()
      return false
    }

    try {
      return await callback()
    } catch (error) {
      console.error('执行回调函数时出错:', error)
      throw error
    }
  }

  /**
   * 简单的credits检查，不执行回调
   * @param requiredCredits 需要的credits数量，默认为1
   * @returns boolean 是否有足够credits
   */
  const hasEnoughCredits = (requiredCredits: number = 1): boolean => {
    return remainingCredits.value >= requiredCredits
  }

  return {
    remainingCredits,
    checkCreditsAndExecute,
    hasEnoughCredits,
  }
}
