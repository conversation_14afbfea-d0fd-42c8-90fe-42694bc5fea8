import { usePrepareInterviewStore } from '~/stores/prepare-interview'
import type { InterviewStage, InterviewType } from '~/types/interview'
import { useStreamedContentProcessor, type ProcessedResult } from './useStreamedContentProcessor'

type ChatMessageForApi = {
  role: 'user' | 'assistant' | 'interviewer'
  content: string
}

export type InterviewQuestionData = ProcessedResult<string>

type InitialQuestionBody = {
  scenario: 'initial'
  interviewType: InterviewType
  resumeText: string
  jobInfo: string
  majorQuestions?: string
}

type NextQuestionBody = {
  scenario: 'followup'
  interviewType: InterviewType
  messages: ChatMessageForApi[]
  stage: InterviewStage
  resumeText: string
  jobInfo: string
  majorQuestions?: string
}

export const useInterviewQuestion = () => {
  const processor = useStreamedContentProcessor<InitialQuestionBody | NextQuestionBody, string>()
  const prepareInterviewStore = usePrepareInterviewStore()
  const { resumeContent: resumeText, positionDescription, companyDescription, majorQuestions = '' } = prepareInterviewStore.formData
  const jobInfo = `${positionDescription} ${companyDescription}`

  const getInitialQuestion = async (type: InterviewType): Promise<InterviewQuestionData> => {
    return processor.process({
      streamOptions: {
        url: '/api/interview/generate',
        body: {
          scenario: 'initial',
          interviewType: type,
          resumeText,
          jobInfo,
          majorQuestions,
        },
      },
      getEmptyContentOnError: () => '无法加载初始问题。',
      errorContextMessage: '获取初始面试问题',
    })
  }

  const getNextQuestion = async (currentMessages: ChatMessageForApi[], type: InterviewType, stage: InterviewStage): Promise<InterviewQuestionData> => {
    return processor.process({
      streamOptions: {
        url: '/api/interview/generate',
        body: {
          scenario: 'followup',
          interviewType: type,
          messages: currentMessages,
          stage,
          resumeText,
          jobInfo,
          majorQuestions,
        },
      },
      getEmptyContentOnError: () => '无法加载下一个问题。',
      errorContextMessage: '获取后续面试问题',
    })
  }

  return {
    isLoading: processor.isLoading,
    error: processor.error,
    getInitialQuestion,
    getNextQuestion,
    processedContent: processor.processedContent,
    isStreamProcessing: processor.isStreamProcessing,
    majorQuestions,
  }
}
