import { ref } from 'vue'
import { useAudioAnalyzer } from './useAudioAnalyzer'
import { useGlobalAudio } from './useGlobalAudio'

export const useVoiceRecorder = () => {
  const isRecording = ref(false)
  const audioBlob = ref<Blob | null>(null)
  const transcribedText = ref('')
  const isTranscribing = ref(false)
  const transcribeError = ref('')
  const isWaitingResponse = ref(false)
  const recordingDuration = ref(0)
  let recordingTimer: ReturnType<typeof setInterval> | null = null

  const { audioData, startAnalyzing, stopAnalyzing } = useAudioAnalyzer()
  const globalAudio = useGlobalAudio()
  let mediaRecorder: MediaRecorder | null = null
  let currentStream: MediaStream | null = null

  const startRecording = async () => {
    if (isWaitingResponse.value) return
    try {
      // 停止当前正在播放的音频
      globalAudio.stopCurrentAudio()

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      currentStream = stream

      // 开始音频分析
      startAnalyzing(stream)

      // 重置并开始计时
      recordingDuration.value = 0
      recordingTimer = setInterval(() => {
        recordingDuration.value++
      }, 1000)

      // 设置录音
      mediaRecorder = new MediaRecorder(stream)
      const chunks: BlobPart[] = []

      mediaRecorder.ondataavailable = e => {
        chunks.push(e.data)
      }

      mediaRecorder.onstop = () => {
        audioBlob.value = new Blob(chunks, { type: 'audio/webm' })
        if (currentStream) {
          currentStream.getTracks().forEach(track => track.stop())
          currentStream = null
        }
        if (recordingTimer) {
          clearInterval(recordingTimer)
          recordingTimer = null
        }
        stopAnalyzing()
      }

      mediaRecorder.start()
      isRecording.value = true
    } catch (error) {
      console.error('Error starting recording:', error)
    }
  }

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state !== 'inactive') {
      mediaRecorder.stop()
      isRecording.value = false
    }
  }

  const transcribe = async () => {
    if (!audioBlob.value) {
      transcribeError.value = '没有可用的录音'
      return
    }

    isTranscribing.value = true
    isWaitingResponse.value = true
    transcribeError.value = ''

    try {
      const formData = new FormData()
      formData.append('file', audioBlob.value)

      const response = await fetch('/api/audio/transcribe', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('转写请求失败')
      }

      const data = await response.json()
      transcribedText.value = data.text
    } catch (error: any) {
      console.error('转写错误:', error)
      transcribeError.value = error.message || '转写失败'
    } finally {
      isTranscribing.value = false
      isWaitingResponse.value = false
    }
  }

  return {
    isRecording,
    audioBlob,
    audioData,
    transcribedText,
    isTranscribing,
    transcribeError,
    isWaitingResponse,
    recordingDuration,
    startRecording,
    stopRecording,
    transcribe,
  }
}
