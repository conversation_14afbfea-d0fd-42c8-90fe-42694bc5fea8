import { usePrepareInterviewStore } from '~/stores/prepare-interview'
import type { InterviewType } from '~/types/interview'
import { useStreamedContentProcessor, type ProcessedResult } from './useStreamedContentProcessor'

type ApiMessage = {
  role: 'user' | 'assistant' | 'interviewer'
  content: string
}

export type SuggestionData = ProcessedResult<string[]>

type SuggestionBody = {
  scenario: 'suggestion'
  messages: ApiMessage[] | undefined
  currentQuestionContent: string
  interviewType: InterviewType
  resumeText: string
  jobInfo: string
}

export const useInterviewSuggestion = () => {
  const processor = useStreamedContentProcessor<SuggestionBody, string[]>()

  const prepareInterviewStore = usePrepareInterviewStore()
  const { resumeContent: resumeText, positionDescription, companyDescription } = prepareInterviewStore.formData
  const jobInfo = `${positionDescription} ${companyDescription}`

  const getSuggestion = async (messages: ApiMessage[] | undefined, currentQuestionContent: string, interviewType: InterviewType): Promise<SuggestionData> => {
    return processor.process({
      streamOptions: {
        url: '/api/interview/generate',
        body: {
          scenario: 'suggestion',
          messages,
          currentQuestionContent,
          interviewType,
          resumeText,
          jobInfo,
        },
      },
      transformAccumulatedText: (text: string): string[] => {
        return text.trim()
          ? text
              .split('\n\n')
              .map(p => p.trim())
              .filter(p => p)
          : []
      },
      getEmptyContentOnError: () => [],
      errorContextMessage: '获取建议内容',
    })
  }

  return {
    isLoading: processor.isLoading,
    error: processor.error,
    getSuggestion,
    processedContent: processor.processedContent,
    isStreamProcessing: processor.isStreamProcessing,
  }
}
