import { ref, onUnmounted } from 'vue'

// 诊断分析阶段的动态提示消息
const DIAGNOSIS_MESSAGES = [
  { title: '初始化分析引擎...', subtitle: '正在启动AI简历分析系统' },
  { title: '解析简历结构...', subtitle: '识别简历各个模块和关键信息' },
  { title: '匹配职位要求...', subtitle: '分析简历与目标职位的匹配度' },
  { title: '评估技能匹配...', subtitle: '检测技能关键词和相关性' },
  { title: '识别改进点...', subtitle: '发现简历中需要优化的地方' },
  { title: '生成专业建议...', subtitle: '基于分析结果制定改进方案' },
  { title: '完善诊断报告...', subtitle: '整理分析结果和优化建议' }
]

// 简历生成阶段的动态提示消息
const GENERATION_MESSAGES = [
  { title: '准备优化引擎...', subtitle: '初始化简历优化系统' },
  { title: '应用诊断建议...', subtitle: '根据分析结果优化简历内容' },
  { title: '优化技能表述...', subtitle: '提升技能描述的专业性' },
  { title: '增强经验描述...', subtitle: '丰富工作经验的表达方式' },
  { title: '调整格式结构...', subtitle: '优化简历的整体布局和结构' },
  { title: '润色语言表达...', subtitle: '提升简历的语言质量和表达力' },
  { title: '最终质量检查...', subtitle: '确保优化后简历的完整性和准确性' }
]

export const useDiagnosisStatusMessages = () => {
  const currentMessageIndex = ref(0)
  const currentMessage = ref(DIAGNOSIS_MESSAGES[0])
  let messageTimer: NodeJS.Timeout | null = null

  // 开始诊断分析的消息轮播
  const startDiagnosisMessages = () => {
    currentMessageIndex.value = 0
    currentMessage.value = DIAGNOSIS_MESSAGES[0]
    
    messageTimer = setInterval(() => {
      currentMessageIndex.value = (currentMessageIndex.value + 1) % DIAGNOSIS_MESSAGES.length
      currentMessage.value = DIAGNOSIS_MESSAGES[currentMessageIndex.value]
    }, 2000) // 每2秒切换一次消息
  }

  // 开始简历生成的消息轮播
  const startGenerationMessages = () => {
    currentMessageIndex.value = 0
    currentMessage.value = GENERATION_MESSAGES[0]
    
    messageTimer = setInterval(() => {
      currentMessageIndex.value = (currentMessageIndex.value + 1) % GENERATION_MESSAGES.length
      currentMessage.value = GENERATION_MESSAGES[currentMessageIndex.value]
    }, 2000) // 每2秒切换一次消息
  }

  // 停止消息轮播
  const stopMessages = () => {
    if (messageTimer) {
      clearInterval(messageTimer)
      messageTimer = null
    }
  }

  // 根据当前阶段获取适当的消息
  const startMessagesForStage = (stage: 'stage-one' | 'stage-two') => {
    stopMessages() // 先停止之前的计时器
    if (stage === 'stage-one') {
      startDiagnosisMessages()
    } else if (stage === 'stage-two') {
      startGenerationMessages()
    }
  }

  // 清理定时器
  onUnmounted(() => {
    stopMessages()
  })

  return {
    currentMessage,
    startMessagesForStage,
    stopMessages,
    startDiagnosisMessages,
    startGenerationMessages
  }
} 