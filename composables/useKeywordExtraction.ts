import { ref } from 'vue'

type KeywordApiResponseData = {
  keywords: string[]
}

type KeywordApiResponse = {
  data: KeywordApiResponseData
}

export const useKeywordExtraction = () => {
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const extractKeywords = async (text: string): Promise<string[]> => {
    if (!text || text.trim() === '') {
      return []
    }
    isLoading.value = true
    error.value = null
    try {
      const response = await $fetch<KeywordApiResponse>('/api/interview/extract-keywords', {
        method: 'POST',
        body: { text },
      })
      return response.data.keywords || []
    } catch (err: any) {
      console.error('关键词提取失败:', err)
      error.value = err.data?.message || '提取关键词时发生未知错误。'
      return []
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    error,
    extractKeywords,
  }
}
