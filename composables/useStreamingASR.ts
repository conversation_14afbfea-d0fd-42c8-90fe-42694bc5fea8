/**
 * 流式语音识别 Composable
 * 支持实时显示识别结果和动态修正效果
 */

export type StreamEventData = {
  type: 'connected' | 'progress' | 'result' | 'complete' | 'error'
  data?: {
    text?: string
    isPartial?: boolean
    confidence?: number
    timestamp?: number
    audioInfo?: {
      fileName?: string
      fileType?: string
      fileSize?: number
      encoding?: string
      sampleRate?: number
    }
    error?: string
  }
}

export const useStreamingASR = () => {
  // 状态管理
  const isRecognizing = ref(false)
  const currentText = ref('')
  const finalText = ref('')
  const error = ref('')
  const progress = ref(0)
  const audioInfo = ref<{
    fileName?: string
    fileType?: string
    fileSize?: number
    encoding?: string
    sampleRate?: number
  } | null>(null)
  
  // 识别历史记录
  const recognitionHistory = ref<Array<{
    timestamp: number
    text: string
    isPartial: boolean
  }>>([])

  // EventSource 实例
  let eventSource: EventSource | null = null

  /**
   * 开始流式语音识别
   * @param audioFile 音频文件
   * @param options 识别选项
   */
  const startRecognition = async (
    audioFile: File,
    options: {
      onProgress?: (text: string, isPartial: boolean) => void
      onComplete?: (text: string) => void
      onError?: (error: string) => void
    } = {}
  ) => {
    try {
      // 重置状态
      isRecognizing.value = true
      currentText.value = ''
      finalText.value = ''
      error.value = ''
      progress.value = 0
      audioInfo.value = null
      recognitionHistory.value = []

      // 准备表单数据
      const formData = new FormData()
      formData.append('audio', audioFile)

      // 发送请求并建立 SSE 连接
      const response = await fetch('/api/asr/xunfei-stream', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // 创建 EventSource 来接收流式数据
      // 注意：这里我们需要使用一个特殊的方式来处理 POST 请求的 SSE
      // 因为 EventSource 只支持 GET 请求，我们需要通过 fetch 的 ReadableStream 来处理
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      // 读取流式数据
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          break
        }

        // 解码数据
        buffer += decoder.decode(value, { stream: true })
        
        // 处理完整的事件
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // 保留不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const eventData: StreamEventData = JSON.parse(line.slice(6))
              await handleStreamEvent(eventData, options)
            } catch (parseError) {
              console.error('解析 SSE 数据失败:', parseError)
            }
          }
        }
      }

    } catch (err: any) {
      console.error('流式识别失败:', err)
      error.value = err.message || '识别失败'
      options.onError?.(error.value)
    } finally {
      isRecognizing.value = false
    }
  }

  /**
   * 处理流式事件
   */
  const handleStreamEvent = async (
    eventData: StreamEventData,
    options: {
      onProgress?: (text: string, isPartial: boolean) => void
      onComplete?: (text: string) => void
      onError?: (error: string) => void
    }
  ) => {


    switch (eventData.type) {
      case 'connected':
        // 连接确认
        break

      case 'progress':
        // 初始化进度
        if (eventData.data?.audioInfo) {
          audioInfo.value = eventData.data.audioInfo
        }
        progress.value = 10 // 开始识别
        break

      case 'result':
        // 中间识别结果（动态修正）
        if (eventData.data?.text !== undefined) {
          currentText.value = eventData.data.text

          // 添加到历史记录
          recognitionHistory.value.push({
            timestamp: eventData.data.timestamp || Date.now(),
            text: eventData.data.text,
            isPartial: eventData.data.isPartial ?? true
          })

          // 触发进度回调
          options.onProgress?.(eventData.data.text, eventData.data.isPartial ?? true)
        }
        progress.value = 70 // 识别中
        break

      case 'complete':
        // 最终识别结果
        if (eventData.data?.text !== undefined) {
          finalText.value = eventData.data.text
          currentText.value = eventData.data.text

          // 添加到历史记录
          recognitionHistory.value.push({
            timestamp: eventData.data.timestamp || Date.now(),
            text: eventData.data.text,
            isPartial: false
          })

          // 触发完成回调
          options.onComplete?.(eventData.data.text)
        }
        progress.value = 100 // 完成
        isRecognizing.value = false
        break

      case 'error':
        // 错误处理
        error.value = eventData.data?.error || '未知错误'
        options.onError?.(error.value)
        isRecognizing.value = false
        progress.value = 0
        break
    }
  }

  /**
   * 停止识别
   */
  const stopRecognition = () => {
    if (eventSource) {
      eventSource.close()
      eventSource = null
    }
    isRecognizing.value = false
  }

  /**
   * 清除结果
   */
  const clearResults = () => {
    currentText.value = ''
    finalText.value = ''
    error.value = ''
    progress.value = 0
    audioInfo.value = null
    recognitionHistory.value = []
  }

  /**
   * 获取识别进度文本
   */
  const progressText = computed(() => {
    if (progress.value === 0) return '准备中...'
    if (progress.value < 50) return '连接中...'
    if (progress.value < 100) return '识别中...'
    return '完成'
  })

  /**
   * 获取当前显示的文本（带动态效果）
   */
  const displayText = computed(() => {
    if (finalText.value) return finalText.value
    if (currentText.value) return currentText.value
    return ''
  })

  // 清理函数
  onUnmounted(() => {
    stopRecognition()
  })

  return {
    // 状态
    isRecognizing: readonly(isRecognizing),
    currentText: readonly(currentText),
    finalText: readonly(finalText),
    error: readonly(error),
    progress: readonly(progress),
    audioInfo: readonly(audioInfo),
    recognitionHistory: readonly(recognitionHistory),
    
    // 计算属性
    progressText,
    displayText,
    
    // 方法
    startRecognition,
    stopRecognition,
    clearResults,
  }
}
