import { ref } from 'vue'

const currentPlayingAudio = ref<{ audio: HTMLAudioElement; id: string } | null>(null)

export const useGlobalAudio = () => {
  const stopCurrentAudio = () => {
    if (currentPlayingAudio.value) {
      currentPlayingAudio.value.audio.pause()
      currentPlayingAudio.value.audio.currentTime = 0
      currentPlayingAudio.value = null
    }
  }

  const setCurrentAudio = (audio: HTMLAudioElement, id: string) => {
    stopCurrentAudio()
    audio.currentTime = 0
    currentPlayingAudio.value = { audio, id }
  }

  return {
    currentPlayingAudio,
    stopCurrentAudio,
    setCurrentAudio,
  }
}
