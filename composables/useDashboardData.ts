import { ref } from 'vue'
import type { Interview } from '../types/interview'

export interface DashboardStats {
  totalInterviews: number
  todayInterviews: number
  averageScore: number
  positionDistribution: {
    name: string
    percentage: number
    color: string
  }[]
  scoresTrend: {
    labels: string[]
    data: number[]
  }
}

interface ApiResponse<T> {
  success: boolean
  data: T
  message: string
}

interface InterviewListResponse {
  list: Interview[]
  pagination: {
    page: number
    limit: number
    total: number
  }
}

export const useDashboardData = () => {
  const recentInterviews = ref<Interview[]>([])
  const stats = ref<DashboardStats>({
    totalInterviews: 0,
    todayInterviews: 0,
    averageScore: 0,
    positionDistribution: [],
    scoresTrend: {
      labels: [],
      data: [],
    },
  })
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const fetchDashboardData = async () => {
    isLoading.value = true
    error.value = null

    try {
      // 获取最近的面试记录和统计数据
      const [interviewsData, statsData] = await Promise.all([
        $fetch<ApiResponse<InterviewListResponse>>('/api/interview/list', {
          query: {
            page: 1,
            limit: 5,
          },
        }),
        $fetch<ApiResponse<DashboardStats>>('/api/interview/stats'),
      ])

      if (interviewsData.success) {
        recentInterviews.value = interviewsData.data.list
      }

      if (statsData.success) {
        stats.value = statsData.data
      }
    } catch (e) {
      error.value = 'get dashboard data failed'
      logger.error('get dashboard data failed:', e)
    } finally {
      isLoading.value = false
    }
  }

  return {
    recentInterviews,
    stats,
    isLoading,
    error,
    fetchDashboardData,
  }
}
