import type { InterviewStage } from '~/server/prompt-service/interview'

interface StageManagerResponse {
  data: {
    result: {
      shouldTransition: boolean
      nextStage: string
      reason: string
    }
    message: string
  }
}

export const useStageManager = () => {
  const checkStageTransition = async (messages: { role: string; content: string }[], currentStage: InterviewStage) => {
    try {
      const response = await $fetch<StageManagerResponse>('/api/interview/stage-update', {
        method: 'POST',
        body: {
          messages,
          currentStage,
        },
      })
      return response.data.result
    } catch (error) {
      console.error('阶段检查错误:', error)
      return null
    }
  }

  return {
    checkStageTransition,
  }
}
