import { type Ref } from 'vue'

export type StreamProcessorOptions = {
  url: string
  method?: 'POST' | 'GET'
  body: Record<string, any>
  processedContentRef: Ref<string>
  isStreamProcessingRef: Ref<boolean>
}

export const useStreamProcessing = () => {
  const processTextStream = async ({ url, method = 'POST', body, processedContentRef, isStreamProcessingRef }: StreamProcessorOptions): Promise<string> => {
    isStreamProcessingRef.value = true
    processedContentRef.value = ''
    let accumulatedText = ''

    try {
      const stream = await $fetch<ReadableStream>(url, {
        method,
        body,
        responseType: 'stream',
      })

      const reader = stream.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) {
          break
        }
        const chunk = decoder.decode(value, { stream: true })
        accumulatedText += chunk
        processedContentRef.value = accumulatedText
      }
      return accumulatedText.trim()
    } catch (error) {
      throw error
    } finally {
      isStreamProcessingRef.value = false
    }
  }

  return {
    processTextStream,
  }
}
